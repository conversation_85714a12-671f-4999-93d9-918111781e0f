# Creating Your Open Graph Image

## What You Need to Do

Your website now has the proper Open Graph meta tags, but you need to create a proper image file for the best social media previews.

## Option 1: Use the HTML Generator (Recommended)

1. Open `public/og-image-generator.html` in your browser
2. Take a screenshot of the page (exactly 1200x630 pixels)
3. Save it as `public/og-image.jpg` or `public/og-image.png`
4. Update the meta tags in `index.html` to point to the new image

## Option 2: Use the SVG (Current Setup)

The SVG image is already created and linked in your meta tags. However, some platforms prefer PNG/JPG.

## Option 3: Create a Custom Image

Create a 1200x630 pixel image with:
- Your PixelVane branding
- Dark background matching your website
- Clear, readable text
- Your logo
- Key services/features

## Testing Your Open Graph Tags

After deploying, test your links on:
- Discord
- Twitter/X
- Facebook
- LinkedIn
- Slack

## Tools to Convert SVG to PNG

If you want to convert the SVG to PNG:
1. Use online tools like CloudConvert
2. Use design tools like Figma, Canva, or Photoshop
3. Use command line tools like ImageMagick

## Important Notes

- Image must be exactly 1200x630 pixels for best results
- File size should be under 8MB
- Use JPG or PNG format for maximum compatibility
- Make sure the image URL is publicly accessible
- Test the preview after deployment

## Current Meta Tags Added

✅ Open Graph tags for Facebook, Discord, etc.
✅ Twitter Card tags for Twitter/X
✅ Proper dimensions and descriptions
✅ Your website URL and branding
