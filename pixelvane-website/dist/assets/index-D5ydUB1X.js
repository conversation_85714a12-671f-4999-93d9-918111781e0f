(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))o(s);new MutationObserver(s=>{for(const a of s)if(a.type==="childList")for(const l of a.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&o(l)}).observe(document,{childList:!0,subtree:!0});function n(s){const a={};return s.integrity&&(a.integrity=s.integrity),s.referrerPolicy&&(a.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?a.credentials="include":s.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function o(s){if(s.ep)return;s.ep=!0;const a=n(s);fetch(s.href,a)}})();/**
 * @license
 * Copyright 2019 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Fe=globalThis,ht=Fe.ShadowRoot&&(Fe.ShadyCSS===void 0||Fe.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,mt=Symbol(),It=new WeakMap;let ao=class{constructor(e,n,o){if(this._$cssResult$=!0,o!==mt)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=e,this.t=n}get styleSheet(){let e=this.o;const n=this.t;if(ht&&e===void 0){const o=n!==void 0&&n.length===1;o&&(e=It.get(n)),e===void 0&&((this.o=e=new CSSStyleSheet).replaceSync(this.cssText),o&&It.set(n,e))}return e}toString(){return this.cssText}};const Xo=t=>new ao(typeof t=="string"?t:t+"",void 0,mt),ae=(t,...e)=>{const n=t.length===1?t[0]:e.reduce((o,s,a)=>o+(l=>{if(l._$cssResult$===!0)return l.cssText;if(typeof l=="number")return l;throw Error("Value passed to 'css' function must be a 'css' function result: "+l+". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.")})(s)+t[a+1],t[0]);return new ao(n,t,mt)},Qo=(t,e)=>{if(ht)t.adoptedStyleSheets=e.map(n=>n instanceof CSSStyleSheet?n:n.styleSheet);else for(const n of e){const o=document.createElement("style"),s=Fe.litNonce;s!==void 0&&o.setAttribute("nonce",s),o.textContent=n.cssText,t.appendChild(o)}},kt=ht?t=>t:t=>t instanceof CSSStyleSheet?(e=>{let n="";for(const o of e.cssRules)n+=o.cssText;return Xo(n)})(t):t;/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const{is:Zo,defineProperty:jo,getOwnPropertyDescriptor:ei,getOwnPropertyNames:ti,getOwnPropertySymbols:ni,getPrototypeOf:oi}=Object,j=globalThis,Rt=j.trustedTypes,ii=Rt?Rt.emptyScript:"",Xe=j.reactiveElementPolyfillSupport,we=(t,e)=>t,Ve={toAttribute(t,e){switch(e){case Boolean:t=t?ii:null;break;case Object:case Array:t=t==null?t:JSON.stringify(t)}return t},fromAttribute(t,e){let n=t;switch(e){case Boolean:n=t!==null;break;case Number:n=t===null?null:Number(t);break;case Object:case Array:try{n=JSON.parse(t)}catch{n=null}}return n}},gt=(t,e)=>!Zo(t,e),Dt={attribute:!0,type:String,converter:Ve,reflect:!1,useDefault:!1,hasChanged:gt};Symbol.metadata??(Symbol.metadata=Symbol("metadata")),j.litPropertyMetadata??(j.litPropertyMetadata=new WeakMap);let ce=class extends HTMLElement{static addInitializer(e){this._$Ei(),(this.l??(this.l=[])).push(e)}static get observedAttributes(){return this.finalize(),this._$Eh&&[...this._$Eh.keys()]}static createProperty(e,n=Dt){if(n.state&&(n.attribute=!1),this._$Ei(),this.prototype.hasOwnProperty(e)&&((n=Object.create(n)).wrapped=!0),this.elementProperties.set(e,n),!n.noAccessor){const o=Symbol(),s=this.getPropertyDescriptor(e,o,n);s!==void 0&&jo(this.prototype,e,s)}}static getPropertyDescriptor(e,n,o){const{get:s,set:a}=ei(this.prototype,e)??{get(){return this[n]},set(l){this[n]=l}};return{get:s,set(l){const c=s==null?void 0:s.call(this);a==null||a.call(this,l),this.requestUpdate(e,c,o)},configurable:!0,enumerable:!0}}static getPropertyOptions(e){return this.elementProperties.get(e)??Dt}static _$Ei(){if(this.hasOwnProperty(we("elementProperties")))return;const e=oi(this);e.finalize(),e.l!==void 0&&(this.l=[...e.l]),this.elementProperties=new Map(e.elementProperties)}static finalize(){if(this.hasOwnProperty(we("finalized")))return;if(this.finalized=!0,this._$Ei(),this.hasOwnProperty(we("properties"))){const n=this.properties,o=[...ti(n),...ni(n)];for(const s of o)this.createProperty(s,n[s])}const e=this[Symbol.metadata];if(e!==null){const n=litPropertyMetadata.get(e);if(n!==void 0)for(const[o,s]of n)this.elementProperties.set(o,s)}this._$Eh=new Map;for(const[n,o]of this.elementProperties){const s=this._$Eu(n,o);s!==void 0&&this._$Eh.set(s,n)}this.elementStyles=this.finalizeStyles(this.styles)}static finalizeStyles(e){const n=[];if(Array.isArray(e)){const o=new Set(e.flat(1/0).reverse());for(const s of o)n.unshift(kt(s))}else e!==void 0&&n.push(kt(e));return n}static _$Eu(e,n){const o=n.attribute;return o===!1?void 0:typeof o=="string"?o:typeof e=="string"?e.toLowerCase():void 0}constructor(){super(),this._$Ep=void 0,this.isUpdatePending=!1,this.hasUpdated=!1,this._$Em=null,this._$Ev()}_$Ev(){var e;this._$ES=new Promise(n=>this.enableUpdating=n),this._$AL=new Map,this._$E_(),this.requestUpdate(),(e=this.constructor.l)==null||e.forEach(n=>n(this))}addController(e){var n;(this._$EO??(this._$EO=new Set)).add(e),this.renderRoot!==void 0&&this.isConnected&&((n=e.hostConnected)==null||n.call(e))}removeController(e){var n;(n=this._$EO)==null||n.delete(e)}_$E_(){const e=new Map,n=this.constructor.elementProperties;for(const o of n.keys())this.hasOwnProperty(o)&&(e.set(o,this[o]),delete this[o]);e.size>0&&(this._$Ep=e)}createRenderRoot(){const e=this.shadowRoot??this.attachShadow(this.constructor.shadowRootOptions);return Qo(e,this.constructor.elementStyles),e}connectedCallback(){var e;this.renderRoot??(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),(e=this._$EO)==null||e.forEach(n=>{var o;return(o=n.hostConnected)==null?void 0:o.call(n)})}enableUpdating(e){}disconnectedCallback(){var e;(e=this._$EO)==null||e.forEach(n=>{var o;return(o=n.hostDisconnected)==null?void 0:o.call(n)})}attributeChangedCallback(e,n,o){this._$AK(e,o)}_$ET(e,n){var a;const o=this.constructor.elementProperties.get(e),s=this.constructor._$Eu(e,o);if(s!==void 0&&o.reflect===!0){const l=(((a=o.converter)==null?void 0:a.toAttribute)!==void 0?o.converter:Ve).toAttribute(n,o.type);this._$Em=e,l==null?this.removeAttribute(s):this.setAttribute(s,l),this._$Em=null}}_$AK(e,n){var a,l;const o=this.constructor,s=o._$Eh.get(e);if(s!==void 0&&this._$Em!==s){const c=o.getPropertyOptions(s),d=typeof c.converter=="function"?{fromAttribute:c.converter}:((a=c.converter)==null?void 0:a.fromAttribute)!==void 0?c.converter:Ve;this._$Em=s;const u=d.fromAttribute(n,c.type);this[s]=u??((l=this._$Ej)==null?void 0:l.get(s))??u,this._$Em=null}}requestUpdate(e,n,o){var s;if(e!==void 0){const a=this.constructor,l=this[e];if(o??(o=a.getPropertyOptions(e)),!((o.hasChanged??gt)(l,n)||o.useDefault&&o.reflect&&l===((s=this._$Ej)==null?void 0:s.get(e))&&!this.hasAttribute(a._$Eu(e,o))))return;this.C(e,n,o)}this.isUpdatePending===!1&&(this._$ES=this._$EP())}C(e,n,{useDefault:o,reflect:s,wrapped:a},l){o&&!(this._$Ej??(this._$Ej=new Map)).has(e)&&(this._$Ej.set(e,l??n??this[e]),a!==!0||l!==void 0)||(this._$AL.has(e)||(this.hasUpdated||o||(n=void 0),this._$AL.set(e,n)),s===!0&&this._$Em!==e&&(this._$Eq??(this._$Eq=new Set)).add(e))}async _$EP(){this.isUpdatePending=!0;try{await this._$ES}catch(n){Promise.reject(n)}const e=this.scheduleUpdate();return e!=null&&await e,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){var o;if(!this.isUpdatePending)return;if(!this.hasUpdated){if(this.renderRoot??(this.renderRoot=this.createRenderRoot()),this._$Ep){for(const[a,l]of this._$Ep)this[a]=l;this._$Ep=void 0}const s=this.constructor.elementProperties;if(s.size>0)for(const[a,l]of s){const{wrapped:c}=l,d=this[a];c!==!0||this._$AL.has(a)||d===void 0||this.C(a,void 0,l,d)}}let e=!1;const n=this._$AL;try{e=this.shouldUpdate(n),e?(this.willUpdate(n),(o=this._$EO)==null||o.forEach(s=>{var a;return(a=s.hostUpdate)==null?void 0:a.call(s)}),this.update(n)):this._$EM()}catch(s){throw e=!1,this._$EM(),s}e&&this._$AE(n)}willUpdate(e){}_$AE(e){var n;(n=this._$EO)==null||n.forEach(o=>{var s;return(s=o.hostUpdated)==null?void 0:s.call(o)}),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(e)),this.updated(e)}_$EM(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$ES}shouldUpdate(e){return!0}update(e){this._$Eq&&(this._$Eq=this._$Eq.forEach(n=>this._$ET(n,this[n]))),this._$EM()}updated(e){}firstUpdated(e){}};ce.elementStyles=[],ce.shadowRootOptions={mode:"open"},ce[we("elementProperties")]=new Map,ce[we("finalized")]=new Map,Xe==null||Xe({ReactiveElement:ce}),(j.reactiveElementVersions??(j.reactiveElementVersions=[])).push("2.1.1");/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const be=globalThis,Ge=be.trustedTypes,Nt=Ge?Ge.createPolicy("lit-html",{createHTML:t=>t}):void 0,lo="$lit$",Z=`lit$${Math.random().toFixed(9).slice(2)}$`,co="?"+Z,ri=`<${co}>`,re=document,Ae=()=>re.createComment(""),Se=t=>t===null||typeof t!="object"&&typeof t!="function",yt=Array.isArray,si=t=>yt(t)||typeof(t==null?void 0:t[Symbol.iterator])=="function",Qe=`[ 	
\f\r]`,Ce=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,Ft=/-->/g,Lt=/>/g,ne=RegExp(`>|${Qe}(?:([^\\s"'>=/]+)(${Qe}*=${Qe}*(?:[^ 	
\f\r"'\`<>=]|("|')|))|$)`,"g"),Ut=/'/g,$t=/"/g,uo=/^(?:script|style|textarea|title)$/i,fo=t=>(e,...n)=>({_$litType$:t,strings:e,values:n}),T=fo(1),Ze=fo(2),ee=Symbol.for("lit-noChange"),U=Symbol.for("lit-nothing"),Vt=new WeakMap,oe=re.createTreeWalker(re,129);function po(t,e){if(!yt(t)||!t.hasOwnProperty("raw"))throw Error("invalid template strings array");return Nt!==void 0?Nt.createHTML(e):e}const ai=(t,e)=>{const n=t.length-1,o=[];let s,a=e===2?"<svg>":e===3?"<math>":"",l=Ce;for(let c=0;c<n;c++){const d=t[c];let u,f,p=-1,h=0;for(;h<d.length&&(l.lastIndex=h,f=l.exec(d),f!==null);)h=l.lastIndex,l===Ce?f[1]==="!--"?l=Ft:f[1]!==void 0?l=Lt:f[2]!==void 0?(uo.test(f[2])&&(s=RegExp("</"+f[2],"g")),l=ne):f[3]!==void 0&&(l=ne):l===ne?f[0]===">"?(l=s??Ce,p=-1):f[1]===void 0?p=-2:(p=l.lastIndex-f[2].length,u=f[1],l=f[3]===void 0?ne:f[3]==='"'?$t:Ut):l===$t||l===Ut?l=ne:l===Ft||l===Lt?l=Ce:(l=ne,s=void 0);const m=l===ne&&t[c+1].startsWith("/>")?" ":"";a+=l===Ce?d+ri:p>=0?(o.push(u),d.slice(0,p)+lo+d.slice(p)+Z+m):d+Z+(p===-2?c:m)}return[po(t,a+(t[n]||"<?>")+(e===2?"</svg>":e===3?"</math>":"")),o]};class Me{constructor({strings:e,_$litType$:n},o){let s;this.parts=[];let a=0,l=0;const c=e.length-1,d=this.parts,[u,f]=ai(e,n);if(this.el=Me.createElement(u,o),oe.currentNode=this.el.content,n===2||n===3){const p=this.el.content.firstChild;p.replaceWith(...p.childNodes)}for(;(s=oe.nextNode())!==null&&d.length<c;){if(s.nodeType===1){if(s.hasAttributes())for(const p of s.getAttributeNames())if(p.endsWith(lo)){const h=f[l++],m=s.getAttribute(p).split(Z),g=/([.?@])?(.*)/.exec(h);d.push({type:1,index:a,name:g[2],strings:m,ctor:g[1]==="."?ci:g[1]==="?"?ui:g[1]==="@"?di:He}),s.removeAttribute(p)}else p.startsWith(Z)&&(d.push({type:6,index:a}),s.removeAttribute(p));if(uo.test(s.tagName)){const p=s.textContent.split(Z),h=p.length-1;if(h>0){s.textContent=Ge?Ge.emptyScript:"";for(let m=0;m<h;m++)s.append(p[m],Ae()),oe.nextNode(),d.push({type:2,index:++a});s.append(p[h],Ae())}}}else if(s.nodeType===8)if(s.data===co)d.push({type:2,index:a});else{let p=-1;for(;(p=s.data.indexOf(Z,p+1))!==-1;)d.push({type:7,index:a}),p+=Z.length-1}a++}}static createElement(e,n){const o=re.createElement("template");return o.innerHTML=e,o}}function pe(t,e,n=t,o){var l,c;if(e===ee)return e;let s=o!==void 0?(l=n._$Co)==null?void 0:l[o]:n._$Cl;const a=Se(e)?void 0:e._$litDirective$;return(s==null?void 0:s.constructor)!==a&&((c=s==null?void 0:s._$AO)==null||c.call(s,!1),a===void 0?s=void 0:(s=new a(t),s._$AT(t,n,o)),o!==void 0?(n._$Co??(n._$Co=[]))[o]=s:n._$Cl=s),s!==void 0&&(e=pe(t,s._$AS(t,e.values),s,o)),e}class li{constructor(e,n){this._$AV=[],this._$AN=void 0,this._$AD=e,this._$AM=n}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(e){const{el:{content:n},parts:o}=this._$AD,s=((e==null?void 0:e.creationScope)??re).importNode(n,!0);oe.currentNode=s;let a=oe.nextNode(),l=0,c=0,d=o[0];for(;d!==void 0;){if(l===d.index){let u;d.type===2?u=new Ie(a,a.nextSibling,this,e):d.type===1?u=new d.ctor(a,d.name,d.strings,this,e):d.type===6&&(u=new fi(a,this,e)),this._$AV.push(u),d=o[++c]}l!==(d==null?void 0:d.index)&&(a=oe.nextNode(),l++)}return oe.currentNode=re,s}p(e){let n=0;for(const o of this._$AV)o!==void 0&&(o.strings!==void 0?(o._$AI(e,o,n),n+=o.strings.length-2):o._$AI(e[n])),n++}}class Ie{get _$AU(){var e;return((e=this._$AM)==null?void 0:e._$AU)??this._$Cv}constructor(e,n,o,s){this.type=2,this._$AH=U,this._$AN=void 0,this._$AA=e,this._$AB=n,this._$AM=o,this.options=s,this._$Cv=(s==null?void 0:s.isConnected)??!0}get parentNode(){let e=this._$AA.parentNode;const n=this._$AM;return n!==void 0&&(e==null?void 0:e.nodeType)===11&&(e=n.parentNode),e}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(e,n=this){e=pe(this,e,n),Se(e)?e===U||e==null||e===""?(this._$AH!==U&&this._$AR(),this._$AH=U):e!==this._$AH&&e!==ee&&this._(e):e._$litType$!==void 0?this.$(e):e.nodeType!==void 0?this.T(e):si(e)?this.k(e):this._(e)}O(e){return this._$AA.parentNode.insertBefore(e,this._$AB)}T(e){this._$AH!==e&&(this._$AR(),this._$AH=this.O(e))}_(e){this._$AH!==U&&Se(this._$AH)?this._$AA.nextSibling.data=e:this.T(re.createTextNode(e)),this._$AH=e}$(e){var a;const{values:n,_$litType$:o}=e,s=typeof o=="number"?this._$AC(e):(o.el===void 0&&(o.el=Me.createElement(po(o.h,o.h[0]),this.options)),o);if(((a=this._$AH)==null?void 0:a._$AD)===s)this._$AH.p(n);else{const l=new li(s,this),c=l.u(this.options);l.p(n),this.T(c),this._$AH=l}}_$AC(e){let n=Vt.get(e.strings);return n===void 0&&Vt.set(e.strings,n=new Me(e)),n}k(e){yt(this._$AH)||(this._$AH=[],this._$AR());const n=this._$AH;let o,s=0;for(const a of e)s===n.length?n.push(o=new Ie(this.O(Ae()),this.O(Ae()),this,this.options)):o=n[s],o._$AI(a),s++;s<n.length&&(this._$AR(o&&o._$AB.nextSibling,s),n.length=s)}_$AR(e=this._$AA.nextSibling,n){var o;for((o=this._$AP)==null?void 0:o.call(this,!1,!0,n);e!==this._$AB;){const s=e.nextSibling;e.remove(),e=s}}setConnected(e){var n;this._$AM===void 0&&(this._$Cv=e,(n=this._$AP)==null||n.call(this,e))}}class He{get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}constructor(e,n,o,s,a){this.type=1,this._$AH=U,this._$AN=void 0,this.element=e,this.name=n,this._$AM=s,this.options=a,o.length>2||o[0]!==""||o[1]!==""?(this._$AH=Array(o.length-1).fill(new String),this.strings=o):this._$AH=U}_$AI(e,n=this,o,s){const a=this.strings;let l=!1;if(a===void 0)e=pe(this,e,n,0),l=!Se(e)||e!==this._$AH&&e!==ee,l&&(this._$AH=e);else{const c=e;let d,u;for(e=a[0],d=0;d<a.length-1;d++)u=pe(this,c[o+d],n,d),u===ee&&(u=this._$AH[d]),l||(l=!Se(u)||u!==this._$AH[d]),u===U?e=U:e!==U&&(e+=(u??"")+a[d+1]),this._$AH[d]=u}l&&!s&&this.j(e)}j(e){e===U?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,e??"")}}class ci extends He{constructor(){super(...arguments),this.type=3}j(e){this.element[this.name]=e===U?void 0:e}}class ui extends He{constructor(){super(...arguments),this.type=4}j(e){this.element.toggleAttribute(this.name,!!e&&e!==U)}}class di extends He{constructor(e,n,o,s,a){super(e,n,o,s,a),this.type=5}_$AI(e,n=this){if((e=pe(this,e,n,0)??U)===ee)return;const o=this._$AH,s=e===U&&o!==U||e.capture!==o.capture||e.once!==o.once||e.passive!==o.passive,a=e!==U&&(o===U||s);s&&this.element.removeEventListener(this.name,this,o),a&&this.element.addEventListener(this.name,this,e),this._$AH=e}handleEvent(e){var n;typeof this._$AH=="function"?this._$AH.call(((n=this.options)==null?void 0:n.host)??this.element,e):this._$AH.handleEvent(e)}}class fi{constructor(e,n,o){this.element=e,this.type=6,this._$AN=void 0,this._$AM=n,this.options=o}get _$AU(){return this._$AM._$AU}_$AI(e){pe(this,e)}}const je=be.litHtmlPolyfillSupport;je==null||je(Me,Ie),(be.litHtmlVersions??(be.litHtmlVersions=[])).push("3.3.1");const pi=(t,e,n)=>{const o=(n==null?void 0:n.renderBefore)??e;let s=o._$litPart$;if(s===void 0){const a=(n==null?void 0:n.renderBefore)??null;o._$litPart$=s=new Ie(e.insertBefore(Ae(),a),a,void 0,n??{})}return s._$AI(t),s};/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const ie=globalThis;let z=class extends ce{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){var n;const e=super.createRenderRoot();return(n=this.renderOptions).renderBefore??(n.renderBefore=e.firstChild),e}update(e){const n=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(e),this._$Do=pi(n,this.renderRoot,this.renderOptions)}connectedCallback(){var e;super.connectedCallback(),(e=this._$Do)==null||e.setConnected(!0)}disconnectedCallback(){var e;super.disconnectedCallback(),(e=this._$Do)==null||e.setConnected(!1)}render(){return ee}};var so;z._$litElement$=!0,z.finalized=!0,(so=ie.litElementHydrateSupport)==null||so.call(ie,{LitElement:z});const et=ie.litElementPolyfillSupport;et==null||et({LitElement:z});(ie.litElementVersions??(ie.litElementVersions=[])).push("4.2.1");/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const te=t=>(e,n)=>{n!==void 0?n.addInitializer(()=>{customElements.define(t,e)}):customElements.define(t,e)};/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const hi={attribute:!0,type:String,converter:Ve,reflect:!1,hasChanged:gt},mi=(t=hi,e,n)=>{const{kind:o,metadata:s}=n;let a=globalThis.litPropertyMetadata.get(s);if(a===void 0&&globalThis.litPropertyMetadata.set(s,a=new Map),o==="setter"&&((t=Object.create(t)).wrapped=!0),a.set(n.name,t),o==="accessor"){const{name:l}=n;return{set(c){const d=e.get.call(this);e.set.call(this,c),this.requestUpdate(l,d,t)},init(c){return c!==void 0&&this.C(l,void 0,t,c),c}}}if(o==="setter"){const{name:l}=n;return function(c){const d=this[l];e.call(this,c),this.requestUpdate(l,d,t)}}throw Error("Unsupported decorator location: "+o)};function L(t){return(e,n)=>typeof n=="object"?mi(t,e,n):((o,s,a)=>{const l=s.hasOwnProperty(a);return s.constructor.createProperty(a,o),l?Object.getOwnPropertyDescriptor(s,a):void 0})(t,e,n)}/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */function N(t){return L({...t,state:!0,attribute:!1})}/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const ho=(t,e,n)=>(n.configurable=!0,n.enumerable=!0,Reflect.decorate&&typeof e!="object"&&Object.defineProperty(t,e,n),n);/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */function vt(t,e){return(n,o,s)=>{const a=l=>{var c;return((c=l.renderRoot)==null?void 0:c.querySelector(t))??null};return ho(n,o,{get(){return a(this)}})}}/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */let gi;function xt(t){return(e,n)=>ho(e,n,{get(){return(this.renderRoot??gi??(gi=document.createDocumentFragment())).querySelectorAll(t)}})}/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const mo={ATTRIBUTE:1},go=t=>(...e)=>({_$litDirective$:t,values:e});let yo=class{constructor(e){}get _$AU(){return this._$AM._$AU}_$AT(e,n,o){this._$Ct=e,this._$AM=n,this._$Ci=o}_$AS(e,n){return this.update(e,n)}update(e,n){return this.render(...n)}};/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Y=go(class extends yo{constructor(t){var e;if(super(t),t.type!==mo.ATTRIBUTE||t.name!=="class"||((e=t.strings)==null?void 0:e.length)>2)throw Error("`classMap()` can only be used in the `class` attribute and must be the only part in the attribute.")}render(t){return" "+Object.keys(t).filter(e=>t[e]).join(" ")+" "}update(t,[e]){var o,s;if(this.st===void 0){this.st=new Set,t.strings!==void 0&&(this.nt=new Set(t.strings.join(" ").split(/\s/).filter(a=>a!=="")));for(const a in e)e[a]&&!((o=this.nt)!=null&&o.has(a))&&this.st.add(a);return this.render(e)}const n=t.element.classList;for(const a of this.st)a in e||(n.remove(a),this.st.delete(a));for(const a in e){const l=!!e[a];l===this.st.has(a)||(s=this.nt)!=null&&s.has(a)||(l?(n.add(a),this.st.add(a)):(n.remove(a),this.st.delete(a)))}return ee}});/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */let yi,vi;function xi(){return{geminiUrl:yi,vertexUrl:vi}}function Ci(t,e,n){var o,s,a;if(!(!((o=t.httpOptions)===null||o===void 0)&&o.baseUrl)){const l=xi();return t.vertexai?(s=l.vertexUrl)!==null&&s!==void 0?s:e:(a=l.geminiUrl)!==null&&a!==void 0?a:n}return t.httpOptions.baseUrl}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class le{}function x(t,e){const n=/\{([^}]+)\}/g;return t.replace(n,(o,s)=>{if(Object.prototype.hasOwnProperty.call(e,s)){const a=e[s];return a!=null?String(a):""}else throw new Error(`Key '${s}' not found in valueMap.`)})}function r(t,e,n){for(let a=0;a<e.length-1;a++){const l=e[a];if(l.endsWith("[]")){const c=l.slice(0,-2);if(!(c in t))if(Array.isArray(n))t[c]=Array.from({length:n.length},()=>({}));else throw new Error(`Value must be a list given an array path ${l}`);if(Array.isArray(t[c])){const d=t[c];if(Array.isArray(n))for(let u=0;u<d.length;u++){const f=d[u];r(f,e.slice(a+1),n[u])}else for(const u of d)r(u,e.slice(a+1),n)}return}else if(l.endsWith("[0]")){const c=l.slice(0,-3);c in t||(t[c]=[{}]);const d=t[c];r(d[0],e.slice(a+1),n);return}(!t[l]||typeof t[l]!="object")&&(t[l]={}),t=t[l]}const o=e[e.length-1],s=t[o];if(s!==void 0){if(!n||typeof n=="object"&&Object.keys(n).length===0||n===s)return;if(typeof s=="object"&&typeof n=="object"&&s!==null&&n!==null)Object.assign(s,n);else throw new Error(`Cannot set value for an existing key. Key: ${o}`)}else t[o]=n}function i(t,e){try{if(e.length===1&&e[0]==="_self")return t;for(let n=0;n<e.length;n++){if(typeof t!="object"||t===null)return;const o=e[n];if(o.endsWith("[]")){const s=o.slice(0,-2);if(s in t){const a=t[s];return Array.isArray(a)?a.map(l=>i(l,e.slice(n+1))):void 0}else return}else t=t[o]}return t}catch(n){if(n instanceof TypeError)return;throw n}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */var Gt;(function(t){t.OUTCOME_UNSPECIFIED="OUTCOME_UNSPECIFIED",t.OUTCOME_OK="OUTCOME_OK",t.OUTCOME_FAILED="OUTCOME_FAILED",t.OUTCOME_DEADLINE_EXCEEDED="OUTCOME_DEADLINE_EXCEEDED"})(Gt||(Gt={}));var Bt;(function(t){t.LANGUAGE_UNSPECIFIED="LANGUAGE_UNSPECIFIED",t.PYTHON="PYTHON"})(Bt||(Bt={}));var q;(function(t){t.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",t.STRING="STRING",t.NUMBER="NUMBER",t.INTEGER="INTEGER",t.BOOLEAN="BOOLEAN",t.ARRAY="ARRAY",t.OBJECT="OBJECT",t.NULL="NULL"})(q||(q={}));var qt;(function(t){t.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",t.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",t.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",t.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",t.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",t.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY",t.HARM_CATEGORY_IMAGE_HATE="HARM_CATEGORY_IMAGE_HATE",t.HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT="HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT",t.HARM_CATEGORY_IMAGE_HARASSMENT="HARM_CATEGORY_IMAGE_HARASSMENT",t.HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT="HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT"})(qt||(qt={}));var zt;(function(t){t.HARM_BLOCK_METHOD_UNSPECIFIED="HARM_BLOCK_METHOD_UNSPECIFIED",t.SEVERITY="SEVERITY",t.PROBABILITY="PROBABILITY"})(zt||(zt={}));var Ht;(function(t){t.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",t.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",t.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",t.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",t.BLOCK_NONE="BLOCK_NONE",t.OFF="OFF"})(Ht||(Ht={}));var Jt;(function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.MODE_DYNAMIC="MODE_DYNAMIC"})(Jt||(Jt={}));var Wt;(function(t){t.AUTH_TYPE_UNSPECIFIED="AUTH_TYPE_UNSPECIFIED",t.NO_AUTH="NO_AUTH",t.API_KEY_AUTH="API_KEY_AUTH",t.HTTP_BASIC_AUTH="HTTP_BASIC_AUTH",t.GOOGLE_SERVICE_ACCOUNT_AUTH="GOOGLE_SERVICE_ACCOUNT_AUTH",t.OAUTH="OAUTH",t.OIDC_AUTH="OIDC_AUTH"})(Wt||(Wt={}));var Yt;(function(t){t.API_SPEC_UNSPECIFIED="API_SPEC_UNSPECIFIED",t.SIMPLE_SEARCH="SIMPLE_SEARCH",t.ELASTIC_SEARCH="ELASTIC_SEARCH"})(Yt||(Yt={}));var Kt;(function(t){t.ENVIRONMENT_UNSPECIFIED="ENVIRONMENT_UNSPECIFIED",t.ENVIRONMENT_BROWSER="ENVIRONMENT_BROWSER"})(Kt||(Kt={}));var Ot;(function(t){t.URL_RETRIEVAL_STATUS_UNSPECIFIED="URL_RETRIEVAL_STATUS_UNSPECIFIED",t.URL_RETRIEVAL_STATUS_SUCCESS="URL_RETRIEVAL_STATUS_SUCCESS",t.URL_RETRIEVAL_STATUS_ERROR="URL_RETRIEVAL_STATUS_ERROR"})(Ot||(Ot={}));var Xt;(function(t){t.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",t.STOP="STOP",t.MAX_TOKENS="MAX_TOKENS",t.SAFETY="SAFETY",t.RECITATION="RECITATION",t.LANGUAGE="LANGUAGE",t.OTHER="OTHER",t.BLOCKLIST="BLOCKLIST",t.PROHIBITED_CONTENT="PROHIBITED_CONTENT",t.SPII="SPII",t.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",t.IMAGE_SAFETY="IMAGE_SAFETY",t.UNEXPECTED_TOOL_CALL="UNEXPECTED_TOOL_CALL"})(Xt||(Xt={}));var Qt;(function(t){t.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",t.NEGLIGIBLE="NEGLIGIBLE",t.LOW="LOW",t.MEDIUM="MEDIUM",t.HIGH="HIGH"})(Qt||(Qt={}));var Zt;(function(t){t.HARM_SEVERITY_UNSPECIFIED="HARM_SEVERITY_UNSPECIFIED",t.HARM_SEVERITY_NEGLIGIBLE="HARM_SEVERITY_NEGLIGIBLE",t.HARM_SEVERITY_LOW="HARM_SEVERITY_LOW",t.HARM_SEVERITY_MEDIUM="HARM_SEVERITY_MEDIUM",t.HARM_SEVERITY_HIGH="HARM_SEVERITY_HIGH"})(Zt||(Zt={}));var jt;(function(t){t.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",t.SAFETY="SAFETY",t.OTHER="OTHER",t.BLOCKLIST="BLOCKLIST",t.PROHIBITED_CONTENT="PROHIBITED_CONTENT",t.IMAGE_SAFETY="IMAGE_SAFETY"})(jt||(jt={}));var en;(function(t){t.TRAFFIC_TYPE_UNSPECIFIED="TRAFFIC_TYPE_UNSPECIFIED",t.ON_DEMAND="ON_DEMAND",t.PROVISIONED_THROUGHPUT="PROVISIONED_THROUGHPUT"})(en||(en={}));var Be;(function(t){t.MODALITY_UNSPECIFIED="MODALITY_UNSPECIFIED",t.TEXT="TEXT",t.IMAGE="IMAGE",t.AUDIO="AUDIO"})(Be||(Be={}));var tn;(function(t){t.MEDIA_RESOLUTION_UNSPECIFIED="MEDIA_RESOLUTION_UNSPECIFIED",t.MEDIA_RESOLUTION_LOW="MEDIA_RESOLUTION_LOW",t.MEDIA_RESOLUTION_MEDIUM="MEDIA_RESOLUTION_MEDIUM",t.MEDIA_RESOLUTION_HIGH="MEDIA_RESOLUTION_HIGH"})(tn||(tn={}));var nt;(function(t){t.JOB_STATE_UNSPECIFIED="JOB_STATE_UNSPECIFIED",t.JOB_STATE_QUEUED="JOB_STATE_QUEUED",t.JOB_STATE_PENDING="JOB_STATE_PENDING",t.JOB_STATE_RUNNING="JOB_STATE_RUNNING",t.JOB_STATE_SUCCEEDED="JOB_STATE_SUCCEEDED",t.JOB_STATE_FAILED="JOB_STATE_FAILED",t.JOB_STATE_CANCELLING="JOB_STATE_CANCELLING",t.JOB_STATE_CANCELLED="JOB_STATE_CANCELLED",t.JOB_STATE_PAUSED="JOB_STATE_PAUSED",t.JOB_STATE_EXPIRED="JOB_STATE_EXPIRED",t.JOB_STATE_UPDATING="JOB_STATE_UPDATING",t.JOB_STATE_PARTIALLY_SUCCEEDED="JOB_STATE_PARTIALLY_SUCCEEDED"})(nt||(nt={}));var nn;(function(t){t.ADAPTER_SIZE_UNSPECIFIED="ADAPTER_SIZE_UNSPECIFIED",t.ADAPTER_SIZE_ONE="ADAPTER_SIZE_ONE",t.ADAPTER_SIZE_TWO="ADAPTER_SIZE_TWO",t.ADAPTER_SIZE_FOUR="ADAPTER_SIZE_FOUR",t.ADAPTER_SIZE_EIGHT="ADAPTER_SIZE_EIGHT",t.ADAPTER_SIZE_SIXTEEN="ADAPTER_SIZE_SIXTEEN",t.ADAPTER_SIZE_THIRTY_TWO="ADAPTER_SIZE_THIRTY_TWO"})(nn||(nn={}));var on;(function(t){t.FEATURE_SELECTION_PREFERENCE_UNSPECIFIED="FEATURE_SELECTION_PREFERENCE_UNSPECIFIED",t.PRIORITIZE_QUALITY="PRIORITIZE_QUALITY",t.BALANCED="BALANCED",t.PRIORITIZE_COST="PRIORITIZE_COST"})(on||(on={}));var rn;(function(t){t.UNSPECIFIED="UNSPECIFIED",t.BLOCKING="BLOCKING",t.NON_BLOCKING="NON_BLOCKING"})(rn||(rn={}));var sn;(function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.MODE_DYNAMIC="MODE_DYNAMIC"})(sn||(sn={}));var an;(function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.AUTO="AUTO",t.ANY="ANY",t.NONE="NONE"})(an||(an={}));var ln;(function(t){t.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",t.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",t.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",t.BLOCK_NONE="BLOCK_NONE"})(ln||(ln={}));var cn;(function(t){t.DONT_ALLOW="DONT_ALLOW",t.ALLOW_ADULT="ALLOW_ADULT",t.ALLOW_ALL="ALLOW_ALL"})(cn||(cn={}));var un;(function(t){t.auto="auto",t.en="en",t.ja="ja",t.ko="ko",t.hi="hi",t.zh="zh",t.pt="pt",t.es="es"})(un||(un={}));var dn;(function(t){t.MASK_MODE_DEFAULT="MASK_MODE_DEFAULT",t.MASK_MODE_USER_PROVIDED="MASK_MODE_USER_PROVIDED",t.MASK_MODE_BACKGROUND="MASK_MODE_BACKGROUND",t.MASK_MODE_FOREGROUND="MASK_MODE_FOREGROUND",t.MASK_MODE_SEMANTIC="MASK_MODE_SEMANTIC"})(dn||(dn={}));var fn;(function(t){t.CONTROL_TYPE_DEFAULT="CONTROL_TYPE_DEFAULT",t.CONTROL_TYPE_CANNY="CONTROL_TYPE_CANNY",t.CONTROL_TYPE_SCRIBBLE="CONTROL_TYPE_SCRIBBLE",t.CONTROL_TYPE_FACE_MESH="CONTROL_TYPE_FACE_MESH"})(fn||(fn={}));var pn;(function(t){t.SUBJECT_TYPE_DEFAULT="SUBJECT_TYPE_DEFAULT",t.SUBJECT_TYPE_PERSON="SUBJECT_TYPE_PERSON",t.SUBJECT_TYPE_ANIMAL="SUBJECT_TYPE_ANIMAL",t.SUBJECT_TYPE_PRODUCT="SUBJECT_TYPE_PRODUCT"})(pn||(pn={}));var hn;(function(t){t.EDIT_MODE_DEFAULT="EDIT_MODE_DEFAULT",t.EDIT_MODE_INPAINT_REMOVAL="EDIT_MODE_INPAINT_REMOVAL",t.EDIT_MODE_INPAINT_INSERTION="EDIT_MODE_INPAINT_INSERTION",t.EDIT_MODE_OUTPAINT="EDIT_MODE_OUTPAINT",t.EDIT_MODE_CONTROLLED_EDITING="EDIT_MODE_CONTROLLED_EDITING",t.EDIT_MODE_STYLE="EDIT_MODE_STYLE",t.EDIT_MODE_BGSWAP="EDIT_MODE_BGSWAP",t.EDIT_MODE_PRODUCT_IMAGE="EDIT_MODE_PRODUCT_IMAGE"})(hn||(hn={}));var mn;(function(t){t.OPTIMIZED="OPTIMIZED",t.LOSSLESS="LOSSLESS"})(mn||(mn={}));var gn;(function(t){t.STATE_UNSPECIFIED="STATE_UNSPECIFIED",t.PROCESSING="PROCESSING",t.ACTIVE="ACTIVE",t.FAILED="FAILED"})(gn||(gn={}));var yn;(function(t){t.SOURCE_UNSPECIFIED="SOURCE_UNSPECIFIED",t.UPLOADED="UPLOADED",t.GENERATED="GENERATED"})(yn||(yn={}));var vn;(function(t){t.MODALITY_UNSPECIFIED="MODALITY_UNSPECIFIED",t.TEXT="TEXT",t.IMAGE="IMAGE",t.VIDEO="VIDEO",t.AUDIO="AUDIO",t.DOCUMENT="DOCUMENT"})(vn||(vn={}));var xn;(function(t){t.START_SENSITIVITY_UNSPECIFIED="START_SENSITIVITY_UNSPECIFIED",t.START_SENSITIVITY_HIGH="START_SENSITIVITY_HIGH",t.START_SENSITIVITY_LOW="START_SENSITIVITY_LOW"})(xn||(xn={}));var Cn;(function(t){t.END_SENSITIVITY_UNSPECIFIED="END_SENSITIVITY_UNSPECIFIED",t.END_SENSITIVITY_HIGH="END_SENSITIVITY_HIGH",t.END_SENSITIVITY_LOW="END_SENSITIVITY_LOW"})(Cn||(Cn={}));var Tn;(function(t){t.ACTIVITY_HANDLING_UNSPECIFIED="ACTIVITY_HANDLING_UNSPECIFIED",t.START_OF_ACTIVITY_INTERRUPTS="START_OF_ACTIVITY_INTERRUPTS",t.NO_INTERRUPTION="NO_INTERRUPTION"})(Tn||(Tn={}));var wn;(function(t){t.TURN_COVERAGE_UNSPECIFIED="TURN_COVERAGE_UNSPECIFIED",t.TURN_INCLUDES_ONLY_ACTIVITY="TURN_INCLUDES_ONLY_ACTIVITY",t.TURN_INCLUDES_ALL_INPUT="TURN_INCLUDES_ALL_INPUT"})(wn||(wn={}));var bn;(function(t){t.SCHEDULING_UNSPECIFIED="SCHEDULING_UNSPECIFIED",t.SILENT="SILENT",t.WHEN_IDLE="WHEN_IDLE",t.INTERRUPT="INTERRUPT"})(bn||(bn={}));var En;(function(t){t.SCALE_UNSPECIFIED="SCALE_UNSPECIFIED",t.C_MAJOR_A_MINOR="C_MAJOR_A_MINOR",t.D_FLAT_MAJOR_B_FLAT_MINOR="D_FLAT_MAJOR_B_FLAT_MINOR",t.D_MAJOR_B_MINOR="D_MAJOR_B_MINOR",t.E_FLAT_MAJOR_C_MINOR="E_FLAT_MAJOR_C_MINOR",t.E_MAJOR_D_FLAT_MINOR="E_MAJOR_D_FLAT_MINOR",t.F_MAJOR_D_MINOR="F_MAJOR_D_MINOR",t.G_FLAT_MAJOR_E_FLAT_MINOR="G_FLAT_MAJOR_E_FLAT_MINOR",t.G_MAJOR_E_MINOR="G_MAJOR_E_MINOR",t.A_FLAT_MAJOR_F_MINOR="A_FLAT_MAJOR_F_MINOR",t.A_MAJOR_G_FLAT_MINOR="A_MAJOR_G_FLAT_MINOR",t.B_FLAT_MAJOR_G_MINOR="B_FLAT_MAJOR_G_MINOR",t.B_MAJOR_A_FLAT_MINOR="B_MAJOR_A_FLAT_MINOR"})(En||(En={}));var ue;(function(t){t.PLAYBACK_CONTROL_UNSPECIFIED="PLAYBACK_CONTROL_UNSPECIFIED",t.PLAY="PLAY",t.PAUSE="PAUSE",t.STOP="STOP",t.RESET_CONTEXT="RESET_CONTEXT"})(ue||(ue={}));class ot{constructor(e){const n={};for(const o of e.headers.entries())n[o[0]]=o[1];this.headers=n,this.responseInternal=e}json(){return this.responseInternal.json()}}class Te{get text(){var e,n,o,s,a,l,c,d;if(((s=(o=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||o===void 0?void 0:o.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning text from the first one.");let u="",f=!1;const p=[];for(const h of(d=(c=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||c===void 0?void 0:c.parts)!==null&&d!==void 0?d:[]){for(const[m,g]of Object.entries(h))m!=="text"&&m!=="thought"&&(g!==null||g!==void 0)&&p.push(m);if(typeof h.text=="string"){if(typeof h.thought=="boolean"&&h.thought)continue;f=!0,u+=h.text}}return p.length>0&&console.warn(`there are non-text parts ${p} in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.`),f?u:void 0}get data(){var e,n,o,s,a,l,c,d;if(((s=(o=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||o===void 0?void 0:o.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning data from the first one.");let u="";const f=[];for(const p of(d=(c=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||c===void 0?void 0:c.parts)!==null&&d!==void 0?d:[]){for(const[h,m]of Object.entries(p))h!=="inlineData"&&(m!==null||m!==void 0)&&f.push(h);p.inlineData&&typeof p.inlineData.data=="string"&&(u+=atob(p.inlineData.data))}return f.length>0&&console.warn(`there are non-data parts ${f} in the response, returning concatenation of all data parts. Please refer to the non data parts for a full response from model.`),u.length>0?btoa(u):void 0}get functionCalls(){var e,n,o,s,a,l,c,d;if(((s=(o=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||o===void 0?void 0:o.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning function calls from the first one.");const u=(d=(c=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||c===void 0?void 0:c.parts)===null||d===void 0?void 0:d.filter(f=>f.functionCall).map(f=>f.functionCall).filter(f=>f!==void 0);if((u==null?void 0:u.length)!==0)return u}get executableCode(){var e,n,o,s,a,l,c,d,u;if(((s=(o=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||o===void 0?void 0:o.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning executable code from the first one.");const f=(d=(c=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||c===void 0?void 0:c.parts)===null||d===void 0?void 0:d.filter(p=>p.executableCode).map(p=>p.executableCode).filter(p=>p!==void 0);if((f==null?void 0:f.length)!==0)return(u=f==null?void 0:f[0])===null||u===void 0?void 0:u.code}get codeExecutionResult(){var e,n,o,s,a,l,c,d,u;if(((s=(o=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||o===void 0?void 0:o.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning code execution result from the first one.");const f=(d=(c=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||c===void 0?void 0:c.parts)===null||d===void 0?void 0:d.filter(p=>p.codeExecutionResult).map(p=>p.codeExecutionResult).filter(p=>p!==void 0);if((f==null?void 0:f.length)!==0)return(u=f==null?void 0:f[0])===null||u===void 0?void 0:u.output}}class An{}class Sn{}class Ti{}class wi{}class Mn{}class _n{}class Pn{}class bi{}class In{}class kn{}class Rn{}class Ei{}class Ai{}class Si{}class Dn{}class Mi{get text(){var e,n,o;let s="",a=!1;const l=[];for(const c of(o=(n=(e=this.serverContent)===null||e===void 0?void 0:e.modelTurn)===null||n===void 0?void 0:n.parts)!==null&&o!==void 0?o:[]){for(const[d,u]of Object.entries(c))d!=="text"&&d!=="thought"&&u!==null&&l.push(d);if(typeof c.text=="string"){if(typeof c.thought=="boolean"&&c.thought)continue;a=!0,s+=c.text}}return l.length>0&&console.warn(`there are non-text parts ${l} in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.`),a?s:void 0}get data(){var e,n,o;let s="";const a=[];for(const l of(o=(n=(e=this.serverContent)===null||e===void 0?void 0:e.modelTurn)===null||n===void 0?void 0:n.parts)!==null&&o!==void 0?o:[]){for(const[c,d]of Object.entries(l))c!=="inlineData"&&d!==null&&a.push(c);l.inlineData&&typeof l.inlineData.data=="string"&&(s+=atob(l.inlineData.data))}return a.length>0&&console.warn(`there are non-data parts ${a} in the response, returning concatenation of all data parts. Please refer to the non data parts for a full response from model.`),s.length>0?btoa(s):void 0}}class _i{get audioChunk(){if(this.serverContent&&this.serverContent.audioChunks&&this.serverContent.audioChunks.length>0)return this.serverContent.audioChunks[0]}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function I(t,e){if(!e||typeof e!="string")throw new Error("model is required and must be a string");if(t.isVertexAI()){if(e.startsWith("publishers/")||e.startsWith("projects/")||e.startsWith("models/"))return e;if(e.indexOf("/")>=0){const n=e.split("/",2);return`publishers/${n[0]}/models/${n[1]}`}else return`publishers/google/models/${e}`}else return e.startsWith("models/")||e.startsWith("tunedModels/")?e:`models/${e}`}function vo(t,e){const n=I(t,e);return n?n.startsWith("publishers/")&&t.isVertexAI()?`projects/${t.getProject()}/locations/${t.getLocation()}/${n}`:n.startsWith("models/")&&t.isVertexAI()?`projects/${t.getProject()}/locations/${t.getLocation()}/publishers/google/${n}`:n:""}function xo(t){return Array.isArray(t)?t.map(e=>qe(e)):[qe(t)]}function qe(t){if(typeof t=="object"&&t!==null)return t;throw new Error(`Could not parse input as Blob. Unsupported blob type: ${typeof t}`)}function Co(t){const e=qe(t);if(e.mimeType&&e.mimeType.startsWith("image/"))return e;throw new Error(`Unsupported mime type: ${e.mimeType}`)}function To(t){const e=qe(t);if(e.mimeType&&e.mimeType.startsWith("audio/"))return e;throw new Error(`Unsupported mime type: ${e.mimeType}`)}function Nn(t){if(t==null)throw new Error("PartUnion is required");if(typeof t=="object")return t;if(typeof t=="string")return{text:t};throw new Error(`Unsupported part type: ${typeof t}`)}function wo(t){if(t==null||Array.isArray(t)&&t.length===0)throw new Error("PartListUnion is required");return Array.isArray(t)?t.map(e=>Nn(e)):[Nn(t)]}function it(t){return t!=null&&typeof t=="object"&&"parts"in t&&Array.isArray(t.parts)}function Fn(t){return t!=null&&typeof t=="object"&&"functionCall"in t}function Ln(t){return t!=null&&typeof t=="object"&&"functionResponse"in t}function $(t){if(t==null)throw new Error("ContentUnion is required");return it(t)?t:{role:"user",parts:wo(t)}}function bo(t,e){if(!e)return[];if(t.isVertexAI()&&Array.isArray(e))return e.flatMap(n=>{const o=$(n);return o.parts&&o.parts.length>0&&o.parts[0].text!==void 0?[o.parts[0].text]:[]});if(t.isVertexAI()){const n=$(e);return n.parts&&n.parts.length>0&&n.parts[0].text!==void 0?[n.parts[0].text]:[]}return Array.isArray(e)?e.map(n=>$(n)):[$(e)]}function H(t){if(t==null||Array.isArray(t)&&t.length===0)throw new Error("contents are required");if(!Array.isArray(t)){if(Fn(t)||Ln(t))throw new Error("To specify functionCall or functionResponse parts, please wrap them in a Content object, specifying the role for them");return[$(t)]}const e=[],n=[],o=it(t[0]);for(const s of t){const a=it(s);if(a!=o)throw new Error("Mixing Content and Parts is not supported, please group the parts into a the appropriate Content objects and specify the roles for them");if(a)e.push(s);else{if(Fn(s)||Ln(s))throw new Error("To specify functionCall or functionResponse parts, please wrap them, and any other parts, in Content objects as appropriate, specifying the role for them");n.push(s)}}return o||e.push({role:"user",parts:wo(n)}),e}function Pi(t,e){t.includes("null")&&(e.nullable=!0);const n=t.filter(o=>o!=="null");if(n.length===1)e.type=Object.values(q).includes(n[0].toUpperCase())?n[0].toUpperCase():q.TYPE_UNSPECIFIED;else{e.anyOf=[];for(const o of n)e.anyOf.push({type:Object.values(q).includes(o.toUpperCase())?o.toUpperCase():q.TYPE_UNSPECIFIED})}}function de(t){const e={},n=["items"],o=["anyOf"],s=["properties"];if(t.type&&t.anyOf)throw new Error("type and anyOf cannot be both populated.");const a=t.anyOf;a!=null&&a.length==2&&(a[0].type==="null"?(e.nullable=!0,t=a[1]):a[1].type==="null"&&(e.nullable=!0,t=a[0])),t.type instanceof Array&&Pi(t.type,e);for(const[l,c]of Object.entries(t))if(c!=null)if(l=="type"){if(c==="null")throw new Error("type: null can not be the only possible type for the field.");if(c instanceof Array)continue;e.type=Object.values(q).includes(c.toUpperCase())?c.toUpperCase():q.TYPE_UNSPECIFIED}else if(n.includes(l))e[l]=de(c);else if(o.includes(l)){const d=[];for(const u of c){if(u.type=="null"){e.nullable=!0;continue}d.push(de(u))}e[l]=d}else if(s.includes(l)){const d={};for(const[u,f]of Object.entries(c))d[u]=de(f);e[l]=d}else{if(l==="additionalProperties")continue;e[l]=c}return e}function Ct(t){return de(t)}function Tt(t){if(typeof t=="object")return t;if(typeof t=="string")return{voiceConfig:{prebuiltVoiceConfig:{voiceName:t}}};throw new Error(`Unsupported speechConfig type: ${typeof t}`)}function wt(t){if("multiSpeakerVoiceConfig"in t)throw new Error("multiSpeakerVoiceConfig is not supported in the live API.");return t}function ge(t){if(t.functionDeclarations)for(const e of t.functionDeclarations)e.parameters&&(Object.keys(e.parameters).includes("$schema")?e.parametersJsonSchema||(e.parametersJsonSchema=e.parameters,delete e.parameters):e.parameters=de(e.parameters)),e.response&&(Object.keys(e.response).includes("$schema")?e.responseJsonSchema||(e.responseJsonSchema=e.response,delete e.response):e.response=de(e.response));return t}function ye(t){if(t==null)throw new Error("tools is required");if(!Array.isArray(t))throw new Error("tools is required and must be an array of Tools");const e=[];for(const n of t)e.push(n);return e}function Ii(t,e,n,o=1){const s=!e.startsWith(`${n}/`)&&e.split("/").length===o;return t.isVertexAI()?e.startsWith("projects/")?e:e.startsWith("locations/")?`projects/${t.getProject()}/${e}`:e.startsWith(`${n}/`)?`projects/${t.getProject()}/locations/${t.getLocation()}/${e}`:s?`projects/${t.getProject()}/locations/${t.getLocation()}/${n}/${e}`:e:s?`${n}/${e}`:e}function X(t,e){if(typeof e!="string")throw new Error("name must be a string");return Ii(t,e,"cachedContents")}function Eo(t){switch(t){case"STATE_UNSPECIFIED":return"JOB_STATE_UNSPECIFIED";case"CREATING":return"JOB_STATE_RUNNING";case"ACTIVE":return"JOB_STATE_SUCCEEDED";case"FAILED":return"JOB_STATE_FAILED";default:return t}}function Q(t){if(typeof t!="string")throw new Error("fromImageBytes must be a string");return t}function ki(t){return t!=null&&typeof t=="object"&&"name"in t}function Ri(t){return t!=null&&typeof t=="object"&&"video"in t}function Di(t){return t!=null&&typeof t=="object"&&"uri"in t}function Ao(t){var e;let n;if(ki(t)&&(n=t.name),!(Di(t)&&(n=t.uri,n===void 0))&&!(Ri(t)&&(n=(e=t.video)===null||e===void 0?void 0:e.uri,n===void 0))){if(typeof t=="string"&&(n=t),n===void 0)throw new Error("Could not extract file name from the provided input.");if(n.startsWith("https://")){const s=n.split("files/")[1].match(/[a-z0-9]+/);if(s===null)throw new Error(`Could not extract file name from URI ${n}`);n=s[0]}else n.startsWith("files/")&&(n=n.split("files/")[1]);return n}}function So(t,e){let n;return t.isVertexAI()?n=e?"publishers/google/models":"models":n=e?"models":"tunedModels",n}function Mo(t){for(const e of["models","tunedModels","publisherModels"])if(Ni(t,e))return t[e];return[]}function Ni(t,e){return t!==null&&typeof t=="object"&&e in t}function Fi(t,e={}){const n=t,o={name:n.name,description:n.description,parametersJsonSchema:n.inputSchema};return e.behavior&&(o.behavior=e.behavior),{functionDeclarations:[o]}}function Li(t,e={}){const n=[],o=new Set;for(const s of t){const a=s.name;if(o.has(a))throw new Error(`Duplicate function name ${a} found in MCP tools. Please ensure function names are unique.`);o.add(a);const l=Fi(s,e);l.functionDeclarations&&n.push(...l.functionDeclarations)}return{functionDeclarations:n}}function _o(t,e){if(typeof e!="string"&&!Array.isArray(e)){if(t&&t.isVertexAI()){if(e.gcsUri&&e.bigqueryUri)throw new Error("Only one of `gcsUri` or `bigqueryUri` can be set.");if(!e.gcsUri&&!e.bigqueryUri)throw new Error("One of `gcsUri` or `bigqueryUri` must be set.")}else{if(e.inlinedRequests&&e.fileName)throw new Error("Only one of `inlinedRequests` or `fileName` can be set.");if(!e.inlinedRequests&&!e.fileName)throw new Error("One of `inlinedRequests` or `fileName` must be set.")}return e}else{if(Array.isArray(e))return{inlinedRequests:e};if(typeof e=="string"){if(e.startsWith("gs://"))return{format:"jsonl",gcsUri:[e]};if(e.startsWith("bq://"))return{format:"bigquery",bigqueryUri:e};if(e.startsWith("files/"))return{fileName:e}}}throw new Error(`Unsupported source: ${e}`)}function Ui(t){const e=t;if(e.startsWith("gs://"))return{format:"jsonl",gcsUri:e};if(e.startsWith("bq://"))return{format:"bigquery",bigqueryUri:e};throw new Error(`Unsupported destination: ${e}`)}function ve(t,e){const n=e;if(!t.isVertexAI()){if(/batches\/[^/]+$/.test(n))return n.split("/").pop();throw new Error(`Invalid batch job name: ${n}.`)}if(/^projects\/[^/]+\/locations\/[^/]+\/batchPredictionJobs\/[^/]+$/.test(n))return n.split("/").pop();if(/^\d+$/.test(n))return n;throw new Error(`Invalid batch job name: ${n}.`)}function Po(t){const e=t;return e==="BATCH_STATE_UNSPECIFIED"?"JOB_STATE_UNSPECIFIED":e==="BATCH_STATE_PENDING"?"JOB_STATE_PENDING":e==="BATCH_STATE_SUCCEEDED"?"JOB_STATE_SUCCEEDED":e==="BATCH_STATE_FAILED"?"JOB_STATE_FAILED":e==="BATCH_STATE_CANCELLED"?"JOB_STATE_CANCELLED":e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function $i(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Vi(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Gi(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Bi(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],$i(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],Vi(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],Gi(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Io(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Bi(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function qi(t){const e={},n=i(t,["anyOf"]);n!=null&&r(e,["anyOf"],n);const o=i(t,["default"]);o!=null&&r(e,["default"],o);const s=i(t,["description"]);s!=null&&r(e,["description"],s);const a=i(t,["enum"]);a!=null&&r(e,["enum"],a);const l=i(t,["example"]);l!=null&&r(e,["example"],l);const c=i(t,["format"]);c!=null&&r(e,["format"],c);const d=i(t,["items"]);d!=null&&r(e,["items"],d);const u=i(t,["maxItems"]);u!=null&&r(e,["maxItems"],u);const f=i(t,["maxLength"]);f!=null&&r(e,["maxLength"],f);const p=i(t,["maxProperties"]);p!=null&&r(e,["maxProperties"],p);const h=i(t,["maximum"]);h!=null&&r(e,["maximum"],h);const m=i(t,["minItems"]);m!=null&&r(e,["minItems"],m);const g=i(t,["minLength"]);g!=null&&r(e,["minLength"],g);const y=i(t,["minProperties"]);y!=null&&r(e,["minProperties"],y);const v=i(t,["minimum"]);v!=null&&r(e,["minimum"],v);const C=i(t,["nullable"]);C!=null&&r(e,["nullable"],C);const w=i(t,["pattern"]);w!=null&&r(e,["pattern"],w);const b=i(t,["properties"]);b!=null&&r(e,["properties"],b);const A=i(t,["propertyOrdering"]);A!=null&&r(e,["propertyOrdering"],A);const E=i(t,["required"]);E!=null&&r(e,["required"],E);const M=i(t,["title"]);M!=null&&r(e,["title"],M);const _=i(t,["type"]);return _!=null&&r(e,["type"],_),e}function zi(t){const e={};if(i(t,["method"])!==void 0)throw new Error("method parameter is not supported in Gemini API.");const n=i(t,["category"]);n!=null&&r(e,["category"],n);const o=i(t,["threshold"]);return o!=null&&r(e,["threshold"],o),e}function Hi(t){const e={},n=i(t,["behavior"]);n!=null&&r(e,["behavior"],n);const o=i(t,["description"]);o!=null&&r(e,["description"],o);const s=i(t,["name"]);s!=null&&r(e,["name"],s);const a=i(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=i(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const c=i(t,["response"]);c!=null&&r(e,["response"],c);const d=i(t,["responseJsonSchema"]);return d!=null&&r(e,["responseJsonSchema"],d),e}function Ji(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function Wi(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Ji(n)),e}function Yi(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function Ki(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Yi(n)),e}function Oi(){return{}}function Xi(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(u=>Hi(u))),r(e,["functionDeclarations"],d)}if(i(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const o=i(t,["googleSearch"]);o!=null&&r(e,["googleSearch"],Wi(o));const s=i(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],Ki(s)),i(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(i(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");i(t,["urlContext"])!=null&&r(e,["urlContext"],Oi());const l=i(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const c=i(t,["computerUse"]);return c!=null&&r(e,["computerUse"],c),e}function Qi(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["allowedFunctionNames"]);return o!=null&&r(e,["allowedFunctionNames"],o),e}function Zi(t){const e={},n=i(t,["latitude"]);n!=null&&r(e,["latitude"],n);const o=i(t,["longitude"]);return o!=null&&r(e,["longitude"],o),e}function ji(t){const e={},n=i(t,["latLng"]);n!=null&&r(e,["latLng"],Zi(n));const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function er(t){const e={},n=i(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],Qi(n));const o=i(t,["retrievalConfig"]);return o!=null&&r(e,["retrievalConfig"],ji(o)),e}function tr(t){const e={},n=i(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function ko(t){const e={},n=i(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],tr(n)),e}function nr(t){const e={},n=i(t,["speaker"]);n!=null&&r(e,["speaker"],n);const o=i(t,["voiceConfig"]);return o!=null&&r(e,["voiceConfig"],ko(o)),e}function or(t){const e={},n=i(t,["speakerVoiceConfigs"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>nr(s))),r(e,["speakerVoiceConfigs"],o)}return e}function ir(t){const e={},n=i(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],ko(n));const o=i(t,["multiSpeakerVoiceConfig"]);o!=null&&r(e,["multiSpeakerVoiceConfig"],or(o));const s=i(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function rr(t){const e={},n=i(t,["includeThoughts"]);n!=null&&r(e,["includeThoughts"],n);const o=i(t,["thinkingBudget"]);return o!=null&&r(e,["thinkingBudget"],o),e}function sr(t,e,n){const o={},s=i(e,["systemInstruction"]);n!==void 0&&s!=null&&r(n,["systemInstruction"],Io($(s)));const a=i(e,["temperature"]);a!=null&&r(o,["temperature"],a);const l=i(e,["topP"]);l!=null&&r(o,["topP"],l);const c=i(e,["topK"]);c!=null&&r(o,["topK"],c);const d=i(e,["candidateCount"]);d!=null&&r(o,["candidateCount"],d);const u=i(e,["maxOutputTokens"]);u!=null&&r(o,["maxOutputTokens"],u);const f=i(e,["stopSequences"]);f!=null&&r(o,["stopSequences"],f);const p=i(e,["responseLogprobs"]);p!=null&&r(o,["responseLogprobs"],p);const h=i(e,["logprobs"]);h!=null&&r(o,["logprobs"],h);const m=i(e,["presencePenalty"]);m!=null&&r(o,["presencePenalty"],m);const g=i(e,["frequencyPenalty"]);g!=null&&r(o,["frequencyPenalty"],g);const y=i(e,["seed"]);y!=null&&r(o,["seed"],y);const v=i(e,["responseMimeType"]);v!=null&&r(o,["responseMimeType"],v);const C=i(e,["responseSchema"]);C!=null&&r(o,["responseSchema"],qi(Ct(C)));const w=i(e,["responseJsonSchema"]);if(w!=null&&r(o,["responseJsonSchema"],w),i(e,["routingConfig"])!==void 0)throw new Error("routingConfig parameter is not supported in Gemini API.");if(i(e,["modelSelectionConfig"])!==void 0)throw new Error("modelSelectionConfig parameter is not supported in Gemini API.");const b=i(e,["safetySettings"]);if(n!==void 0&&b!=null){let P=b;Array.isArray(P)&&(P=P.map(W=>zi(W))),r(n,["safetySettings"],P)}const A=i(e,["tools"]);if(n!==void 0&&A!=null){let P=ye(A);Array.isArray(P)&&(P=P.map(W=>Xi(ge(W)))),r(n,["tools"],P)}const E=i(e,["toolConfig"]);if(n!==void 0&&E!=null&&r(n,["toolConfig"],er(E)),i(e,["labels"])!==void 0)throw new Error("labels parameter is not supported in Gemini API.");const M=i(e,["cachedContent"]);n!==void 0&&M!=null&&r(n,["cachedContent"],X(t,M));const _=i(e,["responseModalities"]);_!=null&&r(o,["responseModalities"],_);const G=i(e,["mediaResolution"]);G!=null&&r(o,["mediaResolution"],G);const S=i(e,["speechConfig"]);if(S!=null&&r(o,["speechConfig"],ir(Tt(S))),i(e,["audioTimestamp"])!==void 0)throw new Error("audioTimestamp parameter is not supported in Gemini API.");const k=i(e,["thinkingConfig"]);return k!=null&&r(o,["thinkingConfig"],rr(k)),o}function ar(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["request","model"],I(t,o));const s=i(e,["contents"]);if(s!=null){let l=H(s);Array.isArray(l)&&(l=l.map(c=>Io(c))),r(n,["request","contents"],l)}const a=i(e,["config"]);return a!=null&&r(n,["request","generationConfig"],sr(t,a,n)),n}function lr(t,e){const n={};if(i(e,["format"])!==void 0)throw new Error("format parameter is not supported in Gemini API.");if(i(e,["gcsUri"])!==void 0)throw new Error("gcsUri parameter is not supported in Gemini API.");if(i(e,["bigqueryUri"])!==void 0)throw new Error("bigqueryUri parameter is not supported in Gemini API.");const o=i(e,["fileName"]);o!=null&&r(n,["fileName"],o);const s=i(e,["inlinedRequests"]);if(s!=null){let a=s;Array.isArray(a)&&(a=a.map(l=>ar(t,l))),r(n,["requests","requests"],a)}return n}function cr(t,e){const n={},o=i(t,["displayName"]);if(e!==void 0&&o!=null&&r(e,["batch","displayName"],o),i(t,["dest"])!==void 0)throw new Error("dest parameter is not supported in Gemini API.");return n}function ur(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["src"]);s!=null&&r(n,["batch","inputConfig"],lr(t,_o(t,s)));const a=i(e,["config"]);return a!=null&&r(n,["config"],cr(a,n)),n}function dr(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],ve(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function fr(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],ve(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function pr(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);if(e!==void 0&&s!=null&&r(e,["_query","pageToken"],s),i(t,["filter"])!==void 0)throw new Error("filter parameter is not supported in Gemini API.");return n}function hr(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],pr(n,e)),e}function mr(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],ve(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function gr(t){const e={},n=i(t,["format"]);n!=null&&r(e,["instancesFormat"],n);const o=i(t,["gcsUri"]);o!=null&&r(e,["gcsSource","uris"],o);const s=i(t,["bigqueryUri"]);if(s!=null&&r(e,["bigquerySource","inputUri"],s),i(t,["fileName"])!==void 0)throw new Error("fileName parameter is not supported in Vertex AI.");if(i(t,["inlinedRequests"])!==void 0)throw new Error("inlinedRequests parameter is not supported in Vertex AI.");return e}function yr(t){const e={},n=i(t,["format"]);n!=null&&r(e,["predictionsFormat"],n);const o=i(t,["gcsUri"]);o!=null&&r(e,["gcsDestination","outputUriPrefix"],o);const s=i(t,["bigqueryUri"]);if(s!=null&&r(e,["bigqueryDestination","outputUri"],s),i(t,["fileName"])!==void 0)throw new Error("fileName parameter is not supported in Vertex AI.");if(i(t,["inlinedResponses"])!==void 0)throw new Error("inlinedResponses parameter is not supported in Vertex AI.");return e}function vr(t,e){const n={},o=i(t,["displayName"]);e!==void 0&&o!=null&&r(e,["displayName"],o);const s=i(t,["dest"]);return e!==void 0&&s!=null&&r(e,["outputConfig"],yr(Ui(s))),n}function xr(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["model"],I(t,o));const s=i(e,["src"]);s!=null&&r(n,["inputConfig"],gr(_o(t,s)));const a=i(e,["config"]);return a!=null&&r(n,["config"],vr(a,n)),n}function Cr(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],ve(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Tr(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],ve(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function wr(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);e!==void 0&&s!=null&&r(e,["_query","pageToken"],s);const a=i(t,["filter"]);return e!==void 0&&a!=null&&r(e,["_query","filter"],a),n}function br(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],wr(n,e)),e}function Er(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],ve(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Ro(){return{}}function Ar(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Sr(t){const e={},n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Mr(t){const e={},n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function _r(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Ar(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],Sr(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],Mr(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Pr(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>_r(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Ir(t){const e={},n=i(t,["citationSources"]);return n!=null&&r(e,["citations"],n),e}function kr(t){const e={},n=i(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const o=i(t,["urlRetrievalStatus"]);return o!=null&&r(e,["urlRetrievalStatus"],o),e}function Rr(t){const e={},n=i(t,["urlMetadata"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>kr(s))),r(e,["urlMetadata"],o)}return e}function Dr(t){const e={},n=i(t,["content"]);n!=null&&r(e,["content"],Pr(n));const o=i(t,["citationMetadata"]);o!=null&&r(e,["citationMetadata"],Ir(o));const s=i(t,["tokenCount"]);s!=null&&r(e,["tokenCount"],s);const a=i(t,["finishReason"]);a!=null&&r(e,["finishReason"],a);const l=i(t,["urlContextMetadata"]);l!=null&&r(e,["urlContextMetadata"],Rr(l));const c=i(t,["avgLogprobs"]);c!=null&&r(e,["avgLogprobs"],c);const d=i(t,["groundingMetadata"]);d!=null&&r(e,["groundingMetadata"],d);const u=i(t,["index"]);u!=null&&r(e,["index"],u);const f=i(t,["logprobsResult"]);f!=null&&r(e,["logprobsResult"],f);const p=i(t,["safetyRatings"]);return p!=null&&r(e,["safetyRatings"],p),e}function Nr(t){const e={},n=i(t,["candidates"]);if(n!=null){let l=n;Array.isArray(l)&&(l=l.map(c=>Dr(c))),r(e,["candidates"],l)}const o=i(t,["modelVersion"]);o!=null&&r(e,["modelVersion"],o);const s=i(t,["promptFeedback"]);s!=null&&r(e,["promptFeedback"],s);const a=i(t,["usageMetadata"]);return a!=null&&r(e,["usageMetadata"],a),e}function Fr(t){const e={},n=i(t,["response"]);return n!=null&&r(e,["response"],Nr(n)),i(t,["error"])!=null&&r(e,["error"],Ro()),e}function Lr(t){const e={},n=i(t,["responsesFile"]);n!=null&&r(e,["fileName"],n);const o=i(t,["inlinedResponses","inlinedResponses"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(a=>Fr(a))),r(e,["inlinedResponses"],s)}return e}function rt(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["metadata","displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["metadata","state"]);s!=null&&r(e,["state"],Po(s));const a=i(t,["metadata","createTime"]);a!=null&&r(e,["createTime"],a);const l=i(t,["metadata","endTime"]);l!=null&&r(e,["endTime"],l);const c=i(t,["metadata","updateTime"]);c!=null&&r(e,["updateTime"],c);const d=i(t,["metadata","model"]);d!=null&&r(e,["model"],d);const u=i(t,["metadata","output"]);return u!=null&&r(e,["dest"],Lr(u)),e}function Ur(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["operations"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(a=>rt(a))),r(e,["batchJobs"],s)}return e}function $r(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["done"]);return o!=null&&r(e,["done"],o),i(t,["error"])!=null&&r(e,["error"],Ro()),e}function Do(t){const e={},n=i(t,["details"]);n!=null&&r(e,["details"],n);const o=i(t,["code"]);o!=null&&r(e,["code"],o);const s=i(t,["message"]);return s!=null&&r(e,["message"],s),e}function Vr(t){const e={},n=i(t,["instancesFormat"]);n!=null&&r(e,["format"],n);const o=i(t,["gcsSource","uris"]);o!=null&&r(e,["gcsUri"],o);const s=i(t,["bigquerySource","inputUri"]);return s!=null&&r(e,["bigqueryUri"],s),e}function Gr(t){const e={},n=i(t,["predictionsFormat"]);n!=null&&r(e,["format"],n);const o=i(t,["gcsDestination","outputUriPrefix"]);o!=null&&r(e,["gcsUri"],o);const s=i(t,["bigqueryDestination","outputUri"]);return s!=null&&r(e,["bigqueryUri"],s),e}function st(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["state"]);s!=null&&r(e,["state"],Po(s));const a=i(t,["error"]);a!=null&&r(e,["error"],Do(a));const l=i(t,["createTime"]);l!=null&&r(e,["createTime"],l);const c=i(t,["startTime"]);c!=null&&r(e,["startTime"],c);const d=i(t,["endTime"]);d!=null&&r(e,["endTime"],d);const u=i(t,["updateTime"]);u!=null&&r(e,["updateTime"],u);const f=i(t,["model"]);f!=null&&r(e,["model"],f);const p=i(t,["inputConfig"]);p!=null&&r(e,["src"],Vr(p));const h=i(t,["outputConfig"]);return h!=null&&r(e,["dest"],Gr(h)),e}function Br(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["batchPredictionJobs"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(a=>st(a))),r(e,["batchJobs"],s)}return e}function qr(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["done"]);o!=null&&r(e,["done"],o);const s=i(t,["error"]);return s!=null&&r(e,["error"],Do(s)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */var se;(function(t){t.PAGED_ITEM_BATCH_JOBS="batchJobs",t.PAGED_ITEM_MODELS="models",t.PAGED_ITEM_TUNING_JOBS="tuningJobs",t.PAGED_ITEM_FILES="files",t.PAGED_ITEM_CACHED_CONTENTS="cachedContents"})(se||(se={}));class ke{constructor(e,n,o,s){this.pageInternal=[],this.paramsInternal={},this.requestInternal=n,this.init(e,o,s)}init(e,n,o){var s,a;this.nameInternal=e,this.pageInternal=n[this.nameInternal]||[],this.idxInternal=0;let l={config:{}};o?typeof o=="object"?l=Object.assign({},o):l=o:l={config:{}},l.config&&(l.config.pageToken=n.nextPageToken),this.paramsInternal=l,this.pageInternalSize=(a=(s=l.config)===null||s===void 0?void 0:s.pageSize)!==null&&a!==void 0?a:this.pageInternal.length}initNextPage(e){this.init(this.nameInternal,e,this.paramsInternal)}get page(){return this.pageInternal}get name(){return this.nameInternal}get pageSize(){return this.pageInternalSize}get params(){return this.paramsInternal}get pageLength(){return this.pageInternal.length}getItem(e){return this.pageInternal[e]}[Symbol.asyncIterator](){return{next:async()=>{if(this.idxInternal>=this.pageLength)if(this.hasNextPage())await this.nextPage();else return{value:void 0,done:!0};const e=this.getItem(this.idxInternal);return this.idxInternal+=1,{value:e,done:!1}},return:async()=>({value:void 0,done:!0})}}async nextPage(){if(!this.hasNextPage())throw new Error("No more pages to fetch.");const e=await this.requestInternal(this.params);return this.initNextPage(e),this.page}hasNextPage(){var e;return((e=this.params.config)===null||e===void 0?void 0:e.pageToken)!==void 0}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class zr extends le{constructor(e){super(),this.apiClient=e,this.create=async n=>{if(this.apiClient.isVertexAI()){const s=Date.now().toString();if(Array.isArray(n.src))throw new Error("InlinedRequest[] is not supported in Vertex AI. Please use Google Cloud Storage URI or BigQuery URI instead.");if(n.config=n.config||{},n.config.displayName===void 0&&(n.config.displayName="genaiBatchJob_${timestampStr}"),n.config.dest===void 0&&typeof n.src=="string")if(n.src.startsWith("gs://")&&n.src.endsWith(".jsonl"))n.config.dest=`${n.src.slice(0,-6)}/dest`;else if(n.src.startsWith("bq://"))n.config.dest=`${n.src}_dest_${s}`;else throw new Error("Unsupported source:"+n.src)}return await this.createInternal(n)},this.list=async(n={})=>new ke(se.PAGED_ITEM_BATCH_JOBS,o=>this.listInternal(o),await this.listInternal(n),n)}async createInternal(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=xr(this.apiClient,e);return c=x("batchPredictionJobs",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>st(f))}else{const u=ur(this.apiClient,e);return c=x("{model}:batchGenerateContent",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>rt(f))}}async get(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Cr(this.apiClient,e);return c=x("batchPredictionJobs/{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>st(f))}else{const u=dr(this.apiClient,e);return c=x("batches/{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>rt(f))}}async cancel(e){var n,o,s,a;let l="",c={};if(this.apiClient.isVertexAI()){const d=Tr(this.apiClient,e);l=x("batchPredictionJobs/{name}:cancel",d._url),c=d._query,delete d.config,delete d._url,delete d._query,await this.apiClient.request({path:l,queryParams:c,body:JSON.stringify(d),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal})}else{const d=fr(this.apiClient,e);l=x("batches/{name}:cancel",d._url),c=d._query,delete d.config,delete d._url,delete d._query,await this.apiClient.request({path:l,queryParams:c,body:JSON.stringify(d),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal})}}async listInternal(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=br(e);return c=x("batchPredictionJobs",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>{const p=Br(f),h=new Dn;return Object.assign(h,p),h})}else{const u=hr(e);return c=x("batches",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>{const p=Ur(f),h=new Dn;return Object.assign(h,p),h})}}async delete(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Er(this.apiClient,e);return c=x("batchPredictionJobs/{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>qr(f))}else{const u=mr(this.apiClient,e);return c=x("batches/{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"DELETE",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>$r(f))}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Hr(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Jr(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Wr(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Yr(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Hr(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],Jr(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],Wr(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Un(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Yr(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Kr(t){const e={},n=i(t,["behavior"]);n!=null&&r(e,["behavior"],n);const o=i(t,["description"]);o!=null&&r(e,["description"],o);const s=i(t,["name"]);s!=null&&r(e,["name"],s);const a=i(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=i(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const c=i(t,["response"]);c!=null&&r(e,["response"],c);const d=i(t,["responseJsonSchema"]);return d!=null&&r(e,["responseJsonSchema"],d),e}function Or(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function Xr(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Or(n)),e}function Qr(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function Zr(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Qr(n)),e}function jr(){return{}}function es(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(u=>Kr(u))),r(e,["functionDeclarations"],d)}if(i(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const o=i(t,["googleSearch"]);o!=null&&r(e,["googleSearch"],Xr(o));const s=i(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],Zr(s)),i(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(i(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");i(t,["urlContext"])!=null&&r(e,["urlContext"],jr());const l=i(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const c=i(t,["computerUse"]);return c!=null&&r(e,["computerUse"],c),e}function ts(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["allowedFunctionNames"]);return o!=null&&r(e,["allowedFunctionNames"],o),e}function ns(t){const e={},n=i(t,["latitude"]);n!=null&&r(e,["latitude"],n);const o=i(t,["longitude"]);return o!=null&&r(e,["longitude"],o),e}function os(t){const e={},n=i(t,["latLng"]);n!=null&&r(e,["latLng"],ns(n));const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function is(t){const e={},n=i(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],ts(n));const o=i(t,["retrievalConfig"]);return o!=null&&r(e,["retrievalConfig"],os(o)),e}function rs(t,e){const n={},o=i(t,["ttl"]);e!==void 0&&o!=null&&r(e,["ttl"],o);const s=i(t,["expireTime"]);e!==void 0&&s!=null&&r(e,["expireTime"],s);const a=i(t,["displayName"]);e!==void 0&&a!=null&&r(e,["displayName"],a);const l=i(t,["contents"]);if(e!==void 0&&l!=null){let f=H(l);Array.isArray(f)&&(f=f.map(p=>Un(p))),r(e,["contents"],f)}const c=i(t,["systemInstruction"]);e!==void 0&&c!=null&&r(e,["systemInstruction"],Un($(c)));const d=i(t,["tools"]);if(e!==void 0&&d!=null){let f=d;Array.isArray(f)&&(f=f.map(p=>es(p))),r(e,["tools"],f)}const u=i(t,["toolConfig"]);if(e!==void 0&&u!=null&&r(e,["toolConfig"],is(u)),i(t,["kmsKeyName"])!==void 0)throw new Error("kmsKeyName parameter is not supported in Gemini API.");return n}function ss(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["model"],vo(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],rs(s,n)),n}function as(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],X(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function ls(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],X(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function cs(t,e){const n={},o=i(t,["ttl"]);e!==void 0&&o!=null&&r(e,["ttl"],o);const s=i(t,["expireTime"]);return e!==void 0&&s!=null&&r(e,["expireTime"],s),n}function us(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],X(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],cs(s,n)),n}function ds(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);return e!==void 0&&s!=null&&r(e,["_query","pageToken"],s),n}function fs(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],ds(n,e)),e}function ps(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function hs(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["data"]);o!=null&&r(e,["data"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function ms(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["fileUri"]);o!=null&&r(e,["fileUri"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function gs(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],ps(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],hs(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],ms(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function $n(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>gs(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function ys(t){const e={};if(i(t,["behavior"])!==void 0)throw new Error("behavior parameter is not supported in Vertex AI.");const n=i(t,["description"]);n!=null&&r(e,["description"],n);const o=i(t,["name"]);o!=null&&r(e,["name"],o);const s=i(t,["parameters"]);s!=null&&r(e,["parameters"],s);const a=i(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const l=i(t,["response"]);l!=null&&r(e,["response"],l);const c=i(t,["responseJsonSchema"]);return c!=null&&r(e,["responseJsonSchema"],c),e}function vs(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function xs(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],vs(n)),e}function Cs(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function Ts(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Cs(n)),e}function ws(){return{}}function bs(t){const e={},n=i(t,["apiKeyString"]);return n!=null&&r(e,["apiKeyString"],n),e}function Es(t){const e={},n=i(t,["apiKeyConfig"]);n!=null&&r(e,["apiKeyConfig"],bs(n));const o=i(t,["authType"]);o!=null&&r(e,["authType"],o);const s=i(t,["googleServiceAccountConfig"]);s!=null&&r(e,["googleServiceAccountConfig"],s);const a=i(t,["httpBasicAuthConfig"]);a!=null&&r(e,["httpBasicAuthConfig"],a);const l=i(t,["oauthConfig"]);l!=null&&r(e,["oauthConfig"],l);const c=i(t,["oidcConfig"]);return c!=null&&r(e,["oidcConfig"],c),e}function As(t){const e={},n=i(t,["authConfig"]);return n!=null&&r(e,["authConfig"],Es(n)),e}function Ss(){return{}}function Ms(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let p=n;Array.isArray(p)&&(p=p.map(h=>ys(h))),r(e,["functionDeclarations"],p)}const o=i(t,["retrieval"]);o!=null&&r(e,["retrieval"],o);const s=i(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],xs(s));const a=i(t,["googleSearchRetrieval"]);a!=null&&r(e,["googleSearchRetrieval"],Ts(a)),i(t,["enterpriseWebSearch"])!=null&&r(e,["enterpriseWebSearch"],ws());const c=i(t,["googleMaps"]);c!=null&&r(e,["googleMaps"],As(c)),i(t,["urlContext"])!=null&&r(e,["urlContext"],Ss());const u=i(t,["codeExecution"]);u!=null&&r(e,["codeExecution"],u);const f=i(t,["computerUse"]);return f!=null&&r(e,["computerUse"],f),e}function _s(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["allowedFunctionNames"]);return o!=null&&r(e,["allowedFunctionNames"],o),e}function Ps(t){const e={},n=i(t,["latitude"]);n!=null&&r(e,["latitude"],n);const o=i(t,["longitude"]);return o!=null&&r(e,["longitude"],o),e}function Is(t){const e={},n=i(t,["latLng"]);n!=null&&r(e,["latLng"],Ps(n));const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function ks(t){const e={},n=i(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],_s(n));const o=i(t,["retrievalConfig"]);return o!=null&&r(e,["retrievalConfig"],Is(o)),e}function Rs(t,e){const n={},o=i(t,["ttl"]);e!==void 0&&o!=null&&r(e,["ttl"],o);const s=i(t,["expireTime"]);e!==void 0&&s!=null&&r(e,["expireTime"],s);const a=i(t,["displayName"]);e!==void 0&&a!=null&&r(e,["displayName"],a);const l=i(t,["contents"]);if(e!==void 0&&l!=null){let p=H(l);Array.isArray(p)&&(p=p.map(h=>$n(h))),r(e,["contents"],p)}const c=i(t,["systemInstruction"]);e!==void 0&&c!=null&&r(e,["systemInstruction"],$n($(c)));const d=i(t,["tools"]);if(e!==void 0&&d!=null){let p=d;Array.isArray(p)&&(p=p.map(h=>Ms(h))),r(e,["tools"],p)}const u=i(t,["toolConfig"]);e!==void 0&&u!=null&&r(e,["toolConfig"],ks(u));const f=i(t,["kmsKeyName"]);return e!==void 0&&f!=null&&r(e,["encryption_spec","kmsKeyName"],f),n}function Ds(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["model"],vo(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],Rs(s,n)),n}function Ns(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],X(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Fs(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],X(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Ls(t,e){const n={},o=i(t,["ttl"]);e!==void 0&&o!=null&&r(e,["ttl"],o);const s=i(t,["expireTime"]);return e!==void 0&&s!=null&&r(e,["expireTime"],s),n}function Us(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],X(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],Ls(s,n)),n}function $s(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);return e!==void 0&&s!=null&&r(e,["_query","pageToken"],s),n}function Vs(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],$s(n,e)),e}function Le(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["model"]);s!=null&&r(e,["model"],s);const a=i(t,["createTime"]);a!=null&&r(e,["createTime"],a);const l=i(t,["updateTime"]);l!=null&&r(e,["updateTime"],l);const c=i(t,["expireTime"]);c!=null&&r(e,["expireTime"],c);const d=i(t,["usageMetadata"]);return d!=null&&r(e,["usageMetadata"],d),e}function Gs(){return{}}function Bs(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["cachedContents"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(a=>Le(a))),r(e,["cachedContents"],s)}return e}function Ue(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["model"]);s!=null&&r(e,["model"],s);const a=i(t,["createTime"]);a!=null&&r(e,["createTime"],a);const l=i(t,["updateTime"]);l!=null&&r(e,["updateTime"],l);const c=i(t,["expireTime"]);c!=null&&r(e,["expireTime"],c);const d=i(t,["usageMetadata"]);return d!=null&&r(e,["usageMetadata"],d),e}function qs(){return{}}function zs(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["cachedContents"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(a=>Ue(a))),r(e,["cachedContents"],s)}return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class Hs extends le{constructor(e){super(),this.apiClient=e,this.list=async(n={})=>new ke(se.PAGED_ITEM_CACHED_CONTENTS,o=>this.listInternal(o),await this.listInternal(n),n)}async create(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Ds(this.apiClient,e);return c=x("cachedContents",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>Ue(f))}else{const u=ss(this.apiClient,e);return c=x("cachedContents",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>Le(f))}}async get(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Ns(this.apiClient,e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>Ue(f))}else{const u=as(this.apiClient,e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>Le(f))}}async delete(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Fs(this.apiClient,e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(()=>{const f=qs(),p=new kn;return Object.assign(p,f),p})}else{const u=ls(this.apiClient,e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"DELETE",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(()=>{const f=Gs(),p=new kn;return Object.assign(p,f),p})}}async update(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Us(this.apiClient,e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"PATCH",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>Ue(f))}else{const u=us(this.apiClient,e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"PATCH",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>Le(f))}}async listInternal(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Vs(e);return c=x("cachedContents",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>{const p=zs(f),h=new Rn;return Object.assign(h,p),h})}else{const u=fs(e);return c=x("cachedContents",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>{const p=Bs(f),h=new Rn;return Object.assign(h,p),h})}}}function Vn(t){var e=typeof Symbol=="function"&&Symbol.iterator,n=e&&t[e],o=0;if(n)return n.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function D(t){return this instanceof D?(this.v=t,this):new D(t)}function fe(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o=n.apply(t,e||[]),s,a=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),c("next"),c("throw"),c("return",l),s[Symbol.asyncIterator]=function(){return this},s;function l(m){return function(g){return Promise.resolve(g).then(m,p)}}function c(m,g){o[m]&&(s[m]=function(y){return new Promise(function(v,C){a.push([m,y,v,C])>1||d(m,y)})},g&&(s[m]=g(s[m])))}function d(m,g){try{u(o[m](g))}catch(y){h(a[0][3],y)}}function u(m){m.value instanceof D?Promise.resolve(m.value.v).then(f,p):h(a[0][2],m)}function f(m){d("next",m)}function p(m){d("throw",m)}function h(m,g){m(g),a.shift(),a.length&&d(a[0][0],a[0][1])}}function Ee(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],n;return e?e.call(t):(t=typeof Vn=="function"?Vn(t):t[Symbol.iterator](),n={},o("next"),o("throw"),o("return"),n[Symbol.asyncIterator]=function(){return this},n);function o(a){n[a]=t[a]&&function(l){return new Promise(function(c,d){l=t[a](l),s(c,d,l.done,l.value)})}}function s(a,l,c,d){Promise.resolve(d).then(function(u){a({value:u,done:c})},l)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Js(t){var e;if(t.candidates==null||t.candidates.length===0)return!1;const n=(e=t.candidates[0])===null||e===void 0?void 0:e.content;return n===void 0?!1:No(n)}function No(t){if(t.parts===void 0||t.parts.length===0)return!1;for(const e of t.parts)if(e===void 0||Object.keys(e).length===0||!e.thought&&e.text!==void 0&&e.text==="")return!1;return!0}function Ws(t){if(t.length!==0){for(const e of t)if(e.role!=="user"&&e.role!=="model")throw new Error(`Role must be user or model, but got ${e.role}.`)}}function Gn(t){if(t===void 0||t.length===0)return[];const e=[],n=t.length;let o=0;for(;o<n;)if(t[o].role==="user")e.push(t[o]),o++;else{const s=[];let a=!0;for(;o<n&&t[o].role==="model";)s.push(t[o]),a&&!No(t[o])&&(a=!1),o++;a?e.push(...s):e.pop()}return e}class Ys{constructor(e,n){this.modelsModule=e,this.apiClient=n}create(e){return new Ks(this.apiClient,this.modelsModule,e.model,e.config,structuredClone(e.history))}}class Ks{constructor(e,n,o,s={},a=[]){this.apiClient=e,this.modelsModule=n,this.model=o,this.config=s,this.history=a,this.sendPromise=Promise.resolve(),Ws(a)}async sendMessage(e){var n;await this.sendPromise;const o=$(e.message),s=this.modelsModule.generateContent({model:this.model,contents:this.getHistory(!0).concat(o),config:(n=e.config)!==null&&n!==void 0?n:this.config});return this.sendPromise=(async()=>{var a,l,c;const d=await s,u=(l=(a=d.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content,f=d.automaticFunctionCallingHistory,p=this.getHistory(!0).length;let h=[];f!=null&&(h=(c=f.slice(p))!==null&&c!==void 0?c:[]);const m=u?[u]:[];this.recordHistory(o,m,h)})(),await this.sendPromise.catch(()=>{this.sendPromise=Promise.resolve()}),s}async sendMessageStream(e){var n;await this.sendPromise;const o=$(e.message),s=this.modelsModule.generateContentStream({model:this.model,contents:this.getHistory(!0).concat(o),config:(n=e.config)!==null&&n!==void 0?n:this.config});this.sendPromise=s.then(()=>{}).catch(()=>{});const a=await s;return this.processStreamResponse(a,o)}getHistory(e=!1){const n=e?Gn(this.history):this.history;return structuredClone(n)}processStreamResponse(e,n){var o,s;return fe(this,arguments,function*(){var l,c,d,u;const f=[];try{for(var p=!0,h=Ee(e),m;m=yield D(h.next()),l=m.done,!l;p=!0){u=m.value,p=!1;const g=u;if(Js(g)){const y=(s=(o=g.candidates)===null||o===void 0?void 0:o[0])===null||s===void 0?void 0:s.content;y!==void 0&&f.push(y)}yield yield D(g)}}catch(g){c={error:g}}finally{try{!p&&!l&&(d=h.return)&&(yield D(d.call(h)))}finally{if(c)throw c.error}}this.recordHistory(n,f)})}recordHistory(e,n,o){let s=[];n.length>0&&n.every(a=>a.role!==void 0)?s=n:s.push({role:"model",parts:[]}),o&&o.length>0?this.history.push(...Gn(o)):this.history.push(e),this.history.push(...s)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class Je extends Error{constructor(e){super(e.message),this.name="ApiError",this.status=e.status,Object.setPrototypeOf(this,Je.prototype)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Os(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);return e!==void 0&&s!=null&&r(e,["_query","pageToken"],s),n}function Xs(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],Os(n,e)),e}function Qs(t){const e={},n=i(t,["details"]);n!=null&&r(e,["details"],n);const o=i(t,["message"]);o!=null&&r(e,["message"],o);const s=i(t,["code"]);return s!=null&&r(e,["code"],s),e}function Zs(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["mimeType"]);s!=null&&r(e,["mimeType"],s);const a=i(t,["sizeBytes"]);a!=null&&r(e,["sizeBytes"],a);const l=i(t,["createTime"]);l!=null&&r(e,["createTime"],l);const c=i(t,["expirationTime"]);c!=null&&r(e,["expirationTime"],c);const d=i(t,["updateTime"]);d!=null&&r(e,["updateTime"],d);const u=i(t,["sha256Hash"]);u!=null&&r(e,["sha256Hash"],u);const f=i(t,["uri"]);f!=null&&r(e,["uri"],f);const p=i(t,["downloadUri"]);p!=null&&r(e,["downloadUri"],p);const h=i(t,["state"]);h!=null&&r(e,["state"],h);const m=i(t,["source"]);m!=null&&r(e,["source"],m);const g=i(t,["videoMetadata"]);g!=null&&r(e,["videoMetadata"],g);const y=i(t,["error"]);return y!=null&&r(e,["error"],Qs(y)),e}function js(t){const e={},n=i(t,["file"]);n!=null&&r(e,["file"],Zs(n));const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function ea(t){const e={},n=i(t,["name"]);n!=null&&r(e,["_url","file"],Ao(n));const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function ta(t){const e={},n=i(t,["name"]);n!=null&&r(e,["_url","file"],Ao(n));const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function na(t){const e={},n=i(t,["details"]);n!=null&&r(e,["details"],n);const o=i(t,["message"]);o!=null&&r(e,["message"],o);const s=i(t,["code"]);return s!=null&&r(e,["code"],s),e}function at(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["mimeType"]);s!=null&&r(e,["mimeType"],s);const a=i(t,["sizeBytes"]);a!=null&&r(e,["sizeBytes"],a);const l=i(t,["createTime"]);l!=null&&r(e,["createTime"],l);const c=i(t,["expirationTime"]);c!=null&&r(e,["expirationTime"],c);const d=i(t,["updateTime"]);d!=null&&r(e,["updateTime"],d);const u=i(t,["sha256Hash"]);u!=null&&r(e,["sha256Hash"],u);const f=i(t,["uri"]);f!=null&&r(e,["uri"],f);const p=i(t,["downloadUri"]);p!=null&&r(e,["downloadUri"],p);const h=i(t,["state"]);h!=null&&r(e,["state"],h);const m=i(t,["source"]);m!=null&&r(e,["source"],m);const g=i(t,["videoMetadata"]);g!=null&&r(e,["videoMetadata"],g);const y=i(t,["error"]);return y!=null&&r(e,["error"],na(y)),e}function oa(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["files"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(a=>at(a))),r(e,["files"],s)}return e}function ia(){return{}}function ra(){return{}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class sa extends le{constructor(e){super(),this.apiClient=e,this.list=async(n={})=>new ke(se.PAGED_ITEM_FILES,o=>this.listInternal(o),await this.listInternal(n),n)}async upload(e){if(this.apiClient.isVertexAI())throw new Error("Vertex AI does not support uploading files. You can share files through a GCS bucket.");return this.apiClient.uploadFile(e.file,e.config).then(n=>at(n))}async download(e){await this.apiClient.downloadFile(e)}async listInternal(e){var n,o;let s,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const c=Xs(e);return a=x("files",c._url),l=c._query,delete c.config,delete c._url,delete c._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),s.then(d=>{const u=oa(d),f=new Ei;return Object.assign(f,u),f})}}async createInternal(e){var n,o;let s,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const c=js(e);return a=x("upload/v1beta/files",c._url),l=c._query,delete c.config,delete c._url,delete c._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),s.then(()=>{const d=ia(),u=new Ai;return Object.assign(u,d),u})}}async get(e){var n,o;let s,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const c=ea(e);return a=x("files/{file}",c._url),l=c._query,delete c.config,delete c._url,delete c._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),s.then(d=>at(d))}}async delete(e){var n,o;let s,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const c=ta(e);return a=x("files/{file}",c._url),l=c._query,delete c.config,delete c._url,delete c._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),s.then(()=>{const d=ra(),u=new Si;return Object.assign(u,d),u})}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function aa(t){const e={},n=i(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function la(t){const e={},n=i(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function Fo(t){const e={},n=i(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],aa(n)),e}function ca(t){const e={},n=i(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],la(n)),e}function ua(t){const e={},n=i(t,["speaker"]);n!=null&&r(e,["speaker"],n);const o=i(t,["voiceConfig"]);return o!=null&&r(e,["voiceConfig"],Fo(o)),e}function da(t){const e={},n=i(t,["speakerVoiceConfigs"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>ua(s))),r(e,["speakerVoiceConfigs"],o)}return e}function fa(t){const e={},n=i(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],Fo(n));const o=i(t,["multiSpeakerVoiceConfig"]);o!=null&&r(e,["multiSpeakerVoiceConfig"],da(o));const s=i(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function pa(t){const e={},n=i(t,["voiceConfig"]);if(n!=null&&r(e,["voiceConfig"],ca(n)),i(t,["multiSpeakerVoiceConfig"])!==void 0)throw new Error("multiSpeakerVoiceConfig parameter is not supported in Vertex AI.");const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function ha(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function ma(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function ga(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function ya(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["data"]);o!=null&&r(e,["data"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function va(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function xa(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["fileUri"]);o!=null&&r(e,["fileUri"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Ca(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],ha(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],ga(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],va(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Ta(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],ma(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],ya(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],xa(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function wa(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Ca(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function ba(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Ta(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Ea(t){const e={},n=i(t,["behavior"]);n!=null&&r(e,["behavior"],n);const o=i(t,["description"]);o!=null&&r(e,["description"],o);const s=i(t,["name"]);s!=null&&r(e,["name"],s);const a=i(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=i(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const c=i(t,["response"]);c!=null&&r(e,["response"],c);const d=i(t,["responseJsonSchema"]);return d!=null&&r(e,["responseJsonSchema"],d),e}function Aa(t){const e={};if(i(t,["behavior"])!==void 0)throw new Error("behavior parameter is not supported in Vertex AI.");const n=i(t,["description"]);n!=null&&r(e,["description"],n);const o=i(t,["name"]);o!=null&&r(e,["name"],o);const s=i(t,["parameters"]);s!=null&&r(e,["parameters"],s);const a=i(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const l=i(t,["response"]);l!=null&&r(e,["response"],l);const c=i(t,["responseJsonSchema"]);return c!=null&&r(e,["responseJsonSchema"],c),e}function Sa(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function Ma(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function _a(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Sa(n)),e}function Pa(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Ma(n)),e}function Ia(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function ka(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function Ra(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Ia(n)),e}function Da(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],ka(n)),e}function Na(){return{}}function Fa(t){const e={},n=i(t,["apiKeyString"]);return n!=null&&r(e,["apiKeyString"],n),e}function La(t){const e={},n=i(t,["apiKeyConfig"]);n!=null&&r(e,["apiKeyConfig"],Fa(n));const o=i(t,["authType"]);o!=null&&r(e,["authType"],o);const s=i(t,["googleServiceAccountConfig"]);s!=null&&r(e,["googleServiceAccountConfig"],s);const a=i(t,["httpBasicAuthConfig"]);a!=null&&r(e,["httpBasicAuthConfig"],a);const l=i(t,["oauthConfig"]);l!=null&&r(e,["oauthConfig"],l);const c=i(t,["oidcConfig"]);return c!=null&&r(e,["oidcConfig"],c),e}function Ua(t){const e={},n=i(t,["authConfig"]);return n!=null&&r(e,["authConfig"],La(n)),e}function $a(){return{}}function Va(){return{}}function Ga(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(u=>Ea(u))),r(e,["functionDeclarations"],d)}if(i(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const o=i(t,["googleSearch"]);o!=null&&r(e,["googleSearch"],_a(o));const s=i(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],Ra(s)),i(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(i(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");i(t,["urlContext"])!=null&&r(e,["urlContext"],$a());const l=i(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const c=i(t,["computerUse"]);return c!=null&&r(e,["computerUse"],c),e}function Ba(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let p=n;Array.isArray(p)&&(p=p.map(h=>Aa(h))),r(e,["functionDeclarations"],p)}const o=i(t,["retrieval"]);o!=null&&r(e,["retrieval"],o);const s=i(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],Pa(s));const a=i(t,["googleSearchRetrieval"]);a!=null&&r(e,["googleSearchRetrieval"],Da(a)),i(t,["enterpriseWebSearch"])!=null&&r(e,["enterpriseWebSearch"],Na());const c=i(t,["googleMaps"]);c!=null&&r(e,["googleMaps"],Ua(c)),i(t,["urlContext"])!=null&&r(e,["urlContext"],Va());const u=i(t,["codeExecution"]);u!=null&&r(e,["codeExecution"],u);const f=i(t,["computerUse"]);return f!=null&&r(e,["computerUse"],f),e}function qa(t){const e={},n=i(t,["handle"]);if(n!=null&&r(e,["handle"],n),i(t,["transparent"])!==void 0)throw new Error("transparent parameter is not supported in Gemini API.");return e}function za(t){const e={},n=i(t,["handle"]);n!=null&&r(e,["handle"],n);const o=i(t,["transparent"]);return o!=null&&r(e,["transparent"],o),e}function Bn(){return{}}function qn(){return{}}function Ha(t){const e={},n=i(t,["disabled"]);n!=null&&r(e,["disabled"],n);const o=i(t,["startOfSpeechSensitivity"]);o!=null&&r(e,["startOfSpeechSensitivity"],o);const s=i(t,["endOfSpeechSensitivity"]);s!=null&&r(e,["endOfSpeechSensitivity"],s);const a=i(t,["prefixPaddingMs"]);a!=null&&r(e,["prefixPaddingMs"],a);const l=i(t,["silenceDurationMs"]);return l!=null&&r(e,["silenceDurationMs"],l),e}function Ja(t){const e={},n=i(t,["disabled"]);n!=null&&r(e,["disabled"],n);const o=i(t,["startOfSpeechSensitivity"]);o!=null&&r(e,["startOfSpeechSensitivity"],o);const s=i(t,["endOfSpeechSensitivity"]);s!=null&&r(e,["endOfSpeechSensitivity"],s);const a=i(t,["prefixPaddingMs"]);a!=null&&r(e,["prefixPaddingMs"],a);const l=i(t,["silenceDurationMs"]);return l!=null&&r(e,["silenceDurationMs"],l),e}function Wa(t){const e={},n=i(t,["automaticActivityDetection"]);n!=null&&r(e,["automaticActivityDetection"],Ha(n));const o=i(t,["activityHandling"]);o!=null&&r(e,["activityHandling"],o);const s=i(t,["turnCoverage"]);return s!=null&&r(e,["turnCoverage"],s),e}function Ya(t){const e={},n=i(t,["automaticActivityDetection"]);n!=null&&r(e,["automaticActivityDetection"],Ja(n));const o=i(t,["activityHandling"]);o!=null&&r(e,["activityHandling"],o);const s=i(t,["turnCoverage"]);return s!=null&&r(e,["turnCoverage"],s),e}function Ka(t){const e={},n=i(t,["targetTokens"]);return n!=null&&r(e,["targetTokens"],n),e}function Oa(t){const e={},n=i(t,["targetTokens"]);return n!=null&&r(e,["targetTokens"],n),e}function Xa(t){const e={},n=i(t,["triggerTokens"]);n!=null&&r(e,["triggerTokens"],n);const o=i(t,["slidingWindow"]);return o!=null&&r(e,["slidingWindow"],Ka(o)),e}function Qa(t){const e={},n=i(t,["triggerTokens"]);n!=null&&r(e,["triggerTokens"],n);const o=i(t,["slidingWindow"]);return o!=null&&r(e,["slidingWindow"],Oa(o)),e}function Za(t){const e={},n=i(t,["proactiveAudio"]);return n!=null&&r(e,["proactiveAudio"],n),e}function ja(t){const e={},n=i(t,["proactiveAudio"]);return n!=null&&r(e,["proactiveAudio"],n),e}function el(t,e){const n={},o=i(t,["generationConfig"]);e!==void 0&&o!=null&&r(e,["setup","generationConfig"],o);const s=i(t,["responseModalities"]);e!==void 0&&s!=null&&r(e,["setup","generationConfig","responseModalities"],s);const a=i(t,["temperature"]);e!==void 0&&a!=null&&r(e,["setup","generationConfig","temperature"],a);const l=i(t,["topP"]);e!==void 0&&l!=null&&r(e,["setup","generationConfig","topP"],l);const c=i(t,["topK"]);e!==void 0&&c!=null&&r(e,["setup","generationConfig","topK"],c);const d=i(t,["maxOutputTokens"]);e!==void 0&&d!=null&&r(e,["setup","generationConfig","maxOutputTokens"],d);const u=i(t,["mediaResolution"]);e!==void 0&&u!=null&&r(e,["setup","generationConfig","mediaResolution"],u);const f=i(t,["seed"]);e!==void 0&&f!=null&&r(e,["setup","generationConfig","seed"],f);const p=i(t,["speechConfig"]);e!==void 0&&p!=null&&r(e,["setup","generationConfig","speechConfig"],fa(wt(p)));const h=i(t,["enableAffectiveDialog"]);e!==void 0&&h!=null&&r(e,["setup","generationConfig","enableAffectiveDialog"],h);const m=i(t,["systemInstruction"]);e!==void 0&&m!=null&&r(e,["setup","systemInstruction"],wa($(m)));const g=i(t,["tools"]);if(e!==void 0&&g!=null){let E=ye(g);Array.isArray(E)&&(E=E.map(M=>Ga(ge(M)))),r(e,["setup","tools"],E)}const y=i(t,["sessionResumption"]);e!==void 0&&y!=null&&r(e,["setup","sessionResumption"],qa(y));const v=i(t,["inputAudioTranscription"]);e!==void 0&&v!=null&&r(e,["setup","inputAudioTranscription"],Bn());const C=i(t,["outputAudioTranscription"]);e!==void 0&&C!=null&&r(e,["setup","outputAudioTranscription"],Bn());const w=i(t,["realtimeInputConfig"]);e!==void 0&&w!=null&&r(e,["setup","realtimeInputConfig"],Wa(w));const b=i(t,["contextWindowCompression"]);e!==void 0&&b!=null&&r(e,["setup","contextWindowCompression"],Xa(b));const A=i(t,["proactivity"]);return e!==void 0&&A!=null&&r(e,["setup","proactivity"],Za(A)),n}function tl(t,e){const n={},o=i(t,["generationConfig"]);e!==void 0&&o!=null&&r(e,["setup","generationConfig"],o);const s=i(t,["responseModalities"]);e!==void 0&&s!=null&&r(e,["setup","generationConfig","responseModalities"],s);const a=i(t,["temperature"]);e!==void 0&&a!=null&&r(e,["setup","generationConfig","temperature"],a);const l=i(t,["topP"]);e!==void 0&&l!=null&&r(e,["setup","generationConfig","topP"],l);const c=i(t,["topK"]);e!==void 0&&c!=null&&r(e,["setup","generationConfig","topK"],c);const d=i(t,["maxOutputTokens"]);e!==void 0&&d!=null&&r(e,["setup","generationConfig","maxOutputTokens"],d);const u=i(t,["mediaResolution"]);e!==void 0&&u!=null&&r(e,["setup","generationConfig","mediaResolution"],u);const f=i(t,["seed"]);e!==void 0&&f!=null&&r(e,["setup","generationConfig","seed"],f);const p=i(t,["speechConfig"]);e!==void 0&&p!=null&&r(e,["setup","generationConfig","speechConfig"],pa(wt(p)));const h=i(t,["enableAffectiveDialog"]);e!==void 0&&h!=null&&r(e,["setup","generationConfig","enableAffectiveDialog"],h);const m=i(t,["systemInstruction"]);e!==void 0&&m!=null&&r(e,["setup","systemInstruction"],ba($(m)));const g=i(t,["tools"]);if(e!==void 0&&g!=null){let E=ye(g);Array.isArray(E)&&(E=E.map(M=>Ba(ge(M)))),r(e,["setup","tools"],E)}const y=i(t,["sessionResumption"]);e!==void 0&&y!=null&&r(e,["setup","sessionResumption"],za(y));const v=i(t,["inputAudioTranscription"]);e!==void 0&&v!=null&&r(e,["setup","inputAudioTranscription"],qn());const C=i(t,["outputAudioTranscription"]);e!==void 0&&C!=null&&r(e,["setup","outputAudioTranscription"],qn());const w=i(t,["realtimeInputConfig"]);e!==void 0&&w!=null&&r(e,["setup","realtimeInputConfig"],Ya(w));const b=i(t,["contextWindowCompression"]);e!==void 0&&b!=null&&r(e,["setup","contextWindowCompression"],Qa(b));const A=i(t,["proactivity"]);return e!==void 0&&A!=null&&r(e,["setup","proactivity"],ja(A)),n}function nl(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["setup","model"],I(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],el(s,n)),n}function ol(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["setup","model"],I(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],tl(s,n)),n}function il(){return{}}function rl(){return{}}function sl(){return{}}function al(){return{}}function ll(t){const e={},n=i(t,["media"]);n!=null&&r(e,["mediaChunks"],xo(n));const o=i(t,["audio"]);o!=null&&r(e,["audio"],To(o));const s=i(t,["audioStreamEnd"]);s!=null&&r(e,["audioStreamEnd"],s);const a=i(t,["video"]);a!=null&&r(e,["video"],Co(a));const l=i(t,["text"]);return l!=null&&r(e,["text"],l),i(t,["activityStart"])!=null&&r(e,["activityStart"],il()),i(t,["activityEnd"])!=null&&r(e,["activityEnd"],sl()),e}function cl(t){const e={},n=i(t,["media"]);n!=null&&r(e,["mediaChunks"],xo(n));const o=i(t,["audio"]);o!=null&&r(e,["audio"],To(o));const s=i(t,["audioStreamEnd"]);s!=null&&r(e,["audioStreamEnd"],s);const a=i(t,["video"]);a!=null&&r(e,["video"],Co(a));const l=i(t,["text"]);return l!=null&&r(e,["text"],l),i(t,["activityStart"])!=null&&r(e,["activityStart"],rl()),i(t,["activityEnd"])!=null&&r(e,["activityEnd"],al()),e}function Lo(t){const e={},n=i(t,["text"]);n!=null&&r(e,["text"],n);const o=i(t,["weight"]);return o!=null&&r(e,["weight"],o),e}function ul(t){const e={},n=i(t,["weightedPrompts"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>Lo(s))),r(e,["weightedPrompts"],o)}return e}function Uo(t){const e={},n=i(t,["temperature"]);n!=null&&r(e,["temperature"],n);const o=i(t,["topK"]);o!=null&&r(e,["topK"],o);const s=i(t,["seed"]);s!=null&&r(e,["seed"],s);const a=i(t,["guidance"]);a!=null&&r(e,["guidance"],a);const l=i(t,["bpm"]);l!=null&&r(e,["bpm"],l);const c=i(t,["density"]);c!=null&&r(e,["density"],c);const d=i(t,["brightness"]);d!=null&&r(e,["brightness"],d);const u=i(t,["scale"]);u!=null&&r(e,["scale"],u);const f=i(t,["muteBass"]);f!=null&&r(e,["muteBass"],f);const p=i(t,["muteDrums"]);p!=null&&r(e,["muteDrums"],p);const h=i(t,["onlyBassAndDrums"]);return h!=null&&r(e,["onlyBassAndDrums"],h),e}function dl(t){const e={},n=i(t,["musicGenerationConfig"]);return n!=null&&r(e,["musicGenerationConfig"],Uo(n)),e}function $o(t){const e={},n=i(t,["model"]);return n!=null&&r(e,["model"],n),e}function Vo(t){const e={},n=i(t,["weightedPrompts"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>Lo(s))),r(e,["weightedPrompts"],o)}return e}function lt(t){const e={},n=i(t,["setup"]);n!=null&&r(e,["setup"],$o(n));const o=i(t,["clientContent"]);o!=null&&r(e,["clientContent"],Vo(o));const s=i(t,["musicGenerationConfig"]);s!=null&&r(e,["musicGenerationConfig"],Uo(s));const a=i(t,["playbackControl"]);return a!=null&&r(e,["playbackControl"],a),e}function fl(){return{}}function pl(t){const e={},n=i(t,["sessionId"]);return n!=null&&r(e,["sessionId"],n),e}function hl(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function ml(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function gl(t){const e={},n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function yl(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["data"]);o!=null&&r(e,["data"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function vl(t){const e={},n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function xl(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["fileUri"]);o!=null&&r(e,["fileUri"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Cl(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],hl(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],gl(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],vl(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Tl(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],ml(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],yl(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],xl(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function wl(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Cl(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function bl(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Tl(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function zn(t){const e={},n=i(t,["text"]);n!=null&&r(e,["text"],n);const o=i(t,["finished"]);return o!=null&&r(e,["finished"],o),e}function Hn(t){const e={},n=i(t,["text"]);n!=null&&r(e,["text"],n);const o=i(t,["finished"]);return o!=null&&r(e,["finished"],o),e}function El(t){const e={},n=i(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const o=i(t,["urlRetrievalStatus"]);return o!=null&&r(e,["urlRetrievalStatus"],o),e}function Al(t){const e={},n=i(t,["urlMetadata"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>El(s))),r(e,["urlMetadata"],o)}return e}function Sl(t){const e={},n=i(t,["modelTurn"]);n!=null&&r(e,["modelTurn"],wl(n));const o=i(t,["turnComplete"]);o!=null&&r(e,["turnComplete"],o);const s=i(t,["interrupted"]);s!=null&&r(e,["interrupted"],s);const a=i(t,["groundingMetadata"]);a!=null&&r(e,["groundingMetadata"],a);const l=i(t,["generationComplete"]);l!=null&&r(e,["generationComplete"],l);const c=i(t,["inputTranscription"]);c!=null&&r(e,["inputTranscription"],zn(c));const d=i(t,["outputTranscription"]);d!=null&&r(e,["outputTranscription"],zn(d));const u=i(t,["urlContextMetadata"]);return u!=null&&r(e,["urlContextMetadata"],Al(u)),e}function Ml(t){const e={},n=i(t,["modelTurn"]);n!=null&&r(e,["modelTurn"],bl(n));const o=i(t,["turnComplete"]);o!=null&&r(e,["turnComplete"],o);const s=i(t,["interrupted"]);s!=null&&r(e,["interrupted"],s);const a=i(t,["groundingMetadata"]);a!=null&&r(e,["groundingMetadata"],a);const l=i(t,["generationComplete"]);l!=null&&r(e,["generationComplete"],l);const c=i(t,["inputTranscription"]);c!=null&&r(e,["inputTranscription"],Hn(c));const d=i(t,["outputTranscription"]);return d!=null&&r(e,["outputTranscription"],Hn(d)),e}function _l(t){const e={},n=i(t,["id"]);n!=null&&r(e,["id"],n);const o=i(t,["args"]);o!=null&&r(e,["args"],o);const s=i(t,["name"]);return s!=null&&r(e,["name"],s),e}function Pl(t){const e={},n=i(t,["args"]);n!=null&&r(e,["args"],n);const o=i(t,["name"]);return o!=null&&r(e,["name"],o),e}function Il(t){const e={},n=i(t,["functionCalls"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>_l(s))),r(e,["functionCalls"],o)}return e}function kl(t){const e={},n=i(t,["functionCalls"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>Pl(s))),r(e,["functionCalls"],o)}return e}function Rl(t){const e={},n=i(t,["ids"]);return n!=null&&r(e,["ids"],n),e}function Dl(t){const e={},n=i(t,["ids"]);return n!=null&&r(e,["ids"],n),e}function Re(t){const e={},n=i(t,["modality"]);n!=null&&r(e,["modality"],n);const o=i(t,["tokenCount"]);return o!=null&&r(e,["tokenCount"],o),e}function De(t){const e={},n=i(t,["modality"]);n!=null&&r(e,["modality"],n);const o=i(t,["tokenCount"]);return o!=null&&r(e,["tokenCount"],o),e}function Nl(t){const e={},n=i(t,["promptTokenCount"]);n!=null&&r(e,["promptTokenCount"],n);const o=i(t,["cachedContentTokenCount"]);o!=null&&r(e,["cachedContentTokenCount"],o);const s=i(t,["responseTokenCount"]);s!=null&&r(e,["responseTokenCount"],s);const a=i(t,["toolUsePromptTokenCount"]);a!=null&&r(e,["toolUsePromptTokenCount"],a);const l=i(t,["thoughtsTokenCount"]);l!=null&&r(e,["thoughtsTokenCount"],l);const c=i(t,["totalTokenCount"]);c!=null&&r(e,["totalTokenCount"],c);const d=i(t,["promptTokensDetails"]);if(d!=null){let h=d;Array.isArray(h)&&(h=h.map(m=>Re(m))),r(e,["promptTokensDetails"],h)}const u=i(t,["cacheTokensDetails"]);if(u!=null){let h=u;Array.isArray(h)&&(h=h.map(m=>Re(m))),r(e,["cacheTokensDetails"],h)}const f=i(t,["responseTokensDetails"]);if(f!=null){let h=f;Array.isArray(h)&&(h=h.map(m=>Re(m))),r(e,["responseTokensDetails"],h)}const p=i(t,["toolUsePromptTokensDetails"]);if(p!=null){let h=p;Array.isArray(h)&&(h=h.map(m=>Re(m))),r(e,["toolUsePromptTokensDetails"],h)}return e}function Fl(t){const e={},n=i(t,["promptTokenCount"]);n!=null&&r(e,["promptTokenCount"],n);const o=i(t,["cachedContentTokenCount"]);o!=null&&r(e,["cachedContentTokenCount"],o);const s=i(t,["candidatesTokenCount"]);s!=null&&r(e,["responseTokenCount"],s);const a=i(t,["toolUsePromptTokenCount"]);a!=null&&r(e,["toolUsePromptTokenCount"],a);const l=i(t,["thoughtsTokenCount"]);l!=null&&r(e,["thoughtsTokenCount"],l);const c=i(t,["totalTokenCount"]);c!=null&&r(e,["totalTokenCount"],c);const d=i(t,["promptTokensDetails"]);if(d!=null){let m=d;Array.isArray(m)&&(m=m.map(g=>De(g))),r(e,["promptTokensDetails"],m)}const u=i(t,["cacheTokensDetails"]);if(u!=null){let m=u;Array.isArray(m)&&(m=m.map(g=>De(g))),r(e,["cacheTokensDetails"],m)}const f=i(t,["candidatesTokensDetails"]);if(f!=null){let m=f;Array.isArray(m)&&(m=m.map(g=>De(g))),r(e,["responseTokensDetails"],m)}const p=i(t,["toolUsePromptTokensDetails"]);if(p!=null){let m=p;Array.isArray(m)&&(m=m.map(g=>De(g))),r(e,["toolUsePromptTokensDetails"],m)}const h=i(t,["trafficType"]);return h!=null&&r(e,["trafficType"],h),e}function Ll(t){const e={},n=i(t,["timeLeft"]);return n!=null&&r(e,["timeLeft"],n),e}function Ul(t){const e={},n=i(t,["timeLeft"]);return n!=null&&r(e,["timeLeft"],n),e}function $l(t){const e={},n=i(t,["newHandle"]);n!=null&&r(e,["newHandle"],n);const o=i(t,["resumable"]);o!=null&&r(e,["resumable"],o);const s=i(t,["lastConsumedClientMessageIndex"]);return s!=null&&r(e,["lastConsumedClientMessageIndex"],s),e}function Vl(t){const e={},n=i(t,["newHandle"]);n!=null&&r(e,["newHandle"],n);const o=i(t,["resumable"]);o!=null&&r(e,["resumable"],o);const s=i(t,["lastConsumedClientMessageIndex"]);return s!=null&&r(e,["lastConsumedClientMessageIndex"],s),e}function Gl(t){const e={};i(t,["setupComplete"])!=null&&r(e,["setupComplete"],fl());const o=i(t,["serverContent"]);o!=null&&r(e,["serverContent"],Sl(o));const s=i(t,["toolCall"]);s!=null&&r(e,["toolCall"],Il(s));const a=i(t,["toolCallCancellation"]);a!=null&&r(e,["toolCallCancellation"],Rl(a));const l=i(t,["usageMetadata"]);l!=null&&r(e,["usageMetadata"],Nl(l));const c=i(t,["goAway"]);c!=null&&r(e,["goAway"],Ll(c));const d=i(t,["sessionResumptionUpdate"]);return d!=null&&r(e,["sessionResumptionUpdate"],$l(d)),e}function Bl(t){const e={},n=i(t,["setupComplete"]);n!=null&&r(e,["setupComplete"],pl(n));const o=i(t,["serverContent"]);o!=null&&r(e,["serverContent"],Ml(o));const s=i(t,["toolCall"]);s!=null&&r(e,["toolCall"],kl(s));const a=i(t,["toolCallCancellation"]);a!=null&&r(e,["toolCallCancellation"],Dl(a));const l=i(t,["usageMetadata"]);l!=null&&r(e,["usageMetadata"],Fl(l));const c=i(t,["goAway"]);c!=null&&r(e,["goAway"],Ul(c));const d=i(t,["sessionResumptionUpdate"]);return d!=null&&r(e,["sessionResumptionUpdate"],Vl(d)),e}function ql(){return{}}function zl(t){const e={},n=i(t,["text"]);n!=null&&r(e,["text"],n);const o=i(t,["weight"]);return o!=null&&r(e,["weight"],o),e}function Hl(t){const e={},n=i(t,["weightedPrompts"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>zl(s))),r(e,["weightedPrompts"],o)}return e}function Jl(t){const e={},n=i(t,["temperature"]);n!=null&&r(e,["temperature"],n);const o=i(t,["topK"]);o!=null&&r(e,["topK"],o);const s=i(t,["seed"]);s!=null&&r(e,["seed"],s);const a=i(t,["guidance"]);a!=null&&r(e,["guidance"],a);const l=i(t,["bpm"]);l!=null&&r(e,["bpm"],l);const c=i(t,["density"]);c!=null&&r(e,["density"],c);const d=i(t,["brightness"]);d!=null&&r(e,["brightness"],d);const u=i(t,["scale"]);u!=null&&r(e,["scale"],u);const f=i(t,["muteBass"]);f!=null&&r(e,["muteBass"],f);const p=i(t,["muteDrums"]);p!=null&&r(e,["muteDrums"],p);const h=i(t,["onlyBassAndDrums"]);return h!=null&&r(e,["onlyBassAndDrums"],h),e}function Wl(t){const e={},n=i(t,["clientContent"]);n!=null&&r(e,["clientContent"],Hl(n));const o=i(t,["musicGenerationConfig"]);return o!=null&&r(e,["musicGenerationConfig"],Jl(o)),e}function Yl(t){const e={},n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);o!=null&&r(e,["mimeType"],o);const s=i(t,["sourceMetadata"]);return s!=null&&r(e,["sourceMetadata"],Wl(s)),e}function Kl(t){const e={},n=i(t,["audioChunks"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>Yl(s))),r(e,["audioChunks"],o)}return e}function Ol(t){const e={},n=i(t,["text"]);n!=null&&r(e,["text"],n);const o=i(t,["filteredReason"]);return o!=null&&r(e,["filteredReason"],o),e}function Xl(t){const e={};i(t,["setupComplete"])!=null&&r(e,["setupComplete"],ql());const o=i(t,["serverContent"]);o!=null&&r(e,["serverContent"],Kl(o));const s=i(t,["filteredPrompt"]);return s!=null&&r(e,["filteredPrompt"],Ol(s)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Ql(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Zl(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function jl(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function ec(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Ql(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],Zl(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],jl(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function We(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>ec(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function tc(t){const e={},n=i(t,["anyOf"]);n!=null&&r(e,["anyOf"],n);const o=i(t,["default"]);o!=null&&r(e,["default"],o);const s=i(t,["description"]);s!=null&&r(e,["description"],s);const a=i(t,["enum"]);a!=null&&r(e,["enum"],a);const l=i(t,["example"]);l!=null&&r(e,["example"],l);const c=i(t,["format"]);c!=null&&r(e,["format"],c);const d=i(t,["items"]);d!=null&&r(e,["items"],d);const u=i(t,["maxItems"]);u!=null&&r(e,["maxItems"],u);const f=i(t,["maxLength"]);f!=null&&r(e,["maxLength"],f);const p=i(t,["maxProperties"]);p!=null&&r(e,["maxProperties"],p);const h=i(t,["maximum"]);h!=null&&r(e,["maximum"],h);const m=i(t,["minItems"]);m!=null&&r(e,["minItems"],m);const g=i(t,["minLength"]);g!=null&&r(e,["minLength"],g);const y=i(t,["minProperties"]);y!=null&&r(e,["minProperties"],y);const v=i(t,["minimum"]);v!=null&&r(e,["minimum"],v);const C=i(t,["nullable"]);C!=null&&r(e,["nullable"],C);const w=i(t,["pattern"]);w!=null&&r(e,["pattern"],w);const b=i(t,["properties"]);b!=null&&r(e,["properties"],b);const A=i(t,["propertyOrdering"]);A!=null&&r(e,["propertyOrdering"],A);const E=i(t,["required"]);E!=null&&r(e,["required"],E);const M=i(t,["title"]);M!=null&&r(e,["title"],M);const _=i(t,["type"]);return _!=null&&r(e,["type"],_),e}function nc(t){const e={};if(i(t,["method"])!==void 0)throw new Error("method parameter is not supported in Gemini API.");const n=i(t,["category"]);n!=null&&r(e,["category"],n);const o=i(t,["threshold"]);return o!=null&&r(e,["threshold"],o),e}function oc(t){const e={},n=i(t,["behavior"]);n!=null&&r(e,["behavior"],n);const o=i(t,["description"]);o!=null&&r(e,["description"],o);const s=i(t,["name"]);s!=null&&r(e,["name"],s);const a=i(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=i(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const c=i(t,["response"]);c!=null&&r(e,["response"],c);const d=i(t,["responseJsonSchema"]);return d!=null&&r(e,["responseJsonSchema"],d),e}function ic(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function rc(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],ic(n)),e}function sc(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function ac(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],sc(n)),e}function lc(){return{}}function cc(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(u=>oc(u))),r(e,["functionDeclarations"],d)}if(i(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const o=i(t,["googleSearch"]);o!=null&&r(e,["googleSearch"],rc(o));const s=i(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],ac(s)),i(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(i(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");i(t,["urlContext"])!=null&&r(e,["urlContext"],lc());const l=i(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const c=i(t,["computerUse"]);return c!=null&&r(e,["computerUse"],c),e}function uc(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["allowedFunctionNames"]);return o!=null&&r(e,["allowedFunctionNames"],o),e}function dc(t){const e={},n=i(t,["latitude"]);n!=null&&r(e,["latitude"],n);const o=i(t,["longitude"]);return o!=null&&r(e,["longitude"],o),e}function fc(t){const e={},n=i(t,["latLng"]);n!=null&&r(e,["latLng"],dc(n));const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function pc(t){const e={},n=i(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],uc(n));const o=i(t,["retrievalConfig"]);return o!=null&&r(e,["retrievalConfig"],fc(o)),e}function hc(t){const e={},n=i(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function Go(t){const e={},n=i(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],hc(n)),e}function mc(t){const e={},n=i(t,["speaker"]);n!=null&&r(e,["speaker"],n);const o=i(t,["voiceConfig"]);return o!=null&&r(e,["voiceConfig"],Go(o)),e}function gc(t){const e={},n=i(t,["speakerVoiceConfigs"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>mc(s))),r(e,["speakerVoiceConfigs"],o)}return e}function yc(t){const e={},n=i(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],Go(n));const o=i(t,["multiSpeakerVoiceConfig"]);o!=null&&r(e,["multiSpeakerVoiceConfig"],gc(o));const s=i(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function vc(t){const e={},n=i(t,["includeThoughts"]);n!=null&&r(e,["includeThoughts"],n);const o=i(t,["thinkingBudget"]);return o!=null&&r(e,["thinkingBudget"],o),e}function xc(t,e,n){const o={},s=i(e,["systemInstruction"]);n!==void 0&&s!=null&&r(n,["systemInstruction"],We($(s)));const a=i(e,["temperature"]);a!=null&&r(o,["temperature"],a);const l=i(e,["topP"]);l!=null&&r(o,["topP"],l);const c=i(e,["topK"]);c!=null&&r(o,["topK"],c);const d=i(e,["candidateCount"]);d!=null&&r(o,["candidateCount"],d);const u=i(e,["maxOutputTokens"]);u!=null&&r(o,["maxOutputTokens"],u);const f=i(e,["stopSequences"]);f!=null&&r(o,["stopSequences"],f);const p=i(e,["responseLogprobs"]);p!=null&&r(o,["responseLogprobs"],p);const h=i(e,["logprobs"]);h!=null&&r(o,["logprobs"],h);const m=i(e,["presencePenalty"]);m!=null&&r(o,["presencePenalty"],m);const g=i(e,["frequencyPenalty"]);g!=null&&r(o,["frequencyPenalty"],g);const y=i(e,["seed"]);y!=null&&r(o,["seed"],y);const v=i(e,["responseMimeType"]);v!=null&&r(o,["responseMimeType"],v);const C=i(e,["responseSchema"]);C!=null&&r(o,["responseSchema"],tc(Ct(C)));const w=i(e,["responseJsonSchema"]);if(w!=null&&r(o,["responseJsonSchema"],w),i(e,["routingConfig"])!==void 0)throw new Error("routingConfig parameter is not supported in Gemini API.");if(i(e,["modelSelectionConfig"])!==void 0)throw new Error("modelSelectionConfig parameter is not supported in Gemini API.");const b=i(e,["safetySettings"]);if(n!==void 0&&b!=null){let P=b;Array.isArray(P)&&(P=P.map(W=>nc(W))),r(n,["safetySettings"],P)}const A=i(e,["tools"]);if(n!==void 0&&A!=null){let P=ye(A);Array.isArray(P)&&(P=P.map(W=>cc(ge(W)))),r(n,["tools"],P)}const E=i(e,["toolConfig"]);if(n!==void 0&&E!=null&&r(n,["toolConfig"],pc(E)),i(e,["labels"])!==void 0)throw new Error("labels parameter is not supported in Gemini API.");const M=i(e,["cachedContent"]);n!==void 0&&M!=null&&r(n,["cachedContent"],X(t,M));const _=i(e,["responseModalities"]);_!=null&&r(o,["responseModalities"],_);const G=i(e,["mediaResolution"]);G!=null&&r(o,["mediaResolution"],G);const S=i(e,["speechConfig"]);if(S!=null&&r(o,["speechConfig"],yc(Tt(S))),i(e,["audioTimestamp"])!==void 0)throw new Error("audioTimestamp parameter is not supported in Gemini API.");const k=i(e,["thinkingConfig"]);return k!=null&&r(o,["thinkingConfig"],vc(k)),o}function Jn(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["contents"]);if(s!=null){let l=H(s);Array.isArray(l)&&(l=l.map(c=>We(c))),r(n,["contents"],l)}const a=i(e,["config"]);return a!=null&&r(n,["generationConfig"],xc(t,a,n)),n}function Cc(t,e){const n={},o=i(t,["taskType"]);e!==void 0&&o!=null&&r(e,["requests[]","taskType"],o);const s=i(t,["title"]);e!==void 0&&s!=null&&r(e,["requests[]","title"],s);const a=i(t,["outputDimensionality"]);if(e!==void 0&&a!=null&&r(e,["requests[]","outputDimensionality"],a),i(t,["mimeType"])!==void 0)throw new Error("mimeType parameter is not supported in Gemini API.");if(i(t,["autoTruncate"])!==void 0)throw new Error("autoTruncate parameter is not supported in Gemini API.");return n}function Tc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["contents"]);s!=null&&r(n,["requests[]","content"],bo(t,s));const a=i(e,["config"]);a!=null&&r(n,["config"],Cc(a,n));const l=i(e,["model"]);return l!==void 0&&r(n,["requests[]","model"],I(t,l)),n}function wc(t,e){const n={};if(i(t,["outputGcsUri"])!==void 0)throw new Error("outputGcsUri parameter is not supported in Gemini API.");if(i(t,["negativePrompt"])!==void 0)throw new Error("negativePrompt parameter is not supported in Gemini API.");const o=i(t,["numberOfImages"]);e!==void 0&&o!=null&&r(e,["parameters","sampleCount"],o);const s=i(t,["aspectRatio"]);e!==void 0&&s!=null&&r(e,["parameters","aspectRatio"],s);const a=i(t,["guidanceScale"]);if(e!==void 0&&a!=null&&r(e,["parameters","guidanceScale"],a),i(t,["seed"])!==void 0)throw new Error("seed parameter is not supported in Gemini API.");const l=i(t,["safetyFilterLevel"]);e!==void 0&&l!=null&&r(e,["parameters","safetySetting"],l);const c=i(t,["personGeneration"]);e!==void 0&&c!=null&&r(e,["parameters","personGeneration"],c);const d=i(t,["includeSafetyAttributes"]);e!==void 0&&d!=null&&r(e,["parameters","includeSafetyAttributes"],d);const u=i(t,["includeRaiReason"]);e!==void 0&&u!=null&&r(e,["parameters","includeRaiReason"],u);const f=i(t,["language"]);e!==void 0&&f!=null&&r(e,["parameters","language"],f);const p=i(t,["outputMimeType"]);e!==void 0&&p!=null&&r(e,["parameters","outputOptions","mimeType"],p);const h=i(t,["outputCompressionQuality"]);if(e!==void 0&&h!=null&&r(e,["parameters","outputOptions","compressionQuality"],h),i(t,["addWatermark"])!==void 0)throw new Error("addWatermark parameter is not supported in Gemini API.");if(i(t,["enhancePrompt"])!==void 0)throw new Error("enhancePrompt parameter is not supported in Gemini API.");return n}function bc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const a=i(e,["config"]);return a!=null&&r(n,["config"],wc(a,n)),n}function Ec(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","name"],I(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Ac(t,e,n){const o={},s=i(e,["pageSize"]);n!==void 0&&s!=null&&r(n,["_query","pageSize"],s);const a=i(e,["pageToken"]);n!==void 0&&a!=null&&r(n,["_query","pageToken"],a);const l=i(e,["filter"]);n!==void 0&&l!=null&&r(n,["_query","filter"],l);const c=i(e,["queryBase"]);return n!==void 0&&c!=null&&r(n,["_url","models_url"],So(t,c)),o}function Sc(t,e){const n={},o=i(e,["config"]);return o!=null&&r(n,["config"],Ac(t,o,n)),n}function Mc(t,e){const n={},o=i(t,["displayName"]);e!==void 0&&o!=null&&r(e,["displayName"],o);const s=i(t,["description"]);e!==void 0&&s!=null&&r(e,["description"],s);const a=i(t,["defaultCheckpointId"]);return e!==void 0&&a!=null&&r(e,["defaultCheckpointId"],a),n}function _c(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","name"],I(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],Mc(s,n)),n}function Pc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","name"],I(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Ic(t){const e={};if(i(t,["systemInstruction"])!==void 0)throw new Error("systemInstruction parameter is not supported in Gemini API.");if(i(t,["tools"])!==void 0)throw new Error("tools parameter is not supported in Gemini API.");if(i(t,["generationConfig"])!==void 0)throw new Error("generationConfig parameter is not supported in Gemini API.");return e}function kc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["contents"]);if(s!=null){let l=H(s);Array.isArray(l)&&(l=l.map(c=>We(c))),r(n,["contents"],l)}const a=i(e,["config"]);return a!=null&&r(n,["config"],Ic(a)),n}function Rc(t){const e={};if(i(t,["gcsUri"])!==void 0)throw new Error("gcsUri parameter is not supported in Gemini API.");const n=i(t,["imageBytes"]);n!=null&&r(e,["bytesBase64Encoded"],Q(n));const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Dc(t,e){const n={},o=i(t,["numberOfVideos"]);if(e!==void 0&&o!=null&&r(e,["parameters","sampleCount"],o),i(t,["outputGcsUri"])!==void 0)throw new Error("outputGcsUri parameter is not supported in Gemini API.");if(i(t,["fps"])!==void 0)throw new Error("fps parameter is not supported in Gemini API.");const s=i(t,["durationSeconds"]);if(e!==void 0&&s!=null&&r(e,["parameters","durationSeconds"],s),i(t,["seed"])!==void 0)throw new Error("seed parameter is not supported in Gemini API.");const a=i(t,["aspectRatio"]);if(e!==void 0&&a!=null&&r(e,["parameters","aspectRatio"],a),i(t,["resolution"])!==void 0)throw new Error("resolution parameter is not supported in Gemini API.");const l=i(t,["personGeneration"]);if(e!==void 0&&l!=null&&r(e,["parameters","personGeneration"],l),i(t,["pubsubTopic"])!==void 0)throw new Error("pubsubTopic parameter is not supported in Gemini API.");const c=i(t,["negativePrompt"]);e!==void 0&&c!=null&&r(e,["parameters","negativePrompt"],c);const d=i(t,["enhancePrompt"]);if(e!==void 0&&d!=null&&r(e,["parameters","enhancePrompt"],d),i(t,["generateAudio"])!==void 0)throw new Error("generateAudio parameter is not supported in Gemini API.");if(i(t,["lastFrame"])!==void 0)throw new Error("lastFrame parameter is not supported in Gemini API.");if(i(t,["compressionQuality"])!==void 0)throw new Error("compressionQuality parameter is not supported in Gemini API.");return n}function Nc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const a=i(e,["image"]);if(a!=null&&r(n,["instances[0]","image"],Rc(a)),i(e,["video"])!==void 0)throw new Error("video parameter is not supported in Gemini API.");const l=i(e,["config"]);return l!=null&&r(n,["config"],Dc(l,n)),n}function Fc(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Lc(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["data"]);o!=null&&r(e,["data"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Uc(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["fileUri"]);o!=null&&r(e,["fileUri"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function $c(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Fc(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],Lc(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],Uc(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function xe(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>$c(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Vc(t){const e={},n=i(t,["anyOf"]);n!=null&&r(e,["anyOf"],n);const o=i(t,["default"]);o!=null&&r(e,["default"],o);const s=i(t,["description"]);s!=null&&r(e,["description"],s);const a=i(t,["enum"]);a!=null&&r(e,["enum"],a);const l=i(t,["example"]);l!=null&&r(e,["example"],l);const c=i(t,["format"]);c!=null&&r(e,["format"],c);const d=i(t,["items"]);d!=null&&r(e,["items"],d);const u=i(t,["maxItems"]);u!=null&&r(e,["maxItems"],u);const f=i(t,["maxLength"]);f!=null&&r(e,["maxLength"],f);const p=i(t,["maxProperties"]);p!=null&&r(e,["maxProperties"],p);const h=i(t,["maximum"]);h!=null&&r(e,["maximum"],h);const m=i(t,["minItems"]);m!=null&&r(e,["minItems"],m);const g=i(t,["minLength"]);g!=null&&r(e,["minLength"],g);const y=i(t,["minProperties"]);y!=null&&r(e,["minProperties"],y);const v=i(t,["minimum"]);v!=null&&r(e,["minimum"],v);const C=i(t,["nullable"]);C!=null&&r(e,["nullable"],C);const w=i(t,["pattern"]);w!=null&&r(e,["pattern"],w);const b=i(t,["properties"]);b!=null&&r(e,["properties"],b);const A=i(t,["propertyOrdering"]);A!=null&&r(e,["propertyOrdering"],A);const E=i(t,["required"]);E!=null&&r(e,["required"],E);const M=i(t,["title"]);M!=null&&r(e,["title"],M);const _=i(t,["type"]);return _!=null&&r(e,["type"],_),e}function Gc(t){const e={},n=i(t,["featureSelectionPreference"]);return n!=null&&r(e,["featureSelectionPreference"],n),e}function Bc(t){const e={},n=i(t,["method"]);n!=null&&r(e,["method"],n);const o=i(t,["category"]);o!=null&&r(e,["category"],o);const s=i(t,["threshold"]);return s!=null&&r(e,["threshold"],s),e}function qc(t){const e={};if(i(t,["behavior"])!==void 0)throw new Error("behavior parameter is not supported in Vertex AI.");const n=i(t,["description"]);n!=null&&r(e,["description"],n);const o=i(t,["name"]);o!=null&&r(e,["name"],o);const s=i(t,["parameters"]);s!=null&&r(e,["parameters"],s);const a=i(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const l=i(t,["response"]);l!=null&&r(e,["response"],l);const c=i(t,["responseJsonSchema"]);return c!=null&&r(e,["responseJsonSchema"],c),e}function zc(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function Hc(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],zc(n)),e}function Jc(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function Wc(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Jc(n)),e}function Yc(){return{}}function Kc(t){const e={},n=i(t,["apiKeyString"]);return n!=null&&r(e,["apiKeyString"],n),e}function Oc(t){const e={},n=i(t,["apiKeyConfig"]);n!=null&&r(e,["apiKeyConfig"],Kc(n));const o=i(t,["authType"]);o!=null&&r(e,["authType"],o);const s=i(t,["googleServiceAccountConfig"]);s!=null&&r(e,["googleServiceAccountConfig"],s);const a=i(t,["httpBasicAuthConfig"]);a!=null&&r(e,["httpBasicAuthConfig"],a);const l=i(t,["oauthConfig"]);l!=null&&r(e,["oauthConfig"],l);const c=i(t,["oidcConfig"]);return c!=null&&r(e,["oidcConfig"],c),e}function Xc(t){const e={},n=i(t,["authConfig"]);return n!=null&&r(e,["authConfig"],Oc(n)),e}function Qc(){return{}}function Bo(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let p=n;Array.isArray(p)&&(p=p.map(h=>qc(h))),r(e,["functionDeclarations"],p)}const o=i(t,["retrieval"]);o!=null&&r(e,["retrieval"],o);const s=i(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],Hc(s));const a=i(t,["googleSearchRetrieval"]);a!=null&&r(e,["googleSearchRetrieval"],Wc(a)),i(t,["enterpriseWebSearch"])!=null&&r(e,["enterpriseWebSearch"],Yc());const c=i(t,["googleMaps"]);c!=null&&r(e,["googleMaps"],Xc(c)),i(t,["urlContext"])!=null&&r(e,["urlContext"],Qc());const u=i(t,["codeExecution"]);u!=null&&r(e,["codeExecution"],u);const f=i(t,["computerUse"]);return f!=null&&r(e,["computerUse"],f),e}function Zc(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["allowedFunctionNames"]);return o!=null&&r(e,["allowedFunctionNames"],o),e}function jc(t){const e={},n=i(t,["latitude"]);n!=null&&r(e,["latitude"],n);const o=i(t,["longitude"]);return o!=null&&r(e,["longitude"],o),e}function eu(t){const e={},n=i(t,["latLng"]);n!=null&&r(e,["latLng"],jc(n));const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function tu(t){const e={},n=i(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],Zc(n));const o=i(t,["retrievalConfig"]);return o!=null&&r(e,["retrievalConfig"],eu(o)),e}function nu(t){const e={},n=i(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function ou(t){const e={},n=i(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],nu(n)),e}function iu(t){const e={},n=i(t,["voiceConfig"]);if(n!=null&&r(e,["voiceConfig"],ou(n)),i(t,["multiSpeakerVoiceConfig"])!==void 0)throw new Error("multiSpeakerVoiceConfig parameter is not supported in Vertex AI.");const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function ru(t){const e={},n=i(t,["includeThoughts"]);n!=null&&r(e,["includeThoughts"],n);const o=i(t,["thinkingBudget"]);return o!=null&&r(e,["thinkingBudget"],o),e}function su(t,e,n){const o={},s=i(e,["systemInstruction"]);n!==void 0&&s!=null&&r(n,["systemInstruction"],xe($(s)));const a=i(e,["temperature"]);a!=null&&r(o,["temperature"],a);const l=i(e,["topP"]);l!=null&&r(o,["topP"],l);const c=i(e,["topK"]);c!=null&&r(o,["topK"],c);const d=i(e,["candidateCount"]);d!=null&&r(o,["candidateCount"],d);const u=i(e,["maxOutputTokens"]);u!=null&&r(o,["maxOutputTokens"],u);const f=i(e,["stopSequences"]);f!=null&&r(o,["stopSequences"],f);const p=i(e,["responseLogprobs"]);p!=null&&r(o,["responseLogprobs"],p);const h=i(e,["logprobs"]);h!=null&&r(o,["logprobs"],h);const m=i(e,["presencePenalty"]);m!=null&&r(o,["presencePenalty"],m);const g=i(e,["frequencyPenalty"]);g!=null&&r(o,["frequencyPenalty"],g);const y=i(e,["seed"]);y!=null&&r(o,["seed"],y);const v=i(e,["responseMimeType"]);v!=null&&r(o,["responseMimeType"],v);const C=i(e,["responseSchema"]);C!=null&&r(o,["responseSchema"],Vc(Ct(C)));const w=i(e,["responseJsonSchema"]);w!=null&&r(o,["responseJsonSchema"],w);const b=i(e,["routingConfig"]);b!=null&&r(o,["routingConfig"],b);const A=i(e,["modelSelectionConfig"]);A!=null&&r(o,["modelConfig"],Gc(A));const E=i(e,["safetySettings"]);if(n!==void 0&&E!=null){let O=E;Array.isArray(O)&&(O=O.map(Oe=>Bc(Oe))),r(n,["safetySettings"],O)}const M=i(e,["tools"]);if(n!==void 0&&M!=null){let O=ye(M);Array.isArray(O)&&(O=O.map(Oe=>Bo(ge(Oe)))),r(n,["tools"],O)}const _=i(e,["toolConfig"]);n!==void 0&&_!=null&&r(n,["toolConfig"],tu(_));const G=i(e,["labels"]);n!==void 0&&G!=null&&r(n,["labels"],G);const S=i(e,["cachedContent"]);n!==void 0&&S!=null&&r(n,["cachedContent"],X(t,S));const k=i(e,["responseModalities"]);k!=null&&r(o,["responseModalities"],k);const P=i(e,["mediaResolution"]);P!=null&&r(o,["mediaResolution"],P);const W=i(e,["speechConfig"]);W!=null&&r(o,["speechConfig"],iu(Tt(W)));const _t=i(e,["audioTimestamp"]);_t!=null&&r(o,["audioTimestamp"],_t);const Pt=i(e,["thinkingConfig"]);return Pt!=null&&r(o,["thinkingConfig"],ru(Pt)),o}function Wn(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["contents"]);if(s!=null){let l=H(s);Array.isArray(l)&&(l=l.map(c=>xe(c))),r(n,["contents"],l)}const a=i(e,["config"]);return a!=null&&r(n,["generationConfig"],su(t,a,n)),n}function au(t,e){const n={},o=i(t,["taskType"]);e!==void 0&&o!=null&&r(e,["instances[]","task_type"],o);const s=i(t,["title"]);e!==void 0&&s!=null&&r(e,["instances[]","title"],s);const a=i(t,["outputDimensionality"]);e!==void 0&&a!=null&&r(e,["parameters","outputDimensionality"],a);const l=i(t,["mimeType"]);e!==void 0&&l!=null&&r(e,["instances[]","mimeType"],l);const c=i(t,["autoTruncate"]);return e!==void 0&&c!=null&&r(e,["parameters","autoTruncate"],c),n}function lu(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["contents"]);s!=null&&r(n,["instances[]","content"],bo(t,s));const a=i(e,["config"]);return a!=null&&r(n,["config"],au(a,n)),n}function cu(t,e){const n={},o=i(t,["outputGcsUri"]);e!==void 0&&o!=null&&r(e,["parameters","storageUri"],o);const s=i(t,["negativePrompt"]);e!==void 0&&s!=null&&r(e,["parameters","negativePrompt"],s);const a=i(t,["numberOfImages"]);e!==void 0&&a!=null&&r(e,["parameters","sampleCount"],a);const l=i(t,["aspectRatio"]);e!==void 0&&l!=null&&r(e,["parameters","aspectRatio"],l);const c=i(t,["guidanceScale"]);e!==void 0&&c!=null&&r(e,["parameters","guidanceScale"],c);const d=i(t,["seed"]);e!==void 0&&d!=null&&r(e,["parameters","seed"],d);const u=i(t,["safetyFilterLevel"]);e!==void 0&&u!=null&&r(e,["parameters","safetySetting"],u);const f=i(t,["personGeneration"]);e!==void 0&&f!=null&&r(e,["parameters","personGeneration"],f);const p=i(t,["includeSafetyAttributes"]);e!==void 0&&p!=null&&r(e,["parameters","includeSafetyAttributes"],p);const h=i(t,["includeRaiReason"]);e!==void 0&&h!=null&&r(e,["parameters","includeRaiReason"],h);const m=i(t,["language"]);e!==void 0&&m!=null&&r(e,["parameters","language"],m);const g=i(t,["outputMimeType"]);e!==void 0&&g!=null&&r(e,["parameters","outputOptions","mimeType"],g);const y=i(t,["outputCompressionQuality"]);e!==void 0&&y!=null&&r(e,["parameters","outputOptions","compressionQuality"],y);const v=i(t,["addWatermark"]);e!==void 0&&v!=null&&r(e,["parameters","addWatermark"],v);const C=i(t,["enhancePrompt"]);return e!==void 0&&C!=null&&r(e,["parameters","enhancePrompt"],C),n}function uu(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const a=i(e,["config"]);return a!=null&&r(n,["config"],cu(a,n)),n}function Ye(t){const e={},n=i(t,["gcsUri"]);n!=null&&r(e,["gcsUri"],n);const o=i(t,["imageBytes"]);o!=null&&r(e,["bytesBase64Encoded"],Q(o));const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function du(t){const e={},n=i(t,["maskMode"]);n!=null&&r(e,["maskMode"],n);const o=i(t,["segmentationClasses"]);o!=null&&r(e,["maskClasses"],o);const s=i(t,["maskDilation"]);return s!=null&&r(e,["dilation"],s),e}function fu(t){const e={},n=i(t,["controlType"]);n!=null&&r(e,["controlType"],n);const o=i(t,["enableControlImageComputation"]);return o!=null&&r(e,["computeControl"],o),e}function pu(t){const e={},n=i(t,["styleDescription"]);return n!=null&&r(e,["styleDescription"],n),e}function hu(t){const e={},n=i(t,["subjectType"]);n!=null&&r(e,["subjectType"],n);const o=i(t,["subjectDescription"]);return o!=null&&r(e,["subjectDescription"],o),e}function mu(t){const e={},n=i(t,["referenceImage"]);n!=null&&r(e,["referenceImage"],Ye(n));const o=i(t,["referenceId"]);o!=null&&r(e,["referenceId"],o);const s=i(t,["referenceType"]);s!=null&&r(e,["referenceType"],s);const a=i(t,["maskImageConfig"]);a!=null&&r(e,["maskImageConfig"],du(a));const l=i(t,["controlImageConfig"]);l!=null&&r(e,["controlImageConfig"],fu(l));const c=i(t,["styleImageConfig"]);c!=null&&r(e,["styleImageConfig"],pu(c));const d=i(t,["subjectImageConfig"]);return d!=null&&r(e,["subjectImageConfig"],hu(d)),e}function gu(t,e){const n={},o=i(t,["outputGcsUri"]);e!==void 0&&o!=null&&r(e,["parameters","storageUri"],o);const s=i(t,["negativePrompt"]);e!==void 0&&s!=null&&r(e,["parameters","negativePrompt"],s);const a=i(t,["numberOfImages"]);e!==void 0&&a!=null&&r(e,["parameters","sampleCount"],a);const l=i(t,["aspectRatio"]);e!==void 0&&l!=null&&r(e,["parameters","aspectRatio"],l);const c=i(t,["guidanceScale"]);e!==void 0&&c!=null&&r(e,["parameters","guidanceScale"],c);const d=i(t,["seed"]);e!==void 0&&d!=null&&r(e,["parameters","seed"],d);const u=i(t,["safetyFilterLevel"]);e!==void 0&&u!=null&&r(e,["parameters","safetySetting"],u);const f=i(t,["personGeneration"]);e!==void 0&&f!=null&&r(e,["parameters","personGeneration"],f);const p=i(t,["includeSafetyAttributes"]);e!==void 0&&p!=null&&r(e,["parameters","includeSafetyAttributes"],p);const h=i(t,["includeRaiReason"]);e!==void 0&&h!=null&&r(e,["parameters","includeRaiReason"],h);const m=i(t,["language"]);e!==void 0&&m!=null&&r(e,["parameters","language"],m);const g=i(t,["outputMimeType"]);e!==void 0&&g!=null&&r(e,["parameters","outputOptions","mimeType"],g);const y=i(t,["outputCompressionQuality"]);e!==void 0&&y!=null&&r(e,["parameters","outputOptions","compressionQuality"],y);const v=i(t,["editMode"]);e!==void 0&&v!=null&&r(e,["parameters","editMode"],v);const C=i(t,["baseSteps"]);return e!==void 0&&C!=null&&r(e,["parameters","editConfig","baseSteps"],C),n}function yu(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const a=i(e,["referenceImages"]);if(a!=null){let c=a;Array.isArray(c)&&(c=c.map(d=>mu(d))),r(n,["instances[0]","referenceImages"],c)}const l=i(e,["config"]);return l!=null&&r(n,["config"],gu(l,n)),n}function vu(t,e){const n={},o=i(t,["includeRaiReason"]);e!==void 0&&o!=null&&r(e,["parameters","includeRaiReason"],o);const s=i(t,["outputMimeType"]);e!==void 0&&s!=null&&r(e,["parameters","outputOptions","mimeType"],s);const a=i(t,["outputCompressionQuality"]);e!==void 0&&a!=null&&r(e,["parameters","outputOptions","compressionQuality"],a);const l=i(t,["enhanceInputImage"]);e!==void 0&&l!=null&&r(e,["parameters","upscaleConfig","enhanceInputImage"],l);const c=i(t,["imagePreservationFactor"]);e!==void 0&&c!=null&&r(e,["parameters","upscaleConfig","imagePreservationFactor"],c);const d=i(t,["numberOfImages"]);e!==void 0&&d!=null&&r(e,["parameters","sampleCount"],d);const u=i(t,["mode"]);return e!==void 0&&u!=null&&r(e,["parameters","mode"],u),n}function xu(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["image"]);s!=null&&r(n,["instances[0]","image"],Ye(s));const a=i(e,["upscaleFactor"]);a!=null&&r(n,["parameters","upscaleConfig","upscaleFactor"],a);const l=i(e,["config"]);return l!=null&&r(n,["config"],vu(l,n)),n}function Cu(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","name"],I(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Tu(t,e,n){const o={},s=i(e,["pageSize"]);n!==void 0&&s!=null&&r(n,["_query","pageSize"],s);const a=i(e,["pageToken"]);n!==void 0&&a!=null&&r(n,["_query","pageToken"],a);const l=i(e,["filter"]);n!==void 0&&l!=null&&r(n,["_query","filter"],l);const c=i(e,["queryBase"]);return n!==void 0&&c!=null&&r(n,["_url","models_url"],So(t,c)),o}function wu(t,e){const n={},o=i(e,["config"]);return o!=null&&r(n,["config"],Tu(t,o,n)),n}function bu(t,e){const n={},o=i(t,["displayName"]);e!==void 0&&o!=null&&r(e,["displayName"],o);const s=i(t,["description"]);e!==void 0&&s!=null&&r(e,["description"],s);const a=i(t,["defaultCheckpointId"]);return e!==void 0&&a!=null&&r(e,["defaultCheckpointId"],a),n}function Eu(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],bu(s,n)),n}function Au(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","name"],I(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Su(t,e){const n={},o=i(t,["systemInstruction"]);e!==void 0&&o!=null&&r(e,["systemInstruction"],xe($(o)));const s=i(t,["tools"]);if(e!==void 0&&s!=null){let l=s;Array.isArray(l)&&(l=l.map(c=>Bo(c))),r(e,["tools"],l)}const a=i(t,["generationConfig"]);return e!==void 0&&a!=null&&r(e,["generationConfig"],a),n}function Mu(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["contents"]);if(s!=null){let l=H(s);Array.isArray(l)&&(l=l.map(c=>xe(c))),r(n,["contents"],l)}const a=i(e,["config"]);return a!=null&&r(n,["config"],Su(a,n)),n}function _u(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["contents"]);if(s!=null){let l=H(s);Array.isArray(l)&&(l=l.map(c=>xe(c))),r(n,["contents"],l)}const a=i(e,["config"]);return a!=null&&r(n,["config"],a),n}function Pu(t){const e={},n=i(t,["uri"]);n!=null&&r(e,["gcsUri"],n);const o=i(t,["videoBytes"]);o!=null&&r(e,["bytesBase64Encoded"],Q(o));const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Iu(t,e){const n={},o=i(t,["numberOfVideos"]);e!==void 0&&o!=null&&r(e,["parameters","sampleCount"],o);const s=i(t,["outputGcsUri"]);e!==void 0&&s!=null&&r(e,["parameters","storageUri"],s);const a=i(t,["fps"]);e!==void 0&&a!=null&&r(e,["parameters","fps"],a);const l=i(t,["durationSeconds"]);e!==void 0&&l!=null&&r(e,["parameters","durationSeconds"],l);const c=i(t,["seed"]);e!==void 0&&c!=null&&r(e,["parameters","seed"],c);const d=i(t,["aspectRatio"]);e!==void 0&&d!=null&&r(e,["parameters","aspectRatio"],d);const u=i(t,["resolution"]);e!==void 0&&u!=null&&r(e,["parameters","resolution"],u);const f=i(t,["personGeneration"]);e!==void 0&&f!=null&&r(e,["parameters","personGeneration"],f);const p=i(t,["pubsubTopic"]);e!==void 0&&p!=null&&r(e,["parameters","pubsubTopic"],p);const h=i(t,["negativePrompt"]);e!==void 0&&h!=null&&r(e,["parameters","negativePrompt"],h);const m=i(t,["enhancePrompt"]);e!==void 0&&m!=null&&r(e,["parameters","enhancePrompt"],m);const g=i(t,["generateAudio"]);e!==void 0&&g!=null&&r(e,["parameters","generateAudio"],g);const y=i(t,["lastFrame"]);e!==void 0&&y!=null&&r(e,["instances[0]","lastFrame"],Ye(y));const v=i(t,["compressionQuality"]);return e!==void 0&&v!=null&&r(e,["parameters","compressionQuality"],v),n}function ku(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],I(t,o));const s=i(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const a=i(e,["image"]);a!=null&&r(n,["instances[0]","image"],Ye(a));const l=i(e,["video"]);l!=null&&r(n,["instances[0]","video"],Pu(l));const c=i(e,["config"]);return c!=null&&r(n,["config"],Iu(c,n)),n}function Ru(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Du(t){const e={},n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Nu(t){const e={},n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Fu(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Ru(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],Du(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],Nu(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Lu(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Fu(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Uu(t){const e={},n=i(t,["citationSources"]);return n!=null&&r(e,["citations"],n),e}function $u(t){const e={},n=i(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const o=i(t,["urlRetrievalStatus"]);return o!=null&&r(e,["urlRetrievalStatus"],o),e}function Vu(t){const e={},n=i(t,["urlMetadata"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>$u(s))),r(e,["urlMetadata"],o)}return e}function Gu(t){const e={},n=i(t,["content"]);n!=null&&r(e,["content"],Lu(n));const o=i(t,["citationMetadata"]);o!=null&&r(e,["citationMetadata"],Uu(o));const s=i(t,["tokenCount"]);s!=null&&r(e,["tokenCount"],s);const a=i(t,["finishReason"]);a!=null&&r(e,["finishReason"],a);const l=i(t,["urlContextMetadata"]);l!=null&&r(e,["urlContextMetadata"],Vu(l));const c=i(t,["avgLogprobs"]);c!=null&&r(e,["avgLogprobs"],c);const d=i(t,["groundingMetadata"]);d!=null&&r(e,["groundingMetadata"],d);const u=i(t,["index"]);u!=null&&r(e,["index"],u);const f=i(t,["logprobsResult"]);f!=null&&r(e,["logprobsResult"],f);const p=i(t,["safetyRatings"]);return p!=null&&r(e,["safetyRatings"],p),e}function Yn(t){const e={},n=i(t,["candidates"]);if(n!=null){let l=n;Array.isArray(l)&&(l=l.map(c=>Gu(c))),r(e,["candidates"],l)}const o=i(t,["modelVersion"]);o!=null&&r(e,["modelVersion"],o);const s=i(t,["promptFeedback"]);s!=null&&r(e,["promptFeedback"],s);const a=i(t,["usageMetadata"]);return a!=null&&r(e,["usageMetadata"],a),e}function Bu(t){const e={},n=i(t,["values"]);return n!=null&&r(e,["values"],n),e}function qu(){return{}}function zu(t){const e={},n=i(t,["embeddings"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Bu(a))),r(e,["embeddings"],s)}return i(t,["metadata"])!=null&&r(e,["metadata"],qu()),e}function Hu(t){const e={},n=i(t,["bytesBase64Encoded"]);n!=null&&r(e,["imageBytes"],Q(n));const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function qo(t){const e={},n=i(t,["safetyAttributes","categories"]);n!=null&&r(e,["categories"],n);const o=i(t,["safetyAttributes","scores"]);o!=null&&r(e,["scores"],o);const s=i(t,["contentType"]);return s!=null&&r(e,["contentType"],s),e}function Ju(t){const e={},n=i(t,["_self"]);n!=null&&r(e,["image"],Hu(n));const o=i(t,["raiFilteredReason"]);o!=null&&r(e,["raiFilteredReason"],o);const s=i(t,["_self"]);return s!=null&&r(e,["safetyAttributes"],qo(s)),e}function Wu(t){const e={},n=i(t,["predictions"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Ju(a))),r(e,["generatedImages"],s)}const o=i(t,["positivePromptSafetyAttributes"]);return o!=null&&r(e,["positivePromptSafetyAttributes"],qo(o)),e}function Yu(t){const e={},n=i(t,["baseModel"]);n!=null&&r(e,["baseModel"],n);const o=i(t,["createTime"]);o!=null&&r(e,["createTime"],o);const s=i(t,["updateTime"]);return s!=null&&r(e,["updateTime"],s),e}function ct(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["description"]);s!=null&&r(e,["description"],s);const a=i(t,["version"]);a!=null&&r(e,["version"],a);const l=i(t,["_self"]);l!=null&&r(e,["tunedModelInfo"],Yu(l));const c=i(t,["inputTokenLimit"]);c!=null&&r(e,["inputTokenLimit"],c);const d=i(t,["outputTokenLimit"]);d!=null&&r(e,["outputTokenLimit"],d);const u=i(t,["supportedGenerationMethods"]);return u!=null&&r(e,["supportedActions"],u),e}function Ku(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["_self"]);if(o!=null){let s=Mo(o);Array.isArray(s)&&(s=s.map(a=>ct(a))),r(e,["models"],s)}return e}function Ou(){return{}}function Xu(t){const e={},n=i(t,["totalTokens"]);n!=null&&r(e,["totalTokens"],n);const o=i(t,["cachedContentTokenCount"]);return o!=null&&r(e,["cachedContentTokenCount"],o),e}function Qu(t){const e={},n=i(t,["video","uri"]);n!=null&&r(e,["uri"],n);const o=i(t,["video","encodedVideo"]);o!=null&&r(e,["videoBytes"],Q(o));const s=i(t,["encoding"]);return s!=null&&r(e,["mimeType"],s),e}function Zu(t){const e={},n=i(t,["_self"]);return n!=null&&r(e,["video"],Qu(n)),e}function ju(t){const e={},n=i(t,["generatedSamples"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(l=>Zu(l))),r(e,["generatedVideos"],a)}const o=i(t,["raiMediaFilteredCount"]);o!=null&&r(e,["raiMediaFilteredCount"],o);const s=i(t,["raiMediaFilteredReasons"]);return s!=null&&r(e,["raiMediaFilteredReasons"],s),e}function ed(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["metadata"]);o!=null&&r(e,["metadata"],o);const s=i(t,["done"]);s!=null&&r(e,["done"],s);const a=i(t,["error"]);a!=null&&r(e,["error"],a);const l=i(t,["response","generateVideoResponse"]);return l!=null&&r(e,["response"],ju(l)),e}function td(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function nd(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["data"]);o!=null&&r(e,["data"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function od(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["fileUri"]);o!=null&&r(e,["fileUri"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function id(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],td(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],nd(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],od(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function rd(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>id(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function sd(t){const e={},n=i(t,["citations"]);return n!=null&&r(e,["citations"],n),e}function ad(t){const e={},n=i(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const o=i(t,["urlRetrievalStatus"]);return o!=null&&r(e,["urlRetrievalStatus"],o),e}function ld(t){const e={},n=i(t,["urlMetadata"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>ad(s))),r(e,["urlMetadata"],o)}return e}function cd(t){const e={},n=i(t,["content"]);n!=null&&r(e,["content"],rd(n));const o=i(t,["citationMetadata"]);o!=null&&r(e,["citationMetadata"],sd(o));const s=i(t,["finishMessage"]);s!=null&&r(e,["finishMessage"],s);const a=i(t,["finishReason"]);a!=null&&r(e,["finishReason"],a);const l=i(t,["urlContextMetadata"]);l!=null&&r(e,["urlContextMetadata"],ld(l));const c=i(t,["avgLogprobs"]);c!=null&&r(e,["avgLogprobs"],c);const d=i(t,["groundingMetadata"]);d!=null&&r(e,["groundingMetadata"],d);const u=i(t,["index"]);u!=null&&r(e,["index"],u);const f=i(t,["logprobsResult"]);f!=null&&r(e,["logprobsResult"],f);const p=i(t,["safetyRatings"]);return p!=null&&r(e,["safetyRatings"],p),e}function Kn(t){const e={},n=i(t,["candidates"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(u=>cd(u))),r(e,["candidates"],d)}const o=i(t,["createTime"]);o!=null&&r(e,["createTime"],o);const s=i(t,["responseId"]);s!=null&&r(e,["responseId"],s);const a=i(t,["modelVersion"]);a!=null&&r(e,["modelVersion"],a);const l=i(t,["promptFeedback"]);l!=null&&r(e,["promptFeedback"],l);const c=i(t,["usageMetadata"]);return c!=null&&r(e,["usageMetadata"],c),e}function ud(t){const e={},n=i(t,["truncated"]);n!=null&&r(e,["truncated"],n);const o=i(t,["token_count"]);return o!=null&&r(e,["tokenCount"],o),e}function dd(t){const e={},n=i(t,["values"]);n!=null&&r(e,["values"],n);const o=i(t,["statistics"]);return o!=null&&r(e,["statistics"],ud(o)),e}function fd(t){const e={},n=i(t,["billableCharacterCount"]);return n!=null&&r(e,["billableCharacterCount"],n),e}function pd(t){const e={},n=i(t,["predictions[]","embeddings"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>dd(a))),r(e,["embeddings"],s)}const o=i(t,["metadata"]);return o!=null&&r(e,["metadata"],fd(o)),e}function hd(t){const e={},n=i(t,["gcsUri"]);n!=null&&r(e,["gcsUri"],n);const o=i(t,["bytesBase64Encoded"]);o!=null&&r(e,["imageBytes"],Q(o));const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function zo(t){const e={},n=i(t,["safetyAttributes","categories"]);n!=null&&r(e,["categories"],n);const o=i(t,["safetyAttributes","scores"]);o!=null&&r(e,["scores"],o);const s=i(t,["contentType"]);return s!=null&&r(e,["contentType"],s),e}function bt(t){const e={},n=i(t,["_self"]);n!=null&&r(e,["image"],hd(n));const o=i(t,["raiFilteredReason"]);o!=null&&r(e,["raiFilteredReason"],o);const s=i(t,["_self"]);s!=null&&r(e,["safetyAttributes"],zo(s));const a=i(t,["prompt"]);return a!=null&&r(e,["enhancedPrompt"],a),e}function md(t){const e={},n=i(t,["predictions"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>bt(a))),r(e,["generatedImages"],s)}const o=i(t,["positivePromptSafetyAttributes"]);return o!=null&&r(e,["positivePromptSafetyAttributes"],zo(o)),e}function gd(t){const e={},n=i(t,["predictions"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>bt(s))),r(e,["generatedImages"],o)}return e}function yd(t){const e={},n=i(t,["predictions"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>bt(s))),r(e,["generatedImages"],o)}return e}function vd(t){const e={},n=i(t,["endpoint"]);n!=null&&r(e,["name"],n);const o=i(t,["deployedModelId"]);return o!=null&&r(e,["deployedModelId"],o),e}function xd(t){const e={},n=i(t,["labels","google-vertex-llm-tuning-base-model-id"]);n!=null&&r(e,["baseModel"],n);const o=i(t,["createTime"]);o!=null&&r(e,["createTime"],o);const s=i(t,["updateTime"]);return s!=null&&r(e,["updateTime"],s),e}function Cd(t){const e={},n=i(t,["checkpointId"]);n!=null&&r(e,["checkpointId"],n);const o=i(t,["epoch"]);o!=null&&r(e,["epoch"],o);const s=i(t,["step"]);return s!=null&&r(e,["step"],s),e}function ut(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["description"]);s!=null&&r(e,["description"],s);const a=i(t,["versionId"]);a!=null&&r(e,["version"],a);const l=i(t,["deployedModels"]);if(l!=null){let p=l;Array.isArray(p)&&(p=p.map(h=>vd(h))),r(e,["endpoints"],p)}const c=i(t,["labels"]);c!=null&&r(e,["labels"],c);const d=i(t,["_self"]);d!=null&&r(e,["tunedModelInfo"],xd(d));const u=i(t,["defaultCheckpointId"]);u!=null&&r(e,["defaultCheckpointId"],u);const f=i(t,["checkpoints"]);if(f!=null){let p=f;Array.isArray(p)&&(p=p.map(h=>Cd(h))),r(e,["checkpoints"],p)}return e}function Td(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["_self"]);if(o!=null){let s=Mo(o);Array.isArray(s)&&(s=s.map(a=>ut(a))),r(e,["models"],s)}return e}function wd(){return{}}function bd(t){const e={},n=i(t,["totalTokens"]);return n!=null&&r(e,["totalTokens"],n),e}function Ed(t){const e={},n=i(t,["tokensInfo"]);return n!=null&&r(e,["tokensInfo"],n),e}function Ad(t){const e={},n=i(t,["gcsUri"]);n!=null&&r(e,["uri"],n);const o=i(t,["bytesBase64Encoded"]);o!=null&&r(e,["videoBytes"],Q(o));const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Sd(t){const e={},n=i(t,["_self"]);return n!=null&&r(e,["video"],Ad(n)),e}function Md(t){const e={},n=i(t,["videos"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(l=>Sd(l))),r(e,["generatedVideos"],a)}const o=i(t,["raiMediaFilteredCount"]);o!=null&&r(e,["raiMediaFilteredCount"],o);const s=i(t,["raiMediaFilteredReasons"]);return s!=null&&r(e,["raiMediaFilteredReasons"],s),e}function _d(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["metadata"]);o!=null&&r(e,["metadata"],o);const s=i(t,["done"]);s!=null&&r(e,["done"],s);const a=i(t,["error"]);a!=null&&r(e,["error"],a);const l=i(t,["response"]);return l!=null&&r(e,["response"],Md(l)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Pd="Content-Type",Id="X-Server-Timeout",kd="User-Agent",dt="x-goog-api-client",Rd="1.9.0",Dd=`google-genai-sdk/${Rd}`,Nd="v1beta1",Fd="v1beta",On=/^data: (.*)(?:\n\n|\r\r|\r\n\r\n)/;class Ld{constructor(e){var n,o;this.clientOptions=Object.assign(Object.assign({},e),{project:e.project,location:e.location,apiKey:e.apiKey,vertexai:e.vertexai});const s={};this.clientOptions.vertexai?(s.apiVersion=(n=this.clientOptions.apiVersion)!==null&&n!==void 0?n:Nd,s.baseUrl=this.baseUrlFromProjectLocation(),this.normalizeAuthParameters()):(s.apiVersion=(o=this.clientOptions.apiVersion)!==null&&o!==void 0?o:Fd,s.baseUrl="https://generativelanguage.googleapis.com/"),s.headers=this.getDefaultHeaders(),this.clientOptions.httpOptions=s,e.httpOptions&&(this.clientOptions.httpOptions=this.patchHttpOptions(s,e.httpOptions))}baseUrlFromProjectLocation(){return this.clientOptions.project&&this.clientOptions.location&&this.clientOptions.location!=="global"?`https://${this.clientOptions.location}-aiplatform.googleapis.com/`:"https://aiplatform.googleapis.com/"}normalizeAuthParameters(){if(this.clientOptions.project&&this.clientOptions.location){this.clientOptions.apiKey=void 0;return}this.clientOptions.project=void 0,this.clientOptions.location=void 0}isVertexAI(){var e;return(e=this.clientOptions.vertexai)!==null&&e!==void 0?e:!1}getProject(){return this.clientOptions.project}getLocation(){return this.clientOptions.location}getApiVersion(){if(this.clientOptions.httpOptions&&this.clientOptions.httpOptions.apiVersion!==void 0)return this.clientOptions.httpOptions.apiVersion;throw new Error("API version is not set.")}getBaseUrl(){if(this.clientOptions.httpOptions&&this.clientOptions.httpOptions.baseUrl!==void 0)return this.clientOptions.httpOptions.baseUrl;throw new Error("Base URL is not set.")}getRequestUrl(){return this.getRequestUrlInternal(this.clientOptions.httpOptions)}getHeaders(){if(this.clientOptions.httpOptions&&this.clientOptions.httpOptions.headers!==void 0)return this.clientOptions.httpOptions.headers;throw new Error("Headers are not set.")}getRequestUrlInternal(e){if(!e||e.baseUrl===void 0||e.apiVersion===void 0)throw new Error("HTTP options are not correctly set.");const o=[e.baseUrl.endsWith("/")?e.baseUrl.slice(0,-1):e.baseUrl];return e.apiVersion&&e.apiVersion!==""&&o.push(e.apiVersion),o.join("/")}getBaseResourcePath(){return`projects/${this.clientOptions.project}/locations/${this.clientOptions.location}`}getApiKey(){return this.clientOptions.apiKey}getWebsocketBaseUrl(){const e=this.getBaseUrl(),n=new URL(e);return n.protocol=n.protocol=="http:"?"ws":"wss",n.toString()}setBaseUrl(e){if(this.clientOptions.httpOptions)this.clientOptions.httpOptions.baseUrl=e;else throw new Error("HTTP options are not correctly set.")}constructUrl(e,n,o){const s=[this.getRequestUrlInternal(n)];return o&&s.push(this.getBaseResourcePath()),e!==""&&s.push(e),new URL(`${s.join("/")}`)}shouldPrependVertexProjectPath(e){return!(this.clientOptions.apiKey||!this.clientOptions.vertexai||e.path.startsWith("projects/")||e.httpMethod==="GET"&&e.path.startsWith("publishers/google/models"))}async request(e){let n=this.clientOptions.httpOptions;e.httpOptions&&(n=this.patchHttpOptions(this.clientOptions.httpOptions,e.httpOptions));const o=this.shouldPrependVertexProjectPath(e),s=this.constructUrl(e.path,n,o);if(e.queryParams)for(const[l,c]of Object.entries(e.queryParams))s.searchParams.append(l,String(c));let a={};if(e.httpMethod==="GET"){if(e.body&&e.body!=="{}")throw new Error("Request body should be empty for GET request, but got non empty request body")}else a.body=e.body;return a=await this.includeExtraHttpOptionsToRequestInit(a,n,e.abortSignal),this.unaryApiCall(s,a,e.httpMethod)}patchHttpOptions(e,n){const o=JSON.parse(JSON.stringify(e));for(const[s,a]of Object.entries(n))typeof a=="object"?o[s]=Object.assign(Object.assign({},o[s]),a):a!==void 0&&(o[s]=a);return o}async requestStream(e){let n=this.clientOptions.httpOptions;e.httpOptions&&(n=this.patchHttpOptions(this.clientOptions.httpOptions,e.httpOptions));const o=this.shouldPrependVertexProjectPath(e),s=this.constructUrl(e.path,n,o);(!s.searchParams.has("alt")||s.searchParams.get("alt")!=="sse")&&s.searchParams.set("alt","sse");let a={};return a.body=e.body,a=await this.includeExtraHttpOptionsToRequestInit(a,n,e.abortSignal),this.streamApiCall(s,a,e.httpMethod)}async includeExtraHttpOptionsToRequestInit(e,n,o){if(n&&n.timeout||o){const s=new AbortController,a=s.signal;if(n.timeout&&(n==null?void 0:n.timeout)>0){const l=setTimeout(()=>s.abort(),n.timeout);l&&typeof l.unref=="function"&&l.unref()}o&&o.addEventListener("abort",()=>{s.abort()}),e.signal=a}return n&&n.extraBody!==null&&Ud(e,n.extraBody),e.headers=await this.getHeadersInternal(n),e}async unaryApiCall(e,n,o){return this.apiCall(e.toString(),Object.assign(Object.assign({},n),{method:o})).then(async s=>(await Xn(s),new ot(s))).catch(s=>{throw s instanceof Error?s:new Error(JSON.stringify(s))})}async streamApiCall(e,n,o){return this.apiCall(e.toString(),Object.assign(Object.assign({},n),{method:o})).then(async s=>(await Xn(s),this.processStreamResponse(s))).catch(s=>{throw s instanceof Error?s:new Error(JSON.stringify(s))})}processStreamResponse(e){var n;return fe(this,arguments,function*(){const s=(n=e==null?void 0:e.body)===null||n===void 0?void 0:n.getReader(),a=new TextDecoder("utf-8");if(!s)throw new Error("Response body is empty");try{let l="";for(;;){const{done:c,value:d}=yield D(s.read());if(c){if(l.trim().length>0)throw new Error("Incomplete JSON segment at the end");break}const u=a.decode(d,{stream:!0});try{const p=JSON.parse(u);if("error"in p){const h=JSON.parse(JSON.stringify(p.error)),m=h.status,g=h.code,y=`got status: ${m}. ${JSON.stringify(p)}`;if(g>=400&&g<600)throw new Je({message:y,status:g})}}catch(p){if(p.name==="ApiError")throw p}l+=u;let f=l.match(On);for(;f;){const p=f[1];try{const h=new Response(p,{headers:e==null?void 0:e.headers,status:e==null?void 0:e.status,statusText:e==null?void 0:e.statusText});yield yield D(new ot(h)),l=l.slice(f[0].length),f=l.match(On)}catch(h){throw new Error(`exception parsing stream chunk ${p}. ${h}`)}}}}finally{s.releaseLock()}})}async apiCall(e,n){return fetch(e,n).catch(o=>{throw new Error(`exception ${o} sending request`)})}getDefaultHeaders(){const e={},n=Dd+" "+this.clientOptions.userAgentExtra;return e[kd]=n,e[dt]=n,e[Pd]="application/json",e}async getHeadersInternal(e){const n=new Headers;if(e&&e.headers){for(const[o,s]of Object.entries(e.headers))n.append(o,s);e.timeout&&e.timeout>0&&n.append(Id,String(Math.ceil(e.timeout/1e3)))}return await this.clientOptions.auth.addAuthHeaders(n),n}async uploadFile(e,n){var o;const s={};n!=null&&(s.mimeType=n.mimeType,s.name=n.name,s.displayName=n.displayName),s.name&&!s.name.startsWith("files/")&&(s.name=`files/${s.name}`);const a=this.clientOptions.uploader,l=await a.stat(e);s.sizeBytes=String(l.size);const c=(o=n==null?void 0:n.mimeType)!==null&&o!==void 0?o:l.type;if(c===void 0||c==="")throw new Error("Can not determine mimeType. Please provide mimeType in the config.");s.mimeType=c;const d=await this.fetchUploadUrl(s,n);return a.upload(e,d,this)}async downloadFile(e){await this.clientOptions.downloader.download(e,this)}async fetchUploadUrl(e,n){var o;let s={};n!=null&&n.httpOptions?s=n.httpOptions:s={apiVersion:"",headers:{"Content-Type":"application/json","X-Goog-Upload-Protocol":"resumable","X-Goog-Upload-Command":"start","X-Goog-Upload-Header-Content-Length":`${e.sizeBytes}`,"X-Goog-Upload-Header-Content-Type":`${e.mimeType}`}};const a={file:e},l=await this.request({path:x("upload/v1beta/files",a._url),body:JSON.stringify(a),httpMethod:"POST",httpOptions:s});if(!l||!(l!=null&&l.headers))throw new Error("Server did not return an HttpResponse or the returned HttpResponse did not have headers.");const c=(o=l==null?void 0:l.headers)===null||o===void 0?void 0:o["x-goog-upload-url"];if(c===void 0)throw new Error("Failed to get upload url. Server did not return the x-google-upload-url in the headers");return c}}async function Xn(t){var e;if(t===void 0)throw new Error("response is undefined");if(!t.ok){const n=t.status;let o;!((e=t.headers.get("content-type"))===null||e===void 0)&&e.includes("application/json")?o=await t.json():o={error:{message:await t.text(),code:t.status,status:t.statusText}};const s=JSON.stringify(o);throw n>=400&&n<600?new Je({message:s,status:n}):new Error(s)}}function Ud(t,e){if(!e||Object.keys(e).length===0)return;if(t.body instanceof Blob){console.warn("includeExtraBodyToRequestInit: extraBody provided but current request body is a Blob. extraBody will be ignored as merging is not supported for Blob bodies.");return}let n={};if(typeof t.body=="string"&&t.body.length>0)try{const a=JSON.parse(t.body);if(typeof a=="object"&&a!==null&&!Array.isArray(a))n=a;else{console.warn("includeExtraBodyToRequestInit: Original request body is valid JSON but not a non-array object. Skip applying extraBody to the request body.");return}}catch{console.warn("includeExtraBodyToRequestInit: Original request body is not valid JSON. Skip applying extraBody to the request body.");return}function o(a,l){const c=Object.assign({},a);for(const d in l)if(Object.prototype.hasOwnProperty.call(l,d)){const u=l[d],f=c[d];u&&typeof u=="object"&&!Array.isArray(u)&&f&&typeof f=="object"&&!Array.isArray(f)?c[d]=o(f,u):(f&&u&&typeof f!=typeof u&&console.warn(`includeExtraBodyToRequestInit:deepMerge: Type mismatch for key "${d}". Original type: ${typeof f}, New type: ${typeof u}. Overwriting.`),c[d]=u)}return c}const s=o(n,e);t.body=JSON.stringify(s)}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const $d="mcp_used/unknown";function Ho(t){for(const e of t)if(Et(e)||typeof e=="object"&&"inputSchema"in e)return!0;return!1}function Jo(t){var e;const n=(e=t[dt])!==null&&e!==void 0?e:"";t[dt]=(n+` ${$d}`).trimStart()}function Vd(t){var e,n,o;return(o=(n=(e=t.config)===null||e===void 0?void 0:e.tools)===null||n===void 0?void 0:n.some(s=>Et(s)))!==null&&o!==void 0?o:!1}function Gd(t){var e,n,o;return(o=(n=(e=t.config)===null||e===void 0?void 0:e.tools)===null||n===void 0?void 0:n.some(s=>!Et(s)))!==null&&o!==void 0?o:!1}function Et(t){return t!==null&&typeof t=="object"&&t instanceof At}function Bd(t,e=100){return fe(this,arguments,function*(){let o,s=0;for(;s<e;){const a=yield D(t.listTools({cursor:o}));for(const l of a.tools)yield yield D(l),s++;if(!a.nextCursor)break;o=a.nextCursor}})}class At{constructor(e=[],n){this.mcpTools=[],this.functionNameToMcpClient={},this.mcpClients=e,this.config=n}static create(e,n){return new At(e,n)}async initialize(){var e,n,o,s;if(this.mcpTools.length>0)return;const a={},l=[];for(const f of this.mcpClients)try{for(var c=!0,d=(n=void 0,Ee(Bd(f))),u;u=await d.next(),e=u.done,!e;c=!0){s=u.value,c=!1;const p=s;l.push(p);const h=p.name;if(a[h])throw new Error(`Duplicate function name ${h} found in MCP tools. Please ensure function names are unique.`);a[h]=f}}catch(p){n={error:p}}finally{try{!c&&!e&&(o=d.return)&&await o.call(d)}finally{if(n)throw n.error}}this.mcpTools=l,this.functionNameToMcpClient=a}async tool(){return await this.initialize(),Li(this.mcpTools,this.config)}async callTool(e){await this.initialize();const n=[];for(const o of e)if(o.name in this.functionNameToMcpClient){const s=this.functionNameToMcpClient[o.name];let a;this.config.timeout&&(a={timeout:this.config.timeout});const l=await s.callTool({name:o.name,arguments:o.args},void 0,a);n.push({functionResponse:{name:o.name,response:l.isError?{error:l}:l}})}return n}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */async function qd(t,e,n){const o=new _i;let s;n.data instanceof Blob?s=JSON.parse(await n.data.text()):s=JSON.parse(n.data);const a=Xl(s);Object.assign(o,a),e(o)}class zd{constructor(e,n,o){this.apiClient=e,this.auth=n,this.webSocketFactory=o}async connect(e){var n,o;if(this.apiClient.isVertexAI())throw new Error("Live music is not supported for Vertex AI.");console.warn("Live music generation is experimental and may change in future versions.");const s=this.apiClient.getWebsocketBaseUrl(),a=this.apiClient.getApiVersion(),l=Wd(this.apiClient.getDefaultHeaders()),c=this.apiClient.getApiKey(),d=`${s}/ws/google.ai.generativelanguage.${a}.GenerativeService.BidiGenerateMusic?key=${c}`;let u=()=>{};const f=new Promise(b=>{u=b}),p=e.callbacks,h=function(){u({})},m=this.apiClient,g={onopen:h,onmessage:b=>{qd(m,p.onmessage,b)},onerror:(n=p==null?void 0:p.onerror)!==null&&n!==void 0?n:function(b){},onclose:(o=p==null?void 0:p.onclose)!==null&&o!==void 0?o:function(b){}},y=this.webSocketFactory.create(d,Jd(l),g);y.connect(),await f;const v=I(this.apiClient,e.model),C=$o({model:v}),w=lt({setup:C});return y.send(JSON.stringify(w)),new Hd(y,this.apiClient)}}class Hd{constructor(e,n){this.conn=e,this.apiClient=n}async setWeightedPrompts(e){if(!e.weightedPrompts||Object.keys(e.weightedPrompts).length===0)throw new Error("Weighted prompts must be set and contain at least one entry.");const n=ul(e),o=Vo(n);this.conn.send(JSON.stringify({clientContent:o}))}async setMusicGenerationConfig(e){e.musicGenerationConfig||(e.musicGenerationConfig={});const n=dl(e),o=lt(n);this.conn.send(JSON.stringify(o))}sendPlaybackControl(e){const n=lt({playbackControl:e});this.conn.send(JSON.stringify(n))}play(){this.sendPlaybackControl(ue.PLAY)}pause(){this.sendPlaybackControl(ue.PAUSE)}stop(){this.sendPlaybackControl(ue.STOP)}resetContext(){this.sendPlaybackControl(ue.RESET_CONTEXT)}close(){this.conn.close()}}function Jd(t){const e={};return t.forEach((n,o)=>{e[o]=n}),e}function Wd(t){const e=new Headers;for(const[n,o]of Object.entries(t))e.append(n,o);return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Yd="FunctionResponse request must have an `id` field from the response of a ToolCall.FunctionalCalls in Google AI.";async function Kd(t,e,n){const o=new Mi;let s;n.data instanceof Blob?s=await n.data.text():n.data instanceof ArrayBuffer?s=new TextDecoder().decode(n.data):s=n.data;const a=JSON.parse(s);if(t.isVertexAI()){const l=Bl(a);Object.assign(o,l)}else{const l=Gl(a);Object.assign(o,l)}e(o)}class Od{constructor(e,n,o){this.apiClient=e,this.auth=n,this.webSocketFactory=o,this.music=new zd(this.apiClient,this.auth,this.webSocketFactory)}async connect(e){var n,o,s,a,l,c;const d=this.apiClient.getWebsocketBaseUrl(),u=this.apiClient.getApiVersion();let f;const p=this.apiClient.getDefaultHeaders();e.config&&e.config.tools&&Ho(e.config.tools)&&Jo(p);const h=jd(p);if(this.apiClient.isVertexAI())f=`${d}/ws/google.cloud.aiplatform.${u}.LlmBidiService/BidiGenerateContent`,await this.auth.addAuthHeaders(h);else{const S=this.apiClient.getApiKey();let k="BidiGenerateContent",P="key";S!=null&&S.startsWith("auth_tokens/")&&(console.warn("Warning: Ephemeral token support is experimental and may change in future versions."),u!=="v1alpha"&&console.warn("Warning: The SDK's ephemeral token support is in v1alpha only. Please use const ai = new GoogleGenAI({apiKey: token.name, httpOptions: { apiVersion: 'v1alpha' }}); before session connection."),k="BidiGenerateContentConstrained",P="access_token"),f=`${d}/ws/google.ai.generativelanguage.${u}.GenerativeService.${k}?${P}=${S}`}let m=()=>{};const g=new Promise(S=>{m=S}),y=e.callbacks,v=function(){var S;(S=y==null?void 0:y.onopen)===null||S===void 0||S.call(y),m({})},C=this.apiClient,w={onopen:v,onmessage:S=>{Kd(C,y.onmessage,S)},onerror:(n=y==null?void 0:y.onerror)!==null&&n!==void 0?n:function(S){},onclose:(o=y==null?void 0:y.onclose)!==null&&o!==void 0?o:function(S){}},b=this.webSocketFactory.create(f,Zd(h),w);b.connect(),await g;let A=I(this.apiClient,e.model);if(this.apiClient.isVertexAI()&&A.startsWith("publishers/")){const S=this.apiClient.getProject(),k=this.apiClient.getLocation();A=`projects/${S}/locations/${k}/`+A}let E={};this.apiClient.isVertexAI()&&((s=e.config)===null||s===void 0?void 0:s.responseModalities)===void 0&&(e.config===void 0?e.config={responseModalities:[Be.AUDIO]}:e.config.responseModalities=[Be.AUDIO]),!((a=e.config)===null||a===void 0)&&a.generationConfig&&console.warn("Setting `LiveConnectConfig.generation_config` is deprecated, please set the fields on `LiveConnectConfig` directly. This will become an error in a future version (not before Q3 2025).");const M=(c=(l=e.config)===null||l===void 0?void 0:l.tools)!==null&&c!==void 0?c:[],_=[];for(const S of M)if(this.isCallableTool(S)){const k=S;_.push(await k.tool())}else _.push(S);_.length>0&&(e.config.tools=_);const G={model:A,config:e.config,callbacks:e.callbacks};return this.apiClient.isVertexAI()?E=ol(this.apiClient,G):E=nl(this.apiClient,G),delete E.config,b.send(JSON.stringify(E)),new Qd(b,this.apiClient)}isCallableTool(e){return"callTool"in e&&typeof e.callTool=="function"}}const Xd={turnComplete:!0};class Qd{constructor(e,n){this.conn=e,this.apiClient=n}tLiveClientContent(e,n){if(n.turns!==null&&n.turns!==void 0){let o=[];try{o=H(n.turns),e.isVertexAI()?o=o.map(s=>xe(s)):o=o.map(s=>We(s))}catch{throw new Error(`Failed to parse client content "turns", type: '${typeof n.turns}'`)}return{clientContent:{turns:o,turnComplete:n.turnComplete}}}return{clientContent:{turnComplete:n.turnComplete}}}tLiveClienttToolResponse(e,n){let o=[];if(n.functionResponses==null)throw new Error("functionResponses is required.");if(Array.isArray(n.functionResponses)?o=n.functionResponses:o=[n.functionResponses],o.length===0)throw new Error("functionResponses is required.");for(const a of o){if(typeof a!="object"||a===null||!("name"in a)||!("response"in a))throw new Error(`Could not parse function response, type '${typeof a}'.`);if(!e.isVertexAI()&&!("id"in a))throw new Error(Yd)}return{toolResponse:{functionResponses:o}}}sendClientContent(e){e=Object.assign(Object.assign({},Xd),e);const n=this.tLiveClientContent(this.apiClient,e);this.conn.send(JSON.stringify(n))}sendRealtimeInput(e){let n={};this.apiClient.isVertexAI()?n={realtimeInput:cl(e)}:n={realtimeInput:ll(e)},this.conn.send(JSON.stringify(n))}sendToolResponse(e){if(e.functionResponses==null)throw new Error("Tool response parameters are required.");const n=this.tLiveClienttToolResponse(this.apiClient,e);this.conn.send(JSON.stringify(n))}close(){this.conn.close()}}function Zd(t){const e={};return t.forEach((n,o)=>{e[o]=n}),e}function jd(t){const e=new Headers;for(const[n,o]of Object.entries(t))e.append(n,o);return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Qn=10;function Zn(t){var e,n,o;if(!((e=t==null?void 0:t.automaticFunctionCalling)===null||e===void 0)&&e.disable)return!0;let s=!1;for(const l of(n=t==null?void 0:t.tools)!==null&&n!==void 0?n:[])if($e(l)){s=!0;break}if(!s)return!0;const a=(o=t==null?void 0:t.automaticFunctionCalling)===null||o===void 0?void 0:o.maximumRemoteCalls;return a&&(a<0||!Number.isInteger(a))||a==0?(console.warn("Invalid maximumRemoteCalls value provided for automatic function calling. Disabled automatic function calling. Please provide a valid integer value greater than 0. maximumRemoteCalls provided:",a),!0):!1}function $e(t){return"callTool"in t&&typeof t.callTool=="function"}function jn(t){var e;return!(!((e=t==null?void 0:t.automaticFunctionCalling)===null||e===void 0)&&e.ignoreCallHistory)}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class ef extends le{constructor(e){super(),this.apiClient=e,this.generateContent=async n=>{var o,s,a,l,c;const d=await this.processParamsForMcpUsage(n);if(this.maybeMoveToResponseJsonSchem(n),!Vd(n)||Zn(n.config))return await this.generateContentInternal(d);if(Gd(n))throw new Error("Automatic function calling with CallableTools and Tools is not yet supported.");let u,f;const p=H(d.contents),h=(a=(s=(o=d.config)===null||o===void 0?void 0:o.automaticFunctionCalling)===null||s===void 0?void 0:s.maximumRemoteCalls)!==null&&a!==void 0?a:Qn;let m=0;for(;m<h&&(u=await this.generateContentInternal(d),!(!u.functionCalls||u.functionCalls.length===0));){const g=u.candidates[0].content,y=[];for(const v of(c=(l=n.config)===null||l===void 0?void 0:l.tools)!==null&&c!==void 0?c:[])if($e(v)){const w=await v.callTool(u.functionCalls);y.push(...w)}m++,f={role:"user",parts:y},d.contents=H(d.contents),d.contents.push(g),d.contents.push(f),jn(d.config)&&(p.push(g),p.push(f))}return jn(d.config)&&(u.automaticFunctionCallingHistory=p),u},this.generateContentStream=async n=>{if(this.maybeMoveToResponseJsonSchem(n),Zn(n.config)){const o=await this.processParamsForMcpUsage(n);return await this.generateContentStreamInternal(o)}else return await this.processAfcStream(n)},this.generateImages=async n=>await this.generateImagesInternal(n).then(o=>{var s;let a;const l=[];if(o!=null&&o.generatedImages)for(const d of o.generatedImages)d&&(d!=null&&d.safetyAttributes)&&((s=d==null?void 0:d.safetyAttributes)===null||s===void 0?void 0:s.contentType)==="Positive Prompt"?a=d==null?void 0:d.safetyAttributes:l.push(d);let c;return a?c={generatedImages:l,positivePromptSafetyAttributes:a}:c={generatedImages:l},c}),this.list=async n=>{var o;const l={config:Object.assign(Object.assign({},{queryBase:!0}),n==null?void 0:n.config)};if(this.apiClient.isVertexAI()&&!l.config.queryBase){if(!((o=l.config)===null||o===void 0)&&o.filter)throw new Error("Filtering tuned models list for Vertex AI is not currently supported");l.config.filter="labels.tune-type:*"}return new ke(se.PAGED_ITEM_MODELS,c=>this.listInternal(c),await this.listInternal(l),l)},this.editImage=async n=>{const o={model:n.model,prompt:n.prompt,referenceImages:[],config:n.config};return n.referenceImages&&n.referenceImages&&(o.referenceImages=n.referenceImages.map(s=>s.toReferenceImageAPI())),await this.editImageInternal(o)},this.upscaleImage=async n=>{let o={numberOfImages:1,mode:"upscale"};n.config&&(o=Object.assign(Object.assign({},o),n.config));const s={model:n.model,image:n.image,upscaleFactor:n.upscaleFactor,config:o};return await this.upscaleImageInternal(s)}}maybeMoveToResponseJsonSchem(e){e.config&&e.config.responseSchema&&(e.config.responseJsonSchema||Object.keys(e.config.responseSchema).includes("$schema")&&(e.config.responseJsonSchema=e.config.responseSchema,delete e.config.responseSchema))}async processParamsForMcpUsage(e){var n,o,s;const a=(n=e.config)===null||n===void 0?void 0:n.tools;if(!a)return e;const l=await Promise.all(a.map(async d=>$e(d)?await d.tool():d)),c={model:e.model,contents:e.contents,config:Object.assign(Object.assign({},e.config),{tools:l})};if(c.config.tools=l,e.config&&e.config.tools&&Ho(e.config.tools)){const d=(s=(o=e.config.httpOptions)===null||o===void 0?void 0:o.headers)!==null&&s!==void 0?s:{};let u=Object.assign({},d);Object.keys(u).length===0&&(u=this.apiClient.getDefaultHeaders()),Jo(u),c.config.httpOptions=Object.assign(Object.assign({},e.config.httpOptions),{headers:u})}return c}async initAfcToolsMap(e){var n,o,s;const a=new Map;for(const l of(o=(n=e.config)===null||n===void 0?void 0:n.tools)!==null&&o!==void 0?o:[])if($e(l)){const c=l,d=await c.tool();for(const u of(s=d.functionDeclarations)!==null&&s!==void 0?s:[]){if(!u.name)throw new Error("Function declaration name is required.");if(a.has(u.name))throw new Error(`Duplicate tool declaration name: ${u.name}`);a.set(u.name,c)}}return a}async processAfcStream(e){var n,o,s;const a=(s=(o=(n=e.config)===null||n===void 0?void 0:n.automaticFunctionCalling)===null||o===void 0?void 0:o.maximumRemoteCalls)!==null&&s!==void 0?s:Qn;let l=!1,c=0;const d=await this.initAfcToolsMap(e);return function(u,f,p){var h,m;return fe(this,arguments,function*(){for(var g,y,v,C;c<a;){l&&(c++,l=!1);const E=yield D(u.processParamsForMcpUsage(p)),M=yield D(u.generateContentStreamInternal(E)),_=[],G=[];try{for(var w=!0,b=(y=void 0,Ee(M)),A;A=yield D(b.next()),g=A.done,!g;w=!0){C=A.value,w=!1;const S=C;if(yield yield D(S),S.candidates&&(!((h=S.candidates[0])===null||h===void 0)&&h.content)){G.push(S.candidates[0].content);for(const k of(m=S.candidates[0].content.parts)!==null&&m!==void 0?m:[])if(c<a&&k.functionCall){if(!k.functionCall.name)throw new Error("Function call name was not returned by the model.");if(f.has(k.functionCall.name)){const P=yield D(f.get(k.functionCall.name).callTool([k.functionCall]));_.push(...P)}else throw new Error(`Automatic function calling was requested, but not all the tools the model used implement the CallableTool interface. Available tools: ${f.keys()}, mising tool: ${k.functionCall.name}`)}}}}catch(S){y={error:S}}finally{try{!w&&!g&&(v=b.return)&&(yield D(v.call(b)))}finally{if(y)throw y.error}}if(_.length>0){l=!0;const S=new Te;S.candidates=[{content:{role:"user",parts:_}}],yield yield D(S);const k=[];k.push(...G),k.push({role:"user",parts:_});const P=H(p.contents).concat(k);p.contents=P}else break}})}(this,d,e)}async generateContentInternal(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Wn(this.apiClient,e);return c=x("{model}:generateContent",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>{const p=Kn(f),h=new Te;return Object.assign(h,p),h})}else{const u=Jn(this.apiClient,e);return c=x("{model}:generateContent",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>{const p=Yn(f),h=new Te;return Object.assign(h,p),h})}}async generateContentStreamInternal(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Wn(this.apiClient,e);return c=x("{model}:streamGenerateContent?alt=sse",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.requestStream({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}),l.then(function(p){return fe(this,arguments,function*(){var h,m,g,y;try{for(var v=!0,C=Ee(p),w;w=yield D(C.next()),h=w.done,!h;v=!0){y=w.value,v=!1;const A=Kn(yield D(y.json())),E=new Te;Object.assign(E,A),yield yield D(E)}}catch(b){m={error:b}}finally{try{!v&&!h&&(g=C.return)&&(yield D(g.call(C)))}finally{if(m)throw m.error}}})})}else{const u=Jn(this.apiClient,e);return c=x("{model}:streamGenerateContent?alt=sse",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.requestStream({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}),l.then(function(p){return fe(this,arguments,function*(){var h,m,g,y;try{for(var v=!0,C=Ee(p),w;w=yield D(C.next()),h=w.done,!h;v=!0){y=w.value,v=!1;const A=Yn(yield D(y.json())),E=new Te;Object.assign(E,A),yield yield D(E)}}catch(b){m={error:b}}finally{try{!v&&!h&&(g=C.return)&&(yield D(g.call(C)))}finally{if(m)throw m.error}}})})}}async embedContent(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=lu(this.apiClient,e);return c=x("{model}:predict",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>{const p=pd(f),h=new An;return Object.assign(h,p),h})}else{const u=Tc(this.apiClient,e);return c=x("{model}:batchEmbedContents",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>{const p=zu(f),h=new An;return Object.assign(h,p),h})}}async generateImagesInternal(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=uu(this.apiClient,e);return c=x("{model}:predict",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>{const p=md(f),h=new Sn;return Object.assign(h,p),h})}else{const u=bc(this.apiClient,e);return c=x("{model}:predict",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>{const p=Wu(f),h=new Sn;return Object.assign(h,p),h})}}async editImageInternal(e){var n,o;let s,a="",l={};if(this.apiClient.isVertexAI()){const c=yu(this.apiClient,e);return a=x("{model}:predict",c._url),l=c._query,delete c.config,delete c._url,delete c._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),s.then(d=>{const u=gd(d),f=new Ti;return Object.assign(f,u),f})}else throw new Error("This method is only supported by the Vertex AI.")}async upscaleImageInternal(e){var n,o;let s,a="",l={};if(this.apiClient.isVertexAI()){const c=xu(this.apiClient,e);return a=x("{model}:predict",c._url),l=c._query,delete c.config,delete c._url,delete c._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),s.then(d=>{const u=yd(d),f=new wi;return Object.assign(f,u),f})}else throw new Error("This method is only supported by the Vertex AI.")}async get(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Cu(this.apiClient,e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>ut(f))}else{const u=Ec(this.apiClient,e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>ct(f))}}async listInternal(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=wu(this.apiClient,e);return c=x("{models_url}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>{const p=Td(f),h=new Mn;return Object.assign(h,p),h})}else{const u=Sc(this.apiClient,e);return c=x("{models_url}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>{const p=Ku(f),h=new Mn;return Object.assign(h,p),h})}}async update(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Eu(this.apiClient,e);return c=x("{model}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"PATCH",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>ut(f))}else{const u=_c(this.apiClient,e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"PATCH",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>ct(f))}}async delete(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Au(this.apiClient,e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(()=>{const f=wd(),p=new _n;return Object.assign(p,f),p})}else{const u=Pc(this.apiClient,e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"DELETE",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(()=>{const f=Ou(),p=new _n;return Object.assign(p,f),p})}}async countTokens(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Mu(this.apiClient,e);return c=x("{model}:countTokens",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>{const p=bd(f),h=new Pn;return Object.assign(h,p),h})}else{const u=kc(this.apiClient,e);return c=x("{model}:countTokens",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>{const p=Xu(f),h=new Pn;return Object.assign(h,p),h})}}async computeTokens(e){var n,o;let s,a="",l={};if(this.apiClient.isVertexAI()){const c=_u(this.apiClient,e);return a=x("{model}:computeTokens",c._url),l=c._query,delete c.config,delete c._url,delete c._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),s.then(d=>{const u=Ed(d),f=new bi;return Object.assign(f,u),f})}else throw new Error("This method is only supported by the Vertex AI.")}async generateVideos(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=ku(this.apiClient,e);return c=x("{model}:predictLongRunning",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>_d(f))}else{const u=Nc(this.apiClient,e);return c=x("{model}:predictLongRunning",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>ed(f))}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function tf(t){const e={},n=i(t,["operationName"]);n!=null&&r(e,["_url","operationName"],n);const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function nf(t){const e={},n=i(t,["operationName"]);n!=null&&r(e,["_url","operationName"],n);const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function of(t){const e={},n=i(t,["operationName"]);n!=null&&r(e,["operationName"],n);const o=i(t,["resourceName"]);o!=null&&r(e,["_url","resourceName"],o);const s=i(t,["config"]);return s!=null&&r(e,["config"],s),e}function rf(t){const e={},n=i(t,["video","uri"]);n!=null&&r(e,["uri"],n);const o=i(t,["video","encodedVideo"]);o!=null&&r(e,["videoBytes"],Q(o));const s=i(t,["encoding"]);return s!=null&&r(e,["mimeType"],s),e}function sf(t){const e={},n=i(t,["_self"]);return n!=null&&r(e,["video"],rf(n)),e}function af(t){const e={},n=i(t,["generatedSamples"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(l=>sf(l))),r(e,["generatedVideos"],a)}const o=i(t,["raiMediaFilteredCount"]);o!=null&&r(e,["raiMediaFilteredCount"],o);const s=i(t,["raiMediaFilteredReasons"]);return s!=null&&r(e,["raiMediaFilteredReasons"],s),e}function lf(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["metadata"]);o!=null&&r(e,["metadata"],o);const s=i(t,["done"]);s!=null&&r(e,["done"],s);const a=i(t,["error"]);a!=null&&r(e,["error"],a);const l=i(t,["response","generateVideoResponse"]);return l!=null&&r(e,["response"],af(l)),e}function cf(t){const e={},n=i(t,["gcsUri"]);n!=null&&r(e,["uri"],n);const o=i(t,["bytesBase64Encoded"]);o!=null&&r(e,["videoBytes"],Q(o));const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function uf(t){const e={},n=i(t,["_self"]);return n!=null&&r(e,["video"],cf(n)),e}function df(t){const e={},n=i(t,["videos"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(l=>uf(l))),r(e,["generatedVideos"],a)}const o=i(t,["raiMediaFilteredCount"]);o!=null&&r(e,["raiMediaFilteredCount"],o);const s=i(t,["raiMediaFilteredReasons"]);return s!=null&&r(e,["raiMediaFilteredReasons"],s),e}function eo(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["metadata"]);o!=null&&r(e,["metadata"],o);const s=i(t,["done"]);s!=null&&r(e,["done"],s);const a=i(t,["error"]);a!=null&&r(e,["error"],a);const l=i(t,["response"]);return l!=null&&r(e,["response"],df(l)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class ff extends le{constructor(e){super(),this.apiClient=e}async getVideosOperation(e){const n=e.operation,o=e.config;if(n.name===void 0||n.name==="")throw new Error("Operation name is required.");if(this.apiClient.isVertexAI()){const s=n.name.split("/operations/")[0];let a;return o&&"httpOptions"in o&&(a=o.httpOptions),this.fetchPredictVideosOperationInternal({operationName:n.name,resourceName:s,config:{httpOptions:a}})}else return this.getVideosOperationInternal({operationName:n.name,config:o})}async getVideosOperationInternal(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=nf(e);return c=x("{operationName}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>eo(f))}else{const u=tf(e);return c=x("{operationName}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>lf(f))}}async fetchPredictVideosOperationInternal(e){var n,o;let s,a="",l={};if(this.apiClient.isVertexAI()){const c=of(e);return a=x("{resourceName}:fetchPredictOperation",c._url),l=c._query,delete c.config,delete c._url,delete c._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),s.then(d=>eo(d))}else throw new Error("This method is only supported by the Vertex AI.")}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function pf(t){const e={},n=i(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function Wo(t){const e={},n=i(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],pf(n)),e}function hf(t){const e={},n=i(t,["speaker"]);n!=null&&r(e,["speaker"],n);const o=i(t,["voiceConfig"]);return o!=null&&r(e,["voiceConfig"],Wo(o)),e}function mf(t){const e={},n=i(t,["speakerVoiceConfigs"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>hf(s))),r(e,["speakerVoiceConfigs"],o)}return e}function gf(t){const e={},n=i(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],Wo(n));const o=i(t,["multiSpeakerVoiceConfig"]);o!=null&&r(e,["multiSpeakerVoiceConfig"],mf(o));const s=i(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function yf(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function vf(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function xf(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Cf(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],yf(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],vf(s));const a=i(t,["fileData"]);a!=null&&r(e,["fileData"],xf(a));const l=i(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const c=i(t,["codeExecutionResult"]);c!=null&&r(e,["codeExecutionResult"],c);const d=i(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const u=i(t,["functionCall"]);u!=null&&r(e,["functionCall"],u);const f=i(t,["functionResponse"]);f!=null&&r(e,["functionResponse"],f);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Tf(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Cf(a))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function wf(t){const e={},n=i(t,["behavior"]);n!=null&&r(e,["behavior"],n);const o=i(t,["description"]);o!=null&&r(e,["description"],o);const s=i(t,["name"]);s!=null&&r(e,["name"],s);const a=i(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=i(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const c=i(t,["response"]);c!=null&&r(e,["response"],c);const d=i(t,["responseJsonSchema"]);return d!=null&&r(e,["responseJsonSchema"],d),e}function bf(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function Ef(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],bf(n)),e}function Af(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function Sf(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Af(n)),e}function Mf(){return{}}function _f(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(u=>wf(u))),r(e,["functionDeclarations"],d)}if(i(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const o=i(t,["googleSearch"]);o!=null&&r(e,["googleSearch"],Ef(o));const s=i(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],Sf(s)),i(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(i(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");i(t,["urlContext"])!=null&&r(e,["urlContext"],Mf());const l=i(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const c=i(t,["computerUse"]);return c!=null&&r(e,["computerUse"],c),e}function Pf(t){const e={},n=i(t,["handle"]);if(n!=null&&r(e,["handle"],n),i(t,["transparent"])!==void 0)throw new Error("transparent parameter is not supported in Gemini API.");return e}function to(){return{}}function If(t){const e={},n=i(t,["disabled"]);n!=null&&r(e,["disabled"],n);const o=i(t,["startOfSpeechSensitivity"]);o!=null&&r(e,["startOfSpeechSensitivity"],o);const s=i(t,["endOfSpeechSensitivity"]);s!=null&&r(e,["endOfSpeechSensitivity"],s);const a=i(t,["prefixPaddingMs"]);a!=null&&r(e,["prefixPaddingMs"],a);const l=i(t,["silenceDurationMs"]);return l!=null&&r(e,["silenceDurationMs"],l),e}function kf(t){const e={},n=i(t,["automaticActivityDetection"]);n!=null&&r(e,["automaticActivityDetection"],If(n));const o=i(t,["activityHandling"]);o!=null&&r(e,["activityHandling"],o);const s=i(t,["turnCoverage"]);return s!=null&&r(e,["turnCoverage"],s),e}function Rf(t){const e={},n=i(t,["targetTokens"]);return n!=null&&r(e,["targetTokens"],n),e}function Df(t){const e={},n=i(t,["triggerTokens"]);n!=null&&r(e,["triggerTokens"],n);const o=i(t,["slidingWindow"]);return o!=null&&r(e,["slidingWindow"],Rf(o)),e}function Nf(t){const e={},n=i(t,["proactiveAudio"]);return n!=null&&r(e,["proactiveAudio"],n),e}function Ff(t,e){const n={},o=i(t,["generationConfig"]);e!==void 0&&o!=null&&r(e,["setup","generationConfig"],o);const s=i(t,["responseModalities"]);e!==void 0&&s!=null&&r(e,["setup","generationConfig","responseModalities"],s);const a=i(t,["temperature"]);e!==void 0&&a!=null&&r(e,["setup","generationConfig","temperature"],a);const l=i(t,["topP"]);e!==void 0&&l!=null&&r(e,["setup","generationConfig","topP"],l);const c=i(t,["topK"]);e!==void 0&&c!=null&&r(e,["setup","generationConfig","topK"],c);const d=i(t,["maxOutputTokens"]);e!==void 0&&d!=null&&r(e,["setup","generationConfig","maxOutputTokens"],d);const u=i(t,["mediaResolution"]);e!==void 0&&u!=null&&r(e,["setup","generationConfig","mediaResolution"],u);const f=i(t,["seed"]);e!==void 0&&f!=null&&r(e,["setup","generationConfig","seed"],f);const p=i(t,["speechConfig"]);e!==void 0&&p!=null&&r(e,["setup","generationConfig","speechConfig"],gf(wt(p)));const h=i(t,["enableAffectiveDialog"]);e!==void 0&&h!=null&&r(e,["setup","generationConfig","enableAffectiveDialog"],h);const m=i(t,["systemInstruction"]);e!==void 0&&m!=null&&r(e,["setup","systemInstruction"],Tf($(m)));const g=i(t,["tools"]);if(e!==void 0&&g!=null){let E=ye(g);Array.isArray(E)&&(E=E.map(M=>_f(ge(M)))),r(e,["setup","tools"],E)}const y=i(t,["sessionResumption"]);e!==void 0&&y!=null&&r(e,["setup","sessionResumption"],Pf(y));const v=i(t,["inputAudioTranscription"]);e!==void 0&&v!=null&&r(e,["setup","inputAudioTranscription"],to());const C=i(t,["outputAudioTranscription"]);e!==void 0&&C!=null&&r(e,["setup","outputAudioTranscription"],to());const w=i(t,["realtimeInputConfig"]);e!==void 0&&w!=null&&r(e,["setup","realtimeInputConfig"],kf(w));const b=i(t,["contextWindowCompression"]);e!==void 0&&b!=null&&r(e,["setup","contextWindowCompression"],Df(b));const A=i(t,["proactivity"]);return e!==void 0&&A!=null&&r(e,["setup","proactivity"],Nf(A)),n}function Lf(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["setup","model"],I(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],Ff(s,n)),n}function Uf(t,e,n){const o={},s=i(e,["expireTime"]);n!==void 0&&s!=null&&r(n,["expireTime"],s);const a=i(e,["newSessionExpireTime"]);n!==void 0&&a!=null&&r(n,["newSessionExpireTime"],a);const l=i(e,["uses"]);n!==void 0&&l!=null&&r(n,["uses"],l);const c=i(e,["liveConnectConstraints"]);n!==void 0&&c!=null&&r(n,["bidiGenerateContentSetup"],Lf(t,c));const d=i(e,["lockAdditionalFields"]);return n!==void 0&&d!=null&&r(n,["fieldMask"],d),o}function $f(t,e){const n={},o=i(e,["config"]);return o!=null&&r(n,["config"],Uf(t,o,n)),n}function Vf(t){const e={},n=i(t,["name"]);return n!=null&&r(e,["name"],n),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Gf(t){const e=[];for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)){const o=t[n];if(typeof o=="object"&&o!=null&&Object.keys(o).length>0){const s=Object.keys(o).map(a=>`${n}.${a}`);e.push(...s)}else e.push(n)}return e.join(",")}function Bf(t,e){let n=null;const o=t.bidiGenerateContentSetup;if(typeof o=="object"&&o!==null&&"setup"in o){const a=o.setup;typeof a=="object"&&a!==null?(t.bidiGenerateContentSetup=a,n=a):delete t.bidiGenerateContentSetup}else o!==void 0&&delete t.bidiGenerateContentSetup;const s=t.fieldMask;if(n){const a=Gf(n);if(Array.isArray(e==null?void 0:e.lockAdditionalFields)&&(e==null?void 0:e.lockAdditionalFields.length)===0)a?t.fieldMask=a:delete t.fieldMask;else if(e!=null&&e.lockAdditionalFields&&e.lockAdditionalFields.length>0&&s!==null&&Array.isArray(s)&&s.length>0){const l=["temperature","topK","topP","maxOutputTokens","responseModalities","seed","speechConfig"];let c=[];s.length>0&&(c=s.map(u=>l.includes(u)?`generationConfig.${u}`:u));const d=[];a&&d.push(a),c.length>0&&d.push(...c),d.length>0?t.fieldMask=d.join(","):delete t.fieldMask}else delete t.fieldMask}else s!==null&&Array.isArray(s)&&s.length>0?t.fieldMask=s.join(","):delete t.fieldMask;return t}class qf extends le{constructor(e){super(),this.apiClient=e}async create(e){var n,o;let s,a="",l={};if(this.apiClient.isVertexAI())throw new Error("The client.tokens.create method is only supported by the Gemini Developer API.");{const c=$f(this.apiClient,e);a=x("auth_tokens",c._url),l=c._query,delete c.config,delete c._url,delete c._query;const d=Bf(c,e.config);return s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(d),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(u=>u.json()),s.then(u=>Vf(u))}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function zf(t){const e={},n=i(t,["name"]);n!=null&&r(e,["_url","name"],n);const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function Hf(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);e!==void 0&&s!=null&&r(e,["_query","pageToken"],s);const a=i(t,["filter"]);return e!==void 0&&a!=null&&r(e,["_query","filter"],a),n}function Jf(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],Hf(n,e)),e}function Wf(t){const e={},n=i(t,["textInput"]);n!=null&&r(e,["textInput"],n);const o=i(t,["output"]);return o!=null&&r(e,["output"],o),e}function Yf(t){const e={};if(i(t,["gcsUri"])!==void 0)throw new Error("gcsUri parameter is not supported in Gemini API.");if(i(t,["vertexDatasetResource"])!==void 0)throw new Error("vertexDatasetResource parameter is not supported in Gemini API.");const n=i(t,["examples"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>Wf(s))),r(e,["examples","examples"],o)}return e}function Kf(t,e){const n={};if(i(t,["validationDataset"])!==void 0)throw new Error("validationDataset parameter is not supported in Gemini API.");const o=i(t,["tunedModelDisplayName"]);if(e!==void 0&&o!=null&&r(e,["displayName"],o),i(t,["description"])!==void 0)throw new Error("description parameter is not supported in Gemini API.");const s=i(t,["epochCount"]);e!==void 0&&s!=null&&r(e,["tuningTask","hyperparameters","epochCount"],s);const a=i(t,["learningRateMultiplier"]);if(a!=null&&r(n,["tuningTask","hyperparameters","learningRateMultiplier"],a),i(t,["exportLastCheckpointOnly"])!==void 0)throw new Error("exportLastCheckpointOnly parameter is not supported in Gemini API.");if(i(t,["adapterSize"])!==void 0)throw new Error("adapterSize parameter is not supported in Gemini API.");const l=i(t,["batchSize"]);e!==void 0&&l!=null&&r(e,["tuningTask","hyperparameters","batchSize"],l);const c=i(t,["learningRate"]);return e!==void 0&&c!=null&&r(e,["tuningTask","hyperparameters","learningRate"],c),n}function Of(t){const e={},n=i(t,["baseModel"]);n!=null&&r(e,["baseModel"],n);const o=i(t,["trainingDataset"]);o!=null&&r(e,["tuningTask","trainingData"],Yf(o));const s=i(t,["config"]);return s!=null&&r(e,["config"],Kf(s,e)),e}function Xf(t){const e={},n=i(t,["name"]);n!=null&&r(e,["_url","name"],n);const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function Qf(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);e!==void 0&&s!=null&&r(e,["_query","pageToken"],s);const a=i(t,["filter"]);return e!==void 0&&a!=null&&r(e,["_query","filter"],a),n}function Zf(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],Qf(n,e)),e}function jf(t,e){const n={},o=i(t,["gcsUri"]);e!==void 0&&o!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],o);const s=i(t,["vertexDatasetResource"]);if(e!==void 0&&s!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],s),i(t,["examples"])!==void 0)throw new Error("examples parameter is not supported in Vertex AI.");return n}function ep(t,e){const n={},o=i(t,["gcsUri"]);o!=null&&r(n,["validationDatasetUri"],o);const s=i(t,["vertexDatasetResource"]);return e!==void 0&&s!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],s),n}function tp(t,e){const n={},o=i(t,["validationDataset"]);e!==void 0&&o!=null&&r(e,["supervisedTuningSpec"],ep(o,n));const s=i(t,["tunedModelDisplayName"]);e!==void 0&&s!=null&&r(e,["tunedModelDisplayName"],s);const a=i(t,["description"]);e!==void 0&&a!=null&&r(e,["description"],a);const l=i(t,["epochCount"]);e!==void 0&&l!=null&&r(e,["supervisedTuningSpec","hyperParameters","epochCount"],l);const c=i(t,["learningRateMultiplier"]);e!==void 0&&c!=null&&r(e,["supervisedTuningSpec","hyperParameters","learningRateMultiplier"],c);const d=i(t,["exportLastCheckpointOnly"]);e!==void 0&&d!=null&&r(e,["supervisedTuningSpec","exportLastCheckpointOnly"],d);const u=i(t,["adapterSize"]);if(e!==void 0&&u!=null&&r(e,["supervisedTuningSpec","hyperParameters","adapterSize"],u),i(t,["batchSize"])!==void 0)throw new Error("batchSize parameter is not supported in Vertex AI.");if(i(t,["learningRate"])!==void 0)throw new Error("learningRate parameter is not supported in Vertex AI.");return n}function np(t){const e={},n=i(t,["baseModel"]);n!=null&&r(e,["baseModel"],n);const o=i(t,["trainingDataset"]);o!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],jf(o,e));const s=i(t,["config"]);return s!=null&&r(e,["config"],tp(s,e)),e}function op(t){const e={},n=i(t,["name"]);n!=null&&r(e,["model"],n);const o=i(t,["name"]);return o!=null&&r(e,["endpoint"],o),e}function Yo(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["state"]);o!=null&&r(e,["state"],Eo(o));const s=i(t,["createTime"]);s!=null&&r(e,["createTime"],s);const a=i(t,["tuningTask","startTime"]);a!=null&&r(e,["startTime"],a);const l=i(t,["tuningTask","completeTime"]);l!=null&&r(e,["endTime"],l);const c=i(t,["updateTime"]);c!=null&&r(e,["updateTime"],c);const d=i(t,["description"]);d!=null&&r(e,["description"],d);const u=i(t,["baseModel"]);u!=null&&r(e,["baseModel"],u);const f=i(t,["_self"]);f!=null&&r(e,["tunedModel"],op(f));const p=i(t,["distillationSpec"]);p!=null&&r(e,["distillationSpec"],p);const h=i(t,["experiment"]);h!=null&&r(e,["experiment"],h);const m=i(t,["labels"]);m!=null&&r(e,["labels"],m);const g=i(t,["pipelineJob"]);g!=null&&r(e,["pipelineJob"],g);const y=i(t,["satisfiesPzi"]);y!=null&&r(e,["satisfiesPzi"],y);const v=i(t,["satisfiesPzs"]);v!=null&&r(e,["satisfiesPzs"],v);const C=i(t,["serviceAccount"]);C!=null&&r(e,["serviceAccount"],C);const w=i(t,["tunedModelDisplayName"]);return w!=null&&r(e,["tunedModelDisplayName"],w),e}function ip(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["tunedModels"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(a=>Yo(a))),r(e,["tuningJobs"],s)}return e}function rp(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["metadata"]);o!=null&&r(e,["metadata"],o);const s=i(t,["done"]);s!=null&&r(e,["done"],s);const a=i(t,["error"]);return a!=null&&r(e,["error"],a),e}function sp(t){const e={},n=i(t,["checkpointId"]);n!=null&&r(e,["checkpointId"],n);const o=i(t,["epoch"]);o!=null&&r(e,["epoch"],o);const s=i(t,["step"]);s!=null&&r(e,["step"],s);const a=i(t,["endpoint"]);return a!=null&&r(e,["endpoint"],a),e}function ap(t){const e={},n=i(t,["model"]);n!=null&&r(e,["model"],n);const o=i(t,["endpoint"]);o!=null&&r(e,["endpoint"],o);const s=i(t,["checkpoints"]);if(s!=null){let a=s;Array.isArray(a)&&(a=a.map(l=>sp(l))),r(e,["checkpoints"],a)}return e}function ft(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["state"]);o!=null&&r(e,["state"],Eo(o));const s=i(t,["createTime"]);s!=null&&r(e,["createTime"],s);const a=i(t,["startTime"]);a!=null&&r(e,["startTime"],a);const l=i(t,["endTime"]);l!=null&&r(e,["endTime"],l);const c=i(t,["updateTime"]);c!=null&&r(e,["updateTime"],c);const d=i(t,["error"]);d!=null&&r(e,["error"],d);const u=i(t,["description"]);u!=null&&r(e,["description"],u);const f=i(t,["baseModel"]);f!=null&&r(e,["baseModel"],f);const p=i(t,["tunedModel"]);p!=null&&r(e,["tunedModel"],ap(p));const h=i(t,["supervisedTuningSpec"]);h!=null&&r(e,["supervisedTuningSpec"],h);const m=i(t,["tuningDataStats"]);m!=null&&r(e,["tuningDataStats"],m);const g=i(t,["encryptionSpec"]);g!=null&&r(e,["encryptionSpec"],g);const y=i(t,["partnerModelTuningSpec"]);y!=null&&r(e,["partnerModelTuningSpec"],y);const v=i(t,["distillationSpec"]);v!=null&&r(e,["distillationSpec"],v);const C=i(t,["experiment"]);C!=null&&r(e,["experiment"],C);const w=i(t,["labels"]);w!=null&&r(e,["labels"],w);const b=i(t,["pipelineJob"]);b!=null&&r(e,["pipelineJob"],b);const A=i(t,["satisfiesPzi"]);A!=null&&r(e,["satisfiesPzi"],A);const E=i(t,["satisfiesPzs"]);E!=null&&r(e,["satisfiesPzs"],E);const M=i(t,["serviceAccount"]);M!=null&&r(e,["serviceAccount"],M);const _=i(t,["tunedModelDisplayName"]);return _!=null&&r(e,["tunedModelDisplayName"],_),e}function lp(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["tuningJobs"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(a=>ft(a))),r(e,["tuningJobs"],s)}return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class cp extends le{constructor(e){super(),this.apiClient=e,this.get=async n=>await this.getInternal(n),this.list=async(n={})=>new ke(se.PAGED_ITEM_TUNING_JOBS,o=>this.listInternal(o),await this.listInternal(n),n),this.tune=async n=>{if(this.apiClient.isVertexAI())return await this.tuneInternal(n);{const o=await this.tuneMldevInternal(n);let s="";return o.metadata!==void 0&&o.metadata.tunedModel!==void 0?s=o.metadata.tunedModel:o.name!==void 0&&o.name.includes("/operations/")&&(s=o.name.split("/operations/")[0]),{name:s,state:nt.JOB_STATE_QUEUED}}}}async getInternal(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Xf(e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>ft(f))}else{const u=zf(e);return c=x("{name}",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>Yo(f))}}async listInternal(e){var n,o,s,a;let l,c="",d={};if(this.apiClient.isVertexAI()){const u=Zf(e);return c=x("tuningJobs",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),l.then(f=>{const p=lp(f),h=new In;return Object.assign(h,p),h})}else{const u=Jf(e);return c=x("tunedModels",u._url),d=u._query,delete u.config,delete u._url,delete u._query,l=this.apiClient.request({path:c,queryParams:d,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(f=>f.json()),l.then(f=>{const p=ip(f),h=new In;return Object.assign(h,p),h})}}async tuneInternal(e){var n,o;let s,a="",l={};if(this.apiClient.isVertexAI()){const c=np(e);return a=x("tuningJobs",c._url),l=c._query,delete c.config,delete c._url,delete c._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),s.then(d=>ft(d))}else throw new Error("This method is only supported by the Vertex AI.")}async tuneMldevInternal(e){var n,o;let s,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const c=Of(e);return a=x("tunedModels",c._url),l=c._query,delete c.config,delete c._url,delete c._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),s.then(d=>rp(d))}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class up{async download(e,n){throw new Error("Download to file is not supported in the browser, please use a browser compliant download like an <a> tag.")}}const dp=1024*1024*8,fp=3,pp=1e3,hp=2,tt="x-goog-upload-status";async function mp(t,e,n){var o,s,a;let l=0,c=0,d=new ot(new Response),u="upload";for(l=t.size;c<l;){const p=Math.min(dp,l-c),h=t.slice(c,c+p);c+p>=l&&(u+=", finalize");let m=0,g=pp;for(;m<fp&&(d=await n.request({path:"",body:h,httpMethod:"POST",httpOptions:{apiVersion:"",baseUrl:e,headers:{"X-Goog-Upload-Command":u,"X-Goog-Upload-Offset":String(c),"Content-Length":String(p)}}}),!(!((o=d==null?void 0:d.headers)===null||o===void 0)&&o[tt]));)m++,await yp(g),g=g*hp;if(c+=p,((s=d==null?void 0:d.headers)===null||s===void 0?void 0:s[tt])!=="active")break;if(l<=c)throw new Error("All content has been uploaded, but the upload status is not finalized.")}const f=await(d==null?void 0:d.json());if(((a=d==null?void 0:d.headers)===null||a===void 0?void 0:a[tt])!=="final")throw new Error("Failed to upload file: Upload status is not finalized.");return f.file}async function gp(t){return{size:t.size,type:t.type}}function yp(t){return new Promise(e=>setTimeout(e,t))}class vp{async upload(e,n,o){if(typeof e=="string")throw new Error("File path is not supported in browser uploader.");return await mp(e,n,o)}async stat(e){if(typeof e=="string")throw new Error("File path is not supported in browser uploader.");return await gp(e)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class xp{create(e,n,o){return new Cp(e,n,o)}}class Cp{constructor(e,n,o){this.url=e,this.headers=n,this.callbacks=o}connect(){this.ws=new WebSocket(this.url),this.ws.onopen=this.callbacks.onopen,this.ws.onerror=this.callbacks.onerror,this.ws.onclose=this.callbacks.onclose,this.ws.onmessage=this.callbacks.onmessage}send(e){if(this.ws===void 0)throw new Error("WebSocket is not connected");this.ws.send(e)}close(){if(this.ws===void 0)throw new Error("WebSocket is not connected");this.ws.close()}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const no="x-goog-api-key";class Tp{constructor(e){this.apiKey=e}async addAuthHeaders(e){if(e.get(no)===null){if(this.apiKey.startsWith("auth_tokens/"))throw new Error("Ephemeral tokens are only supported by the live API.");if(!this.apiKey)throw new Error("API key is missing. Please provide a valid API key.");e.append(no,this.apiKey)}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const wp="gl-node/";class bp{constructor(e){var n;if(e.apiKey==null)throw new Error("An API Key must be set when running in a browser");if(e.project||e.location)throw new Error("Vertex AI project based authentication is not supported on browser runtimes. Please do not provide a project or location.");this.vertexai=(n=e.vertexai)!==null&&n!==void 0?n:!1,this.apiKey=e.apiKey;const o=Ci(e,void 0,void 0);o&&(e.httpOptions?e.httpOptions.baseUrl=o:e.httpOptions={baseUrl:o}),this.apiVersion=e.apiVersion;const s=new Tp(this.apiKey);this.apiClient=new Ld({auth:s,apiVersion:this.apiVersion,apiKey:this.apiKey,vertexai:this.vertexai,httpOptions:e.httpOptions,userAgentExtra:wp+"web",uploader:new vp,downloader:new up}),this.models=new ef(this.apiClient),this.live=new Od(this.apiClient,s,new xp),this.batches=new zr(this.apiClient),this.chats=new Ys(this.models,this.apiClient),this.caches=new Hs(this.apiClient),this.files=new sa(this.apiClient),this.operations=new ff(this.apiClient),this.authTokens=new qf(this.apiClient),this.tunings=new cp(this.apiClient)}}/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */const Ko="important",Ep=" !"+Ko,_e=go(class extends yo{constructor(t){var e;if(super(t),t.type!==mo.ATTRIBUTE||t.name!=="style"||((e=t.strings)==null?void 0:e.length)>2)throw Error("The `styleMap` directive must be used in the `style` attribute and must be the only part in the attribute.")}render(t){return Object.keys(t).reduce((e,n)=>{const o=t[n];return o==null?e:e+`${n=n.includes("-")?n:n.replace(/(?:^(webkit|moz|ms|o)|)(?=[A-Z])/g,"-$&").toLowerCase()}:${o};`},"")}update(t,[e]){const{style:n}=t.element;if(this.ft===void 0)return this.ft=new Set(Object.keys(e)),this.render(e);for(const o of this.ft)e[o]==null&&(this.ft.delete(o),o.includes("-")?n.removeProperty(o):n[o]=null);for(const o in e){const s=e[o];if(s!=null){this.ft.add(o);const a=typeof s=="string"&&s.endsWith(Ep);o.includes("-")||a?n.setProperty(o,a?s.slice(0,-11):s,a?Ko:""):n[o]=s}}return ee}});var Ap=Object.defineProperty,Sp=Object.getOwnPropertyDescriptor,B=(t,e,n,o)=>{for(var s=o>1?void 0:o?Sp(e,n):e,a=t.length-1,l;a>=0;a--)(l=t[a])&&(s=(o?l(e,n,s):l(s))||s);return o&&s&&Ap(e,n,s),s};let V=class extends z{constructor(){super(...arguments),this.promptId="",this.text="",this.weight=0,this.color="",this.category="",this.filtered=!1,this.cc=0,this.channel=0,this.learnMode=!1,this.showCC=!1,this.midiDispatcher=null,this.audioLevel=0}connectedCallback(){var t;super.connectedCallback(),(t=this.midiDispatcher)==null||t.addEventListener("cc-message",e=>{const n=e,{channel:o,cc:s,value:a}=n.detail;this.learnMode?(this.cc=s,this.channel=o,this.learnMode=!1,this.dispatchPromptChange()):s===this.cc&&(this.weight=a/127*2,this.dispatchPromptChange())})}update(t){t.has("showCC")&&!this.showCC&&(this.learnMode=!1),super.update(t)}dispatchPromptChange(){this.dispatchEvent(new CustomEvent("prompt-changed",{detail:{promptId:this.promptId,text:this.text,weight:this.weight,cc:this.cc,color:this.color,category:this.category}}))}updateWeight(){this.weight=this.weightInput.valueAsNumber,this.dispatchPromptChange()}toggleLearnMode(){this.learnMode=!this.learnMode}removeThisPrompt(){this.dispatchEvent(new CustomEvent("prompt-removed",{bubbles:!0,composed:!0,detail:{promptId:this.promptId}}))}render(){const t=Y({"learn-mode":this.learnMode,"show-cc":this.showCC}),e=this.weight/2*100,n=this.filtered?0:Math.min(this.audioLevel*15,12),o=this.filtered?0:Math.min(this.audioLevel*8,6),s={"--color":this.filtered?"#666":this.color,"--fill-percent":`${e}%`},a={boxShadow:this.weight>0?`0 0 ${n}px ${o}px ${this.filtered?"#666":this.color}`:"none"};return T`
      <div id="root" class=${t} style=${_e(s)}>
        <div class="info">
          <div id="text-container">
            <div class="color-dot" style="background-color: ${this.filtered?"#666":this.color}"></div>
            <div id="text" title=${this.text}>
              ${this.text}
            </div>
          </div>
          <div id="midi" @click=${this.toggleLearnMode}>
            ${this.learnMode?"Learn...":`CC:${this.cc}`}
          </div>
        </div>
        <div class="slider-area">
           <div class="slider-container">
              <div class="slider-track"></div>
              <div class="slider-fill" style=${_e(a)}></div>
              <input
                id="weight-slider"
                type="range"
                min="0"
                max="2"
                step="0.01"
                .value=${this.weight}
                @input=${this.updateWeight}>
            </div>
          <div class="weight-readout">Weight: ${this.weight.toFixed(2)}</div>
        </div>
        <button class="remove-btn" @click=${this.removeThisPrompt} title="Remove track">✕</button>
      </div>`}};V.styles=ae`
    :host {
      display: block; /* The host is a block, the root div will be the flex container */
      background: #1C1C1C;
      border-radius: 8px;
      padding: 12px 15px;
      height: 70px;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.2s ease-in-out;
      flex-shrink: 0;
    }
    #root {
      display: flex;
      align-items: center;
      gap: 15px;
      width: 100%;
      height: 100%;
    }
    :host([filtered]) {
      opacity: 0.6;
    }
    .info {
      display: flex;
      flex-direction: column;
      gap: 4px;
      width: 160px;
      flex-shrink: 0;
      user-select: none;
      justify-content: center;
    }
    #text-container {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .color-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      flex-shrink: 0;
    }
    #text {
      color: #f0f0f0;
      font-size: 16px; /* Made slightly larger to be more prominent */
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      text-align: left;
    }
    #midi {
      font-family: monospace;
      text-align: left;
      font-size: 12px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 4px;
      padding: 2px 5px;
      color: rgba(255, 255, 255, 0.5);
      background: rgba(0, 0, 0, 0.2);
      cursor: pointer;
      user-select: none;
      width: fit-content;
      visibility: hidden;
      transition: all 0.2s ease;
    }
    .learn-mode #midi {
      color: #0A0A0A;
      background: #F9B200;
      border-color: #F9B200;
    }
    .show-cc #midi {
      visibility: visible;
    }

    .slider-area {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      gap: 5px;
      height: 100%;
      justify-content: center;
    }
    .weight-readout {
      color: #aaa;
      font-size: 11px;
      font-family: monospace;
      user-select: none;
    }
    .slider-container {
      position: relative;
      width: 100%;
      height: 20px;
      display: flex;
      align-items: center;
    }
    .slider-track, .slider-fill {
      position: absolute;
      height: 6px;
      width: 100%;
      border-radius: 3px;
      top: 50%;
      transform: translateY(-50%);
    }
    .slider-track {
      background-color: #0A0A0A;
    }
    .slider-fill {
      background-color: var(--color);
      width: var(--fill-percent);
      transition: box-shadow 0.1s;
    }

    #weight-slider {
      -webkit-appearance: none;
      appearance: none;
      width: 100%;
      background: transparent;
      cursor: pointer;
      position: relative;
      z-index: 2;
      margin: 0;
    }
    #weight-slider:focus {
      outline: none;
    }

    /* Thumb */
    #weight-slider::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: #f0f0f0;
      border: none;
    }
    #weight-slider::-moz-range-thumb {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: #f0f0f0;
      border: none;
    }

    .remove-btn {
      background: transparent;
      color: #888;
      border: none;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      cursor: pointer;
      font-size: 16px;
      line-height: 24px;
      text-align: center;
      padding: 0;
      margin-left: 10px;
      flex-shrink: 0; /* Prevent button from shrinking */
      transition: all 0.2s;
    }
    .remove-btn:hover {
      color: white;
      background: #444;
    }

    /* Responsive adjustments */
    @media (max-width: 480px) {
      :host {
        height: auto;
        padding: 12px;
      }
      #root {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }
      .info {
        width: 100%;
      }
      .slider-area {
        width: 100%;
      }
      .remove-btn {
        position: absolute;
        top: 6px;
        right: 6px;
        margin-left: 0;
        background: rgba(0,0,0,0.2);
      }
    }
  `;B([L({type:String})],V.prototype,"promptId",2);B([L({type:String})],V.prototype,"text",2);B([L({type:Number})],V.prototype,"weight",2);B([L({type:String})],V.prototype,"color",2);B([L({type:String})],V.prototype,"category",2);B([L({type:Boolean,reflect:!0})],V.prototype,"filtered",2);B([L({type:Number})],V.prototype,"cc",2);B([L({type:Number})],V.prototype,"channel",2);B([L({type:Boolean})],V.prototype,"learnMode",2);B([L({type:Boolean})],V.prototype,"showCC",2);B([vt("#weight-slider")],V.prototype,"weightInput",2);B([L({type:Object})],V.prototype,"midiDispatcher",2);B([L({type:Number})],V.prototype,"audioLevel",2);V=B([te("prompt-controller")],V);var Mp=Object.defineProperty,_p=Object.getOwnPropertyDescriptor,Oo=(t,e,n,o)=>{for(var s=o>1?void 0:o?_p(e,n):e,a=t.length-1,l;a>=0;a--)(l=t[a])&&(s=(o?l(e,n,s):l(s))||s);return o&&s&&Mp(e,n,s),s};let ze=class extends z{constructor(){super(...arguments),this.playbackState="stopped"}renderSvg(){return T` <svg
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <circle class="bg" cx="30" cy="30" r="30" fill="rgba(255, 255, 255, 0.05)"/>
      <circle class="bg" cx="30" cy="30" r="29" stroke="rgba(255, 255, 255, 0.2)" stroke-width="1"/>
      ${this.renderIcon()}
    </svg>`}renderPause(){return Ze`
      <g class="icon">
        <rect x="22" y="19" width="6" height="22" rx="2" fill="#FEFEFE" />
        <rect x="32" y="19" width="6" height="22" rx="2" fill="#FEFEFE" />
      </g>
    `}renderPlay(){return Ze`<path class="icon" d="M25 20.5359C25 19.4223 26.2415 18.7846 27.2185 19.4434L42.9231 28.9075C43.8328 29.5218 43.8328 30.8268 42.9231 31.4411L27.2185 40.9052C26.2415 41.564 25 40.9263 25 39.8127V20.5359Z" fill="#FEFEFE"/>`}renderLoading(){return Ze`<path class="loader-path" d="M 30,5 A 25,25 0 0 1 55,30" />`}renderIcon(){return this.playbackState==="loading"?this.renderLoading():this.playbackState==="playing"?this.renderPause():this.renderPlay()}render(){return T`${this.renderSvg()}`}};ze.styles=ae`
    :host {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      width: 100%;
      height: 100%;
    }
    :host(:hover) svg .bg {
      transform: scale(1.05);
    }
    svg {
      width: 100%;
      height: 100%;
    }
    .bg {
      transition: transform 0.2s ease-out;
      transform-origin: center;
    }
    .icon {
      transition: opacity 0.2s ease;
    }
    .loader-path {
      stroke: #F9B200;
      stroke-width: 4;
      stroke-linecap: round;
      fill: none;
      transform-origin: center;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `;Oo([L({type:String})],ze.prototype,"playbackState",2);ze=Oo([te("play-pause-button")],ze);var Pp=Object.defineProperty,Ip=Object.getOwnPropertyDescriptor,St=(t,e,n,o)=>{for(var s=o>1?void 0:o?Ip(e,n):e,a=t.length-1,l;a>=0;a--)(l=t[a])&&(s=(o?l(e,n,s):l(s))||s);return o&&s&&Pp(e,n,s),s};let Pe=class extends z{constructor(){super(),this.x=1,this.y=0,this.boundRect=null,this.handlePointerMove=this.handlePointerMove.bind(this),this.handlePointerUp=this.handlePointerUp.bind(this)}handlePointerDown(t){var o;if((o=this.shadowRoot)==null||o.host.setPointerCapture(t.pointerId),this.boundRect=this.getBoundingClientRect(),!this.boundRect)return;const e=Math.max(0,Math.min(1,(t.clientX-this.boundRect.left)/this.boundRect.width)),n=Math.max(0,Math.min(1,1-(t.clientY-this.boundRect.top)/this.boundRect.height));this.updatePosition(e,n),window.addEventListener("pointermove",this.handlePointerMove),window.addEventListener("pointerup",this.handlePointerUp),window.addEventListener("pointercancel",this.handlePointerUp)}handlePointerMove(t){if(!this.boundRect)return;const e=Math.max(0,Math.min(1,(t.clientX-this.boundRect.left)/this.boundRect.width)),n=Math.max(0,Math.min(1,1-(t.clientY-this.boundRect.top)/this.boundRect.height));this.updatePosition(e,n)}handlePointerUp(t){var e;(e=this.shadowRoot)==null||e.host.releasePointerCapture(t.pointerId),window.removeEventListener("pointermove",this.handlePointerMove),window.removeEventListener("pointerup",this.handlePointerUp),window.removeEventListener("pointercancel",this.handlePointerUp)}updatePosition(t,e,n=!0){this.x=t,this.y=e,n&&this.dispatchEvent(new CustomEvent("fx-changed",{detail:{x:this.x,y:this.y},bubbles:!0,composed:!0}))}resetPad(){this.updatePosition(1,0)}render(){const t=_e({left:`${this.x*100}%`,top:`${(1-this.y)*100}%`});return T`
      <div id="pad-area" 
           @pointerdown=${this.handlePointerDown}
           @dblclick=${this.resetPad}
           title="X: Filter | Y: Delay | Double-click to reset"
      >
        <div id="grid"></div>
        <div id="puck" style=${t}></div>
        <div id="label-x" class="labels">FILTER</div>
        <div id="label-y" class="labels">DELAY</div>
      </div>
    `}};Pe.styles=ae`
    :host {
      display: block;
      width: 100%;
      height: 100%;
      touch-action: none;
      position: relative;
      cursor: crosshair;
      background: #0a0a0a;
      border-radius: 6px;
      box-shadow: inset 0 0 10px rgba(0,0,0,0.5);
    }
    #pad-area {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
      border-radius: 6px;
    }
    #grid {
      position: absolute;
      width: 100%;
      height: 100%;
      background-image:
        linear-gradient(rgba(255,255,255,0.08) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255,255,255,0.08) 1px, transparent 1px);
      background-size: 25% 25%;
    }
    #puck {
      position: absolute;
      width: 24px;
      height: 24px;
      background: #F9B200;
      border-radius: 50%;
      border: 2px solid white;
      transform: translate(-50%, -50%);
      pointer-events: none;
      box-shadow: 0 0 15px #F9B200, 0 0 25px #f1c40f;
      will-change: top, left;
      transition: box-shadow 0.2s ease;
    }
    #pad-area:active #puck {
       box-shadow: 0 0 20px #F9B200, 0 0 35px #f1c40f;
    }
    .labels {
        font-family: monospace;
        font-size: 11px;
        color: rgba(255, 255, 255, 0.4);
        position: absolute;
        user-select: none;
        pointer-events: none;
        text-shadow: 0 0 2px black;
    }
    #label-x {
        bottom: 5px;
        left: 50%;
        transform: translateX(-50%);
    }
    #label-y {
        top: calc(50% - 5px);
        left: 12px;
        transform: translateY(-50%) rotate(-90deg);
        transform-origin: center;
    }
  `;St([N()],Pe.prototype,"x",2);St([N()],Pe.prototype,"y",2);Pe=St([te("fx-pad")],Pe);/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/class kp extends EventTarget{constructor(){super(...arguments),this.access=null,this.activeMidiInputId=null}async getMidiAccess(){if(this.access)return[...this.access.inputs.keys()];if(!navigator.requestMIDIAccess)throw new Error("Your browser does not support the Web MIDI API. For a list of compatible browsers, see https://caniuse.com/midi");if(this.access=await navigator.requestMIDIAccess({sysex:!1}).catch(n=>n),this.access===null)throw new Error("Unable to acquire MIDI access.");const e=[...this.access.inputs.keys()];e.length>0&&this.activeMidiInputId===null&&(this.activeMidiInputId=e[0]);for(const n of this.access.inputs.values())n.onmidimessage=o=>{if(n.id!==this.activeMidiInputId)return;const{data:s}=o;if(!s){console.error("MIDI message has no data");return}const a=s[0],l=a&15;if(!((a&240)===176))return;const u={cc:s[1],value:s[2],channel:l};this.dispatchEvent(new CustomEvent("cc-message",{detail:u}))};return e}getDeviceName(e){if(!this.access)return null;const n=this.access.inputs.get(e);return n?n.name:null}}var Rp=Object.defineProperty,Dp=Object.getOwnPropertyDescriptor,F=(t,e,n,o)=>{for(var s=o>1?void 0:o?Dp(e,n):e,a=t.length-1,l;a>=0;a--)(l=t[a])&&(s=(o?l(e,n,s):l(s))||s);return o&&s&&Rp(e,n,s),s};const Np=T`<svg xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="currentColor"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M11 18h2v-2h-2v2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z"/></svg>`;let R=class extends z{constructor(t,e,n,o){super(),this.textToIdMap=new Map,this.activePromptIds=new Set,this.showMidi=!1,this.playbackState="stopped",this.audioLevel=0,this.midiInputIds=[],this.activeMidiInputId=null,this.complexity=.5,this.masterVolume=.8,this.searchQuery="",this.isRecording=!1,this.downloadUrl=null,this.downloadFilename="prompt-dj-beat.webm",this.previewingPromptId=null,this.wasPlayingBeforePreview=!1,this.isAiToolWindowMinimized=!0,this.showBeatMakerHelp=!1,this.audioFxState={pitch:1,pan:0,reverb:.2,delayTime:.5,delayFeedback:.4,isMono:!1,is8D:!1,isWindowMinimized:!0},this.activeFxTab="controls",this.beatMakerPromptText="",this.isGeneratingBeat=!1,this.isSidebarOpen=!1,this.filteredPrompts=new Set,this.allPrompts=t,this.midiDispatcher=new kp,this.ai=e,this.availablePromptsForAI=n,this.liveMusicHelper=o;for(const s of this.allPrompts.values())this.textToIdMap.set(s.text,s.promptId)}getActivePromptColors(){return[...this.allPrompts.values()].filter(e=>this.activePromptIds.has(e.promptId)&&e.weight>0).map(e=>e.color)}updated(t){window.innerWidth<=768&&this.isSidebarOpen&&(t.has("playbackState")&&this.playbackState==="playing"||t.has("isGeneratingBeat")&&this.isGeneratingBeat)&&(this.isSidebarOpen=!1)}dispatchPromptsChanged(){const t=new Map;if(this.previewingPromptId){const e=this.allPrompts.get(this.previewingPromptId);if(e){const n={...e,weight:1};t.set(this.previewingPromptId,n)}}else{for(const e of this.activePromptIds){const n=this.allPrompts.get(e);n&&n.weight>0&&t.set(e,n)}if(this.complexity>.05){const e={promptId:"internal-complexity-prompt",text:"intricate and layered instrumentation, complex harmonies, detailed patterns",weight:this.complexity*.75,cc:-1,color:"transparent",category:"Complexity"};t.set(e.promptId,e)}}this.dispatchEvent(new CustomEvent("prompts-changed",{detail:t}))}handleComplexityChange(t){this.complexity=t.target.valueAsNumber,this.dispatchPromptsChanged()}handleMasterVolumeChange(t){this.masterVolume=t.target.valueAsNumber,this.liveMusicHelper.setMasterVolume(this.masterVolume)}handlePromptChanged(t){const{promptId:e,text:n,weight:o,cc:s,color:a,category:l}=t.detail,c=this.allPrompts.get(e);c&&(c.text=n,c.weight=o,c.cc=s,c.color=a,c.category=l,this.allPrompts.set(e,c),this.dispatchPromptsChanged(),this.requestUpdate("allPrompts"))}addPrompt(t){if(this.previewingPromptId)return;const e=this.allPrompts.get(t);e&&(e.weight=.5,this.allPrompts.set(t,e)),this.activePromptIds.add(t),this.dispatchPromptsChanged(),this.requestUpdate("activePromptIds"),window.innerWidth<=768&&(this.isSidebarOpen=!1)}handlePromptRemoved(t){const{promptId:e}=t.detail,n=this.allPrompts.get(e);n&&(n.weight=0,this.allPrompts.set(e,n)),this.activePromptIds.delete(e),this.dispatchPromptsChanged(),this.requestUpdate("activePromptIds")}toggleShowMidi(){return this.setShowMidi(!this.showMidi)}async setShowMidi(t){if(this.showMidi=t,!!this.showMidi)try{const e=await this.midiDispatcher.getMidiAccess();this.midiInputIds=e,this.activeMidiInputId=this.midiDispatcher.activeMidiInputId}catch(e){this.dispatchEvent(new CustomEvent("error",{detail:e.message}))}}handleMidiInputChange(t){const n=t.target.value;this.activeMidiInputId=n,this.midiDispatcher.activeMidiInputId=n}handleSearchInput(t){this.searchQuery=t.target.value}toggleRecording(){this.dispatchEvent(new CustomEvent("toggle-recording")),this.downloadUrl&&this.clearDownload()}setDownload(t,e){this.downloadUrl&&URL.revokeObjectURL(this.downloadUrl),this.downloadUrl=URL.createObjectURL(t);const n=e.join("-").replace(/[^a-z0-9-_]/gi,"_");this.downloadFilename=`${n||"prompt-dj-beat"}.webm`}clearDownload(){this.downloadUrl&&(URL.revokeObjectURL(this.downloadUrl),this.downloadUrl=null)}async playPause(){this.previewingPromptId?(this.previewingPromptId=null,this.wasPlayingBeforePreview=!1,await this.liveMusicHelper.stop()):await this.liveMusicHelper.playPause(),this.clearDownload()}addFilteredPrompt(t){this.filteredPrompts=new Set([...this.filteredPrompts,t])}async handlePreviewToggle(t){if(this.previewingPromptId===t){this.previewingPromptId=null,await this.liveMusicHelper.pause(),this.wasPlayingBeforePreview&&(this.dispatchPromptsChanged(),await this.liveMusicHelper.play()),this.wasPlayingBeforePreview=!1;return}this.previewingPromptId===null&&(this.wasPlayingBeforePreview=this.playbackState==="playing"||this.playbackState==="loading",this.wasPlayingBeforePreview&&await this.liveMusicHelper.pause()),this.previewingPromptId=t,this.dispatchPromptsChanged(),await this.liveMusicHelper.play()}toggleAiWindow(){this.isAiToolWindowMinimized=!this.isAiToolWindowMinimized,window.innerWidth<=768&&!this.isAiToolWindowMinimized&&(this.audioFxState={...this.audioFxState,isWindowMinimized:!0}),this.isAiToolWindowMinimized&&(this.showBeatMakerHelp=!1)}toggleFxWindow(){const t=this.audioFxState.isWindowMinimized;this.audioFxState={...this.audioFxState,isWindowMinimized:!t},window.innerWidth<=768&&!this.audioFxState.isWindowMinimized&&(this.isAiToolWindowMinimized=!0)}handleFxChange(t,e){const n=e.target.valueAsNumber;switch(this.audioFxState={...this.audioFxState,[t]:n},t){case"pitch":this.liveMusicHelper.setPlaybackRate(n);break;case"pan":this.liveMusicHelper.setPan(n);break;case"reverb":this.liveMusicHelper.setReverb(n);break;case"delayTime":this.liveMusicHelper.setDelayTime(n);break;case"delayFeedback":this.liveMusicHelper.setDelayFeedback(n);break}}handleFxToggle(t){const e=!this.audioFxState[t],n=t==="is8D"&&e?0:this.audioFxState.pan;this.audioFxState={...this.audioFxState,[t]:e,pan:n},t==="isMono"?this.liveMusicHelper.setMono(e):t==="is8D"&&(this.liveMusicHelper.toggle8D(e),this.liveMusicHelper.setPan(n))}handlePerfFxChange(t){const{x:e,y:n}=t.detail;this.liveMusicHelper.setFilter(e),this.liveMusicHelper.setDelay(n)}buildCategorizedPromptList(){const t=new Map;for(const n of this.availablePromptsForAI)t.has(n.category)||t.set(n.category,[]),t.get(n.category).push(n.text);let e=`Here are the available instrumental prompts, grouped by category:

`;for(const[n,o]of t.entries())e+=`**${n}**
`,e+=o.map(s=>`- ${s}`).join(`
`),e+=`

`;return e}async generateBeat(){if(!this.beatMakerPromptText.trim()||this.isGeneratingBeat)return;this.isGeneratingBeat=!0;const t={type:q.OBJECT,properties:{prompts:{type:q.ARRAY,description:"A list of musical prompts and their weights to create the beat.",items:{type:q.OBJECT,properties:{promptName:{type:q.STRING,description:"The exact name of a prompt from the provided list."},weight:{type:q.NUMBER,description:"The weight for this prompt (0.1 to 2.0)."}},required:["promptName","weight"]}}},required:["prompts"]},e=`You are a world-class AI Beat Maker. Your task is to act as a sophisticated music producer, meticulously deconstructing a user's beat description and translating it into a professional, musically coherent combination of prompts from a provided list. Your final output must be a balanced and listenable piece of music.

**CRITICAL RULES FOR BEAT CONSTRUCTION:**

1.  **Deconstruct the User's Request:** Analyze the user's prompt for these key elements:
    *   **Genre & Style:** What is the primary genre and sub-genre? (e.g., Classic Rio Baile Funk, Dark Brazilian Phonk, 90s Boom Bap). Note regional specifics.
    *   **Mood & Vibe:** What is the core emotion? (e.g., Aggressive, melancholic, euphoric, dreamy, tense).
    *   **Tempo & Energy:** Is a BPM mentioned or implied (e.g., "fast," "slow," "laid-back")?
    *   **Key Instruments & Textures:** What specific instruments, sounds, or textures are requested? (e.g., "distorted cowbell," "Rhodes piano," "vinyl crackle").

2.  **Infer Negative Constraints:** Identify what to **exclude**. This is as important as what to include.
    *   If the user asks for an "instrumental," you **MUST NOT** include any vocal prompts (e.g. ad-libs, vocal chops, scatting).
    *   If the user asks for a "dark" or "menacing" beat, you **MUST AVOID** bright, happy, or euphoric elements (e.g., "uplifting trance anthem").
    *   If the request is "minimal" or "sparse," you **MUST** use fewer layers overall.

3.  **Build the "Core Trio" (MANDATORY FOUNDATION):** Every beat MUST be built on this clear foundation. Failure to do so results in a muddy, amateur-sounding mix.
    *   **ONE Rhythmic Backbone:** Select **EXACTLY ONE** primary drum or percussion prompt that defines the beat's pulse. **DO NOT EVER select multiple main drum loops.**
    *   **ONE Bass Element:** Select **EXACTLY ONE** bass prompt that locks in with the rhythm. **A track MUST NOT have two competing basslines.**
    *   **ONE Primary Melodic/Harmonic Element:** Select **ONE** lead instrument, chord progression, or sample that defines the song's main character.

4.  **Add Flavor and Texture (SUPPORTING LAYERS):**
    *   After the Core Trio is established, you may select **1 to 4 additional prompts** for detail and atmosphere.
    *   **PRIORITIZE SPECIFICITY:** When selecting, prefer highly descriptive prompts (e.g., "A classic 'hoover' synth sound...") over generic ones (e.g., "Synth").
    *   **AVOID REDUNDANCY AT ALL COSTS:** Never select multiple prompts serving the same musical function (e.g., two different piano chord loops, two lead synths). Use FX, Percussion, Pads, or subtle textures to enrich the sound without clutter.
    *   For "simple" or "minimal" beats, use only **1 or 2** supporting layers.

5.  **Assign Professional Weights for a CLEAN MIX:**
    *   **Rhythmic Backbone:** High weight (**1.0 to 1.5**). It must be prominent.
    *   **Bass Element:** Strong, present weight (**0.9 to 1.6**). It must be powerful and clear.
    *   **Primary Melodic/Harmonic Element:** Weight depends on its role. Main melody: **0.8-1.4**. Background chords: **0.5-0.9**.
    *   **Supporting Layers & FX (CRUCIAL):** These MUST have **LOWER** weights to sit "behind" the core elements. Use weights between **0.2 and 0.7**. This is the key to avoiding a muddy, unprofessional mix. Atmospheric pads, vinyl crackle, or secondary percussion must remain subtle.

6.  **Final JSON Output:** Your response must be ONLY a single, valid JSON object that strictly adheres to the provided schema: \`{ "prompts": [{ "promptName": "...", "weight": ... }] }\`.
    *   Ensure every \`promptName\` exists **EXACTLY** as it appears in the provided list.
    *   Do not invent new prompts or categories. Do not add comments or explanations.`,n=this.buildCategorizedPromptList();try{const o=[{text:e},{text:`User's beat idea: "${this.beatMakerPromptText}"`},{text:n}],s=await this.ai.models.generateContent({model:"gemini-2.5-flash",contents:o,config:{responseMimeType:"application/json",responseSchema:t}}),a=JSON.parse(s.text.trim());this.activePromptIds.forEach(c=>{const d=this.allPrompts.get(c);d&&(d.weight=0)}),this.activePromptIds.clear();const l=new Set;if(a.prompts&&Array.isArray(a.prompts))for(const c of a.prompts){const d=this.textToIdMap.get(c.promptName);if(d){l.add(d);const u=this.allPrompts.get(d);u&&(u.weight=Math.max(0,Math.min(2,c.weight||.5)))}}this.activePromptIds=l,this.dispatchPromptsChanged(),this.requestUpdate()}catch(o){console.error(o),this.dispatchEvent(new CustomEvent("error",{detail:"Failed to generate beat. The AI might be busy, or the request was too complex. Please try again."}))}finally{this.isGeneratingBeat=!1}}render(){const t=Y({"sidebar-open":this.isSidebarOpen}),e=T`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"></path></svg>`;return T`
      <div class="responsive-container ${t}">
        <div id="sidebar">
          <div id="sidebar-header">
            <h2>Prompts</h2>
            <input 
              type="search" 
              id="search-input" 
              placeholder="Search assets..."
              .value=${this.searchQuery}
              @input=${this.handleSearchInput}
              ?disabled=${this.isGeneratingBeat}
            />
          </div>
          <div id="asset-list">
            ${this.renderSidebar()}
          </div>
        </div>
        <div id="main-content">
          ${this.isSidebarOpen?T`<div id="sidebar-backdrop" @click=${()=>this.isSidebarOpen=!1}></div>`:""}
          <div id="toolbar">
            <div id="midi-controls">
              <button id="sidebar-toggle" @click=${()=>this.isSidebarOpen=!this.isSidebarOpen} title="Toggle Prompts">${e}</button>
              <button
                @click=${this.toggleShowMidi}
                class=${this.showMidi?"active":""}
                >MIDI</button
              >
              <select
                @change=${this.handleMidiInputChange}
                .value=${this.activeMidiInputId||""}
                style=${this.showMidi?"":"visibility: hidden"}>
                ${this.midiInputIds.length>0?this.midiInputIds.map(n=>T`<option value=${n}>
                          ${this.midiDispatcher.getDeviceName(n)}
                        </option>`):T`<option value="">No devices found</option>`}
              </select>
            </div>
            <div id="transport-controls">
              <play-pause-button .playbackState=${this.playbackState} @click=${this.playPause}></play-pause-button>
              <div id="complexity-control" class="slider-control">
                <label for="complexity-slider">COMPLEXITY</label>
                <input
                  id="complexity-slider"
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  .value=${this.complexity}
                  @input=${this.handleComplexityChange}
                />
              </div>
              <div id="volume-control" class="slider-control">
                <label for="volume-slider">MASTER VOL</label>
                <input
                  id="volume-slider"
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  .value=${this.masterVolume}
                  @input=${this.handleMasterVolumeChange}
                />
              </div>
            </div>
            <div id="main-actions">
              <div id="record-controls">
                  <button 
                    @click=${this.toggleRecording}
                    class=${this.isRecording?"record-active":""}
                    title=${this.isRecording?"Stop Recording":"Record Session"}
                    ?disabled=${this.isGeneratingBeat}
                    >${this.isRecording?"STOP":"REC"}
                  </button>
                  ${this.downloadUrl?T`
                    <a id="download-btn" class="button" .href=${this.downloadUrl} .download=${this.downloadFilename}>DOWNLOAD</a>
                  `:""}
              </div>
            </div>
          </div>
          <div id="track-area">
            ${this.renderTrackArea()}
          </div>
          ${this.renderAudioFxWindow()}
          ${this.renderAiToolsWindow()}
        </div>
      </div>
    `}renderSidebar(){const t=this.searchQuery.toLowerCase(),e=[...this.allPrompts.values()].filter(s=>!this.activePromptIds.has(s.promptId)&&(s.text.toLowerCase().includes(t)||s.category.toLowerCase().includes(t))).sort((s,a)=>s.text.localeCompare(a.text));if(e.length===0&&this.searchQuery)return T`<div class="empty-state">No prompts found.</div>`;const n=T`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"></path></svg>`,o=T`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6 6h12v12H6z"></path></svg>`;return e.map(s=>T`
      <div class="asset-item">
        <div class="asset-info" title=${s.text}>
            <div class="asset-color-swatch" style="background-color: ${s.color}"></div>
            <span>${s.text}</span>
        </div>
        <div class="asset-actions">
            <button 
                class="preview-btn ${this.previewingPromptId===s.promptId?"previewing":""}" 
                @click=${()=>this.handlePreviewToggle(s.promptId)} 
                title=${this.previewingPromptId===s.promptId?"Stop preview":`Preview ${s.text}`}
                ?disabled=${this.isGeneratingBeat}>
                ${this.previewingPromptId===s.promptId?o:n}
            </button>
            <button class="add-btn" @click=${()=>this.addPrompt(s.promptId)} title="Add ${s.text} to tracks" ?disabled=${this.isGeneratingBeat}>+</button>
        </div>
      </div>
    `)}renderTrackArea(){const t=[...this.allPrompts.values()].filter(e=>this.activePromptIds.has(e.promptId));return t.length===0?T`<div class="empty-state">Add prompts from the sidebar or use the AI Beat Maker<br>to create a beat from a description.</div>`:t.map(e=>T`<prompt-controller
        .promptId=${e.promptId}
        ?filtered=${this.filteredPrompts.has(e.text)}
        .cc=${e.cc}
        .text=${e.text}
        .weight=${e.weight}
        .color=${e.color}
        .category=${e.category}
        .midiDispatcher=${this.midiDispatcher}
        ?showCC=${this.showMidi}
        .audioLevel=${this.audioLevel}
        @prompt-changed=${this.handlePromptChanged}
        @prompt-removed=${this.handlePromptRemoved}>
      </prompt-controller>`)}renderAiToolsWindow(){const t=Y({"floating-window":!0,minimized:this.isAiToolWindowMinimized,"help-open":!this.isAiToolWindowMinimized&&this.showBeatMakerHelp}),e=T`
        <div class="window-body">
            <div class="ai-setting">
              <label for="beat-maker-prompt-input">Beat Description</label>
              <textarea
                  id="beat-maker-prompt-input"
                  class="ai-textarea"
                  placeholder="e.g., 'A simple, melancholic lo-fi hip hop beat with a mellow rhodes piano and some vinyl crackle.'"
                  .value=${this.beatMakerPromptText}
                  @input=${n=>this.beatMakerPromptText=n.target.value}
                  ?disabled=${this.isGeneratingBeat}
              ></textarea>
            </div>
            <button
              class="ai-button"
              @click=${this.generateBeat}
              ?disabled=${this.isGeneratingBeat||!this.beatMakerPromptText.trim()}
            >
              ${this.isGeneratingBeat?"Generating...":"Generate Beat"}
            </button>
        </div>
    `;return T`
      <div id="ai-tool-window" class=${t}>
        <div class="window-header">
          <h3 @click=${this.toggleAiWindow} style="cursor: pointer; flex-grow: 1;">AI Beat Maker</h3>
          <div class="header-buttons">
            <button title="Prompting Tips" @click=${n=>{n.stopPropagation(),this.showBeatMakerHelp=!this.showBeatMakerHelp}}>
              ${Np}
            </button>
            <button title="Toggle Window" @click=${n=>{n.stopPropagation(),this.toggleAiWindow()}}>
              ${this.isAiToolWindowMinimized?"□":"—"}
            </button>
          </div>
        </div>
        ${!this.isAiToolWindowMinimized&&this.showBeatMakerHelp?this.renderBeatMakerHelp():this.isAiToolWindowMinimized?"":e}
      </div>
    `}renderBeatMakerHelp(){return T`
      <div id="beat-maker-help">
        <h4>Pro-Tips for Crafting Better Beats</h4>
        <ul>
          <li>
            <strong>Be Specific & Descriptive</strong>
            <p>The more detail you give, the better. Name the genre, mood, key instruments, and tempo.</p>
            <p class="example"><em>Instead of:</em> "sad piano"<br><em>Try:</em> "A slow, melancholic piano ballad with soft strings and rain sounds."</p>
          </li>
          <li>
            <strong>Experiment and Refine</strong>
            <p>Small changes in wording can make a big difference. Try swapping "funky" for "groovy", or "fast" for "uptempo" and see what you get.</p>
          </li>
          <li>
            <strong>Tell It What to Avoid</strong>
            <p>You can guide the AI by telling it what <em>not</em> to include in the prompt. For example, add "no drums" or "instrumental only, no vocals".</p>
          </li>
           <li>
            <strong>Use the Controls</strong>
            <p>Your prompt is just the start! Use the <strong>Complexity</strong> slider and the <strong>Audio Effects</strong> to fine-tune your sound in real-time.</p>
          </li>
        </ul>
        <button class="ai-button" @click=${()=>this.showBeatMakerHelp=!1}>Got it</button>
      </div>
    `}renderAudioFxWindow(){const{pitch:t,pan:e,reverb:n,delayTime:o,delayFeedback:s,isMono:a,is8D:l,isWindowMinimized:c}=this.audioFxState,d=Y({"floating-window":!0,minimized:c,"pad-view-active":!c&&this.activeFxTab==="pad"});return T`
        <div id="audio-fx-window" class=${d}>
            <div class="window-header">
                <h3 @click=${this.toggleFxWindow} style="cursor: pointer; flex-grow: 1;">Audio Effects</h3>
                <div class="header-buttons">
                    <button title="Toggle Window" @click=${u=>{u.stopPropagation(),this.toggleFxWindow()}}>${c?"□":"—"}</button>
                </div>
            </div>
            <div class="window-body">
                <div class="ai-tabs">
                     <button 
                        class=${Y({"ai-tab-btn":!0,active:this.activeFxTab==="controls"})}
                        @click=${()=>this.activeFxTab="controls"}
                    >Controls</button>
                    <button 
                        class=${Y({"ai-tab-btn":!0,active:this.activeFxTab==="pad"})}
                        @click=${()=>this.activeFxTab="pad"}
                    >Performance Pad</button>
                </div>

                ${this.activeFxTab==="controls"?T`
                    <div class="fx-content">
                        <div class="fx-setting">
                            <label for="pitch-slider">Pitch <span>${t.toFixed(2)}x</span></label>
                            <input id="pitch-slider" class="fx-slider" type="range" min="0.5" max="2" step="0.01" .value=${t} @input=${u=>this.handleFxChange("pitch",u)} />
                        </div>

                        <div class="fx-setting">
                            <label for="pan-slider">Stereo Pan <span>${e===0?"C":e>0?`R ${Math.round(e*100)}`:`L ${Math.round(Math.abs(e)*100)}`}</span></label>
                            <input id="pan-slider" class="fx-slider" type="range" min="-1" max="1" step="0.01" .value=${e} ?disabled=${l} @input=${u=>this.handleFxChange("pan",u)} />
                        </div>
                        
                        <div class="fx-setting">
                            <label for="reverb-slider">Reverb <span>${Math.round(n*100)}%</span></label>
                            <input id="reverb-slider" class="fx-slider" type="range" min="0" max="1" step="0.01" .value=${n} @input=${u=>this.handleFxChange("reverb",u)} />
                        </div>

                        <div class="fx-setting">
                            <label for="delay-time-slider">Delay Time <span>${o.toFixed(2)}s</span></label>
                            <input id="delay-time-slider" class="fx-slider" type="range" min="0.01" max="1" step="0.01" .value=${o} @input=${u=>this.handleFxChange("delayTime",u)} />
                        </div>
                        
                        <div class="fx-setting">
                            <label for="delay-feedback-slider">Delay Feedback <span>${Math.round(s*100)}%</span></label>
                            <input id="delay-feedback-slider" class="fx-slider" type="range" min="0" max="0.95" step="0.01" .value=${s} @input=${u=>this.handleFxChange("delayFeedback",u)} />
                        </div>

                        <div class="fx-buttons">
                            <button @click=${()=>this.handleFxToggle("isMono")} class=${a?"active":""}>Mono</button>
                            <button @click=${()=>this.handleFxToggle("is8D")} class=${l?"active":""}>8D Audio</button>
                        </div>
                    </div>
                `:T`
                    <div class="fx-pad-wrapper">
                        <fx-pad @fx-changed=${this.handlePerfFxChange}></fx-pad>
                    </div>
                `}
            </div>
        </div>
    `}};R.styles=ae`
    :host {
      height: 100%;
      width: 100%;
      display: block; /* Changed to block for the inner container to control flex */
      box-sizing: border-box;
      background: transparent; /* Changed for visualizer */
      color: #f0f0f0;
      font-family: 'Inter', sans-serif;
    }
    .responsive-container {
      display: flex;
      width: 100%;
      height: 100%;
      position: relative;
      overflow-x: hidden;
      background: transparent;
    }
    #sidebar {
      width: 320px;
      min-width: 320px;
      height: 100%;
      background: rgba(17, 17, 17, 0.9);
      backdrop-filter: blur(10px);
      display: flex;
      flex-direction: column;
      overflow-y: hidden;
      border-right: 1px solid rgba(255, 255, 255, 0.1);
      transition: transform 0.3s ease-in-out;
      z-index: 50;
    }
    #sidebar-header {
      padding: 20px 20px 15px 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      flex-shrink: 0;
    }
    #sidebar-header h2 {
      color: white;
      margin: 0 0 15px 0;
      font-size: 20px;
      font-weight: 500;
    }
    #search-input {
      width: 100%;
      padding: 8px 12px;
      border-radius: 6px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      background: #1C1C1C;
      color: white;
      font-family: inherit;
      font-size: 14px;
      box-sizing: border-box;
      transition: border-color 0.2s ease;
    }
    #search-input:focus {
      outline: none;
      border-color: #F9B200;
    }
    #asset-list {
      flex-grow: 1;
      padding: 10px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      overflow-y: auto;
    }
    .asset-item {
      display: flex;
      align-items: center;
      gap: 5px;
      background: #1C1C1C;
      border-radius: 6px;
      border: 1px solid transparent;
      padding: 0 8px 0 15px;
      transition: all 0.2s ease;
    }
    .asset-item:hover {
      border-color: rgba(255, 255, 255, 0.2);
      background: #282828;
    }
    .asset-info {
      flex-grow: 1;
      display: flex;
      align-items: center;
      gap: 12px;
      font-family: inherit;
      font-size: 14px;
      color: #f0f0f0;
      user-select: none;
      overflow: hidden;
      white-space: nowrap;
    }
    .asset-info span {
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .asset-actions {
        display: flex;
        flex-shrink: 0;
    }
    .preview-btn, .add-btn {
      width: 36px;
      height: 36px;
      flex-shrink: 0;
      background: transparent;
      border: none;
      color: #999;
      cursor: pointer;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }
    .preview-btn:hover, .add-btn:hover {
      background: #3c3c3c;
      color: white;
    }
    .preview-btn.previewing {
      color: #F9B200;
    }
    .preview-btn svg {
      width: 20px;
      height: 20px;
    }
    .add-btn {
        font-size: 24px;
        font-weight: 300;
    }
    .asset-color-swatch {
      width: 14px;
      height: 14px;
      border-radius: 4px;
      flex-shrink: 0;
    }
    #main-content {
      flex-grow: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
      overflow: hidden;
      background: transparent;
    }
    #toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 20px;
      background: rgba(17, 17, 17, 0.8);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      position: relative;
      z-index: 2;
      gap: 15px;
      flex-shrink: 0;
      height: 80px;
      box-sizing: border-box;
    }
    #transport-controls {
      display: flex;
      align-items: center;
      gap: 20px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    #midi-controls, #record-controls {
      display: flex;
      gap: 10px;
      align-items: center;
    }
    #sidebar-toggle {
      display: none;
      background: transparent;
      border: none;
      color: #f0f0f0;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
    }
    #sidebar-toggle svg {
      width: 100%;
      height: 100%;
    }
    #main-actions {
      display: flex;
      gap: 10px;
      align-items: center;
    }
    #track-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;
      padding: 20px;
      overflow-y: auto;
      position: relative;
      z-index: 1;
    }
    .empty-state {
      color: #aaa;
      background: rgba(0,0,0,0.4);
      padding: 20px;
      border-radius: 8px;
      text-align: center;
      margin: auto;
      font-size: 1.1em;
      line-height: 1.6;
    }
    play-pause-button {
      width: 60px;
      height: 60px;
    }
    button {
      font: inherit;
      font-weight: 500;
      cursor: pointer;
      color: #f0f0f0;
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 6px;
      user-select: none;
      padding: 6px 12px;
      transition: all 0.2s ease;
    }
    button:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 0.5);
    }
    button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: transparent;
    }

    button.active {
      background-color: #F9B200;
      color: #0A0A0A;
      border-color: #F9B200;
    }
    button.active:hover {
      background-color: #fbc13a;
    }

    button.record-active {
      background-color: #e74c3c;
      color: white;
      border-color: #e74c3c;
      animation: pulse 1.5s infinite;
    }
    #download-btn {
      background: #F9B200;
      color: #0A0A0A;
      border-color: #F9B200;
      font-weight: bold;
      text-decoration: none;
      display: inline-block;
    }
    #download-btn:hover {
      background: #fbc13a;
    }
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
      100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
    }
    select {
      font: inherit;
      padding: 5px 8px;
      background: #1C1C1C;
      color: #f0f0f0;
      border-radius: 6px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      outline: none;
      cursor: pointer;
    }
    .slider-control {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      color: #ccc;
    }
    .slider-control label {
      font-size: 10px;
      font-weight: bold;
      letter-spacing: 1px;
      user-select: none;
    }
    .slider-control input[type="range"] {
      -webkit-appearance: none;
      appearance: none;
      width: 150px;
      height: 5px;
      background: #0A0A0A;
      outline: none;
      border-radius: 3px;
      cursor: pointer;
    }
    .slider-control input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 15px;
      height: 15px;
      background: #f0f0f0;
      cursor: pointer;
      border-radius: 50%;
      border: none;
    }
    .slider-control input[type="range"]::-moz-range-thumb {
      width: 15px;
      height: 15px;
      background: #f0f0f0;
      cursor: pointer;
      border-radius: 50%;
      border: none;
    }

    #volume-control input[type="range"] {
        width: 120px;
    }
    
    /* Floating Window Styles */
    .floating-window {
      position: absolute;
      bottom: 20px;
      background: rgba(28, 28, 28, 0.9);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.5);
      z-index: 10;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    }
    #ai-tool-window {
      right: 20px;
      width: 380px;
    }
    #ai-tool-window.help-open {
        height: auto;
    }
    #audio-fx-window {
      left: 20px;
      width: 300px;
      transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1), width 0.3s ease, height 0.3s ease;
    }
    #audio-fx-window.pad-view-active {
      width: 280px;
      height: 360px;
    }
    .floating-window.minimized {
      height: 40px !important;
      width: 220px !important;
    }
    #audio-fx-window.minimized {
        width: 180px !important;
    }
    .window-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      height: 40px;
      background: #282828;
      flex-shrink: 0;
      user-select: none;
    }
    .window-header h3 {
      color: white;
      font-size: 14px;
      font-weight: 500;
      margin: 0;
    }
    .header-buttons {
        display: flex;
        align-items: center;
        gap: 4px;
    }
    .header-buttons button {
      background: transparent;
      border: none;
      color: #aaa;
      cursor: pointer;
      font-size: 20px;
      padding: 0;
      width: 26px;
      height: 26px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }
    .header-buttons button:hover {
      color: white;
      background: rgba(255, 255, 255, 0.15);
    }
    .window-body {
      padding: 15px;
      display: flex;
      flex-direction: column;
      gap: 15px;
      flex-grow: 1;
      overflow: hidden;
    }
    .ai-setting {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    .ai-setting label {
        font-size: 13px;
        color: #aaa;
        font-weight: 500;
    }
    .ai-textarea {
      width: 100%;
      box-sizing: border-box;
      background: #0A0A0A;
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      border-radius: 4px;
      padding: 10px;
      resize: none;
      font-family: inherit;
    }
    #beat-maker-prompt-input {
      height: 120px;
    }
    .ai-button {
      border: none;
      color: white;
      padding: 12px;
      border-radius: 6px;
      font-size: 15px;
      font-weight: 600;
      margin-top: 5px;
      background: #8e44ad;
    }
    .ai-button:hover:not(:disabled) {
      filter: brightness(1.1);
    }
    .ai-button:disabled {
      background: #444 !important;
      cursor: wait;
      color: #888;
    }
    #beat-maker-help {
        padding: 15px;
        background: #111;
        max-height: 400px;
        overflow-y: auto;
    }
    #beat-maker-help h4 {
        margin: 0 0 15px 0;
        font-size: 16px;
        color: #F9B200;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding-bottom: 10px;
    }
    #beat-maker-help ul {
        list-style: none;
        padding: 0;
        margin: 0 0 20px 0;
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    #beat-maker-help li {
        line-height: 1.5;
    }
    #beat-maker-help p {
        margin: 5px 0 0 0;
        color: #ccc;
        font-size: 13px;
    }
    #beat-maker-help strong {
        color: #f0f0f0;
        font-weight: 500;
    }
    #beat-maker-help .example {
        background: rgba(0,0,0,0.3);
        padding: 8px;
        border-radius: 4px;
        border-left: 3px solid #F9B200;
        margin-top: 8px;
        font-size: 12px;
    }
    #beat-maker-help .example em {
        color: #aaa;
        font-style: normal;
    }
    #beat-maker-help .ai-button {
      width: 100%;
      text-align: center;
      background: #444;
      margin-top: 0;
    }


    #sidebar-backdrop {
        display: none;
    }

    /* FX Window Styles */
    .fx-content {
        padding: 15px;
        display: flex;
        flex-direction: column;
        gap: 18px;
        flex-grow: 1;
        overflow-y: auto;
    }
    .fx-setting {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .fx-setting label {
      display: flex;
      justify-content: space-between;
      font-size: 13px;
      color: #aaa;
      font-weight: 500;
    }
    .fx-setting label span:last-child {
      font-family: monospace;
      color: #f0f0f0;
      background: rgba(0,0,0,0.2);
      padding: 1px 4px;
      border-radius: 3px;
    }
    .fx-slider {
      -webkit-appearance: none;
      appearance: none;
      width: 100%;
      height: 6px;
      background: #0A0A0A;
      outline: none;
      border-radius: 3px;
      cursor: pointer;
      border: 1px solid rgba(255, 255, 255, 0.2);
      margin: 0;
    }
    .fx-slider::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 16px;
      height: 16px;
      background: #f0f0f0;
      cursor: pointer;
      border-radius: 50%;
      border: none;
      transition: transform 0.1s ease;
    }
    .fx-slider:active::-webkit-slider-thumb {
      transform: scale(1.2);
    }
    .fx-slider::-moz-range-thumb {
      width: 16px;
      height: 16px;
      background: #f0f0f0;
      cursor: pointer;
      border-radius: 50%;
      border: none;
    }
    .fx-buttons {
      display: flex;
      gap: 10px;
      margin-top: 5px;
    }
    .fx-buttons button {
      flex: 1;
    }

    .fx-pad-wrapper {
        flex-grow: 1;
        display: flex;
        padding: 15px;
    }

    .fx-pad-wrapper fx-pad {
        flex-grow: 1;
    }
    
    .window-body .ai-tabs {
      display: flex;
      flex-shrink: 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding: 0 15px;
      margin: -15px -15px 0 -15px;
    }
    .window-body .ai-tab-btn {
      flex: 1;
      padding: 12px;
      background: transparent;
      border: none;
      color: #aaa;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      border-bottom: 2px solid transparent;
      margin-bottom: -1px;
    }
    .window-body .ai-tab-btn:hover {
      background: rgba(255,255,255,0.05);
      color: white;
    }
    .window-body .ai-tab-btn.active {
      color: white;
      border-bottom-color: #F9B200;
    }


    /* Responsive Design */
    @media (max-width: 768px) {
      #sidebar-toggle {
        display: block;
      }
      
      #sidebar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 280px;
        min-width: 280px;
        z-index: 100;
        transform: translateX(-100%);
        box-shadow: 4px 0px 15px rgba(0,0,0,0.4);
      }
      
      .sidebar-open #sidebar {
        transform: translateX(0);
      }
      
      .sidebar-open #sidebar-backdrop {
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 99;
      }
      
      #main-content {
        width: 100%;
      }
      
      #toolbar {
        flex-wrap: wrap;
        height: auto;
        justify-content: space-between;
        gap: 15px;
      }
      
      #transport-controls {
        position: static;
        transform: none;
        order: -1;
        width: 100%;
        justify-content: center;
        padding-bottom: 15px;
        margin-bottom: 15px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
      }

      #complexity-control {
          display: none;
      }

      play-pause-button {
          width: 50px;
          height: 50px;
      }
      
      #midi-controls { order: 0; }
      #main-actions { order: 1; }

      #track-area {
        padding: 15px;
        gap: 15px;
      }

      /* Floating Window Mobile Layout */
      .floating-window {
        /* Default state is maximized on mobile */
        width: calc(100% - 20px);
        left: 10px;
        right: 10px;
        bottom: 10px;
      }

      /* Both windows are visible now */
      #audio-fx-window {
          display: flex;
      }

      #ai-tool-window.minimized {
        width: 180px !important;
        left: auto;
        right: 10px;
      }
      #audio-fx-window.minimized {
        width: 160px !important;
        left: 10px;
        right: auto;
      }
    }
    @media (max-width: 480px) {
        #midi-controls {
            flex-grow: 1;
        }
        #midi-controls select {
            width: 100%;
            max-width: 150px;
        }
        #main-actions {
            flex-grow: 1;
            justify-content: flex-end;
        }
        .asset-item {
            padding: 0 4px 0 12px;
        }
        .preview-btn, .add-btn {
            width: 32px;
            height: 32px;
        }
        #volume-control {
            display: none;
        }
    }
  `;F([N()],R.prototype,"activePromptIds",2);F([L({type:Boolean})],R.prototype,"showMidi",2);F([L({type:String})],R.prototype,"playbackState",2);F([N()],R.prototype,"audioLevel",2);F([N()],R.prototype,"midiInputIds",2);F([N()],R.prototype,"activeMidiInputId",2);F([N()],R.prototype,"complexity",2);F([N()],R.prototype,"masterVolume",2);F([N()],R.prototype,"searchQuery",2);F([N()],R.prototype,"isRecording",2);F([N()],R.prototype,"downloadUrl",2);F([N()],R.prototype,"downloadFilename",2);F([N()],R.prototype,"previewingPromptId",2);F([N()],R.prototype,"wasPlayingBeforePreview",2);F([N()],R.prototype,"isAiToolWindowMinimized",2);F([N()],R.prototype,"showBeatMakerHelp",2);F([N()],R.prototype,"audioFxState",2);F([N()],R.prototype,"activeFxTab",2);F([N()],R.prototype,"beatMakerPromptText",2);F([N()],R.prototype,"isGeneratingBeat",2);F([N()],R.prototype,"isSidebarOpen",2);F([L({type:Object})],R.prototype,"filteredPrompts",2);R=F([te("prompt-dj-midi")],R);var Fp=Object.defineProperty,Lp=Object.getOwnPropertyDescriptor,Mt=(t,e,n,o)=>{for(var s=o>1?void 0:o?Lp(e,n):e,a=t.length-1,l;a>=0;a--)(l=t[a])&&(s=(o?l(e,n,s):l(s))||s);return o&&s&&Fp(e,n,s),s};let he=class extends z{constructor(){super(...arguments),this.message="",this.showing=!1}renderMessageWithLinks(){const t=/(https?:\/\/[^\s]+)/g;return this.message.split(t).map((n,o)=>o%2===0?n:T`<a href=${n} target="_blank" rel="noopener">${n}</a>`)}render(){return T`<div class=${Y({showing:this.showing,toast:!0})}>
      <div class="message">${this.renderMessageWithLinks()}</div>
      <button @click=${this.hide}>✕</button>
    </div>`}show(t){this.showing=!0,this.message=t}hide(){this.showing=!1}};he.styles=ae`
    .toast {
      line-height: 1.6;
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #1C1C1C;
      color: white;
      padding: 15px 20px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 15px;
      width: min(450px, 80vw);
      transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 5px 25px 0 rgba(0, 0, 0, 0.5);
      text-wrap: pretty;
      z-index: 1000;
    }
    button {
      background: transparent;
      border: none;
      color: #999;
      cursor: pointer;
      font-size: 1.2em;
      padding: 5px;
      border-radius: 50%;
      width: 28px;
      height: 28px;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }
    button:hover {
      color: white;
      background-color: rgba(255, 255, 255, 0.1);
    }
    .toast:not(.showing) {
      transition-duration: 1s;
      transform: translate(-50%, -200%);
    }
    a {
      color: #F9B200;
      text-decoration: underline;
    }
    a:hover {
      color: #fbc13a;
    }
  `;Mt([L({type:String})],he.prototype,"message",2);Mt([L({type:Boolean})],he.prototype,"showing",2);he=Mt([te("toast-message")],he);/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/function Up(t){const e=atob(t),n=e.length,o=new Uint8Array(n);for(let s=0;s<n;s++)o[s]=e.charCodeAt(s);return o}async function $p(t,e,n,o){const s=e.createBuffer(o,t.length/2/o,n),a=new Int16Array(t.buffer),l=a.length,c=new Float32Array(l);for(let d=0;d<l;d++)c[d]=a[d]/32768;for(let d=0;d<o;d++){const u=c.filter((f,p)=>p%o===d);s.copyToChannel(u,d)}return s}/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/function Vp(t,e){let n=-1/0,o;return(...s)=>{const a=Date.now();return a-n>=e&&(o=t(...s),n=a),o}}/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */class Gp{constructor(e){this.lfo=null,this.is8DEnabled=!1,this.isMono=!1,this.audioContext=e,this.input=e.createGain(),this.output=e.createGain(),this.panner=e.createStereoPanner(),this.monoMerger=e.createChannelMerger(1),this.filter=e.createBiquadFilter(),this.filter.type="lowpass",this.filter.frequency.value=e.sampleRate/2,this.filter.Q.value=1,this.delay=e.createDelay(1),this.delay.delayTime.value=.5,this.delayWetGain=e.createGain(),this.delayWetGain.gain.value=0,this.delayFeedback=e.createGain(),this.delayFeedback.gain.value=.4,this.reverbConvolver=e.createConvolver(),this.reverbConvolver.buffer=this.createReverbImpulse(2,2),this.reverbWetGain=e.createGain(),this.reverbWetGain.gain.value=0,this.eightDConvolver=e.createConvolver(),this.eightDImpulseReverb=this.createReverbImpulse(1.5,2),this.eightDConvolver.buffer=this.eightDImpulseReverb,this.lfoGain=e.createGain(),this.lfoGain.gain.value=1,this.eightDWetGain=this.audioContext.createGain(),this.eightDWetGain.gain.value=0,this.eightDDryGain=this.audioContext.createGain(),this.eightDDryGain.gain.value=1,this.input.connect(this.filter),this.filter.connect(this.panner),this.panner.connect(this.eightDDryGain),this.eightDDryGain.connect(this.output),this.panner.connect(this.eightDConvolver),this.eightDConvolver.connect(this.eightDWetGain),this.eightDWetGain.connect(this.output),this.panner.connect(this.delayWetGain),this.delayWetGain.connect(this.delay),this.delay.connect(this.delayFeedback),this.delayFeedback.connect(this.delay),this.delay.connect(this.output),this.panner.connect(this.reverbWetGain),this.reverbWetGain.connect(this.reverbConvolver),this.reverbConvolver.connect(this.output)}setPan(e){this.is8DEnabled||this.panner.pan.setTargetAtTime(e,this.audioContext.currentTime,.05)}setMono(e){e!==this.isMono&&(this.isMono=e,this.input.disconnect(),this.isMono?(this.input.connect(this.monoMerger),this.monoMerger.connect(this.filter)):(this.monoMerger.numberOfInputs>0&&this.monoMerger.disconnect(this.filter),this.input.connect(this.filter)))}toggle8D(e){if(this.is8DEnabled===e)return;this.is8DEnabled=e;const n=.2,o=this.audioContext.currentTime;e?(this.eightDDryGain.gain.linearRampToValueAtTime(0,o+n),this.eightDWetGain.gain.linearRampToValueAtTime(1,o+n),this.lfo||(this.lfo=this.audioContext.createOscillator(),this.lfo.type="sine",this.lfo.frequency.value=.1,this.lfo.connect(this.lfoGain),this.lfoGain.connect(this.panner.pan),this.lfo.start())):(this.eightDDryGain.gain.linearRampToValueAtTime(1,o+n),this.eightDWetGain.gain.linearRampToValueAtTime(0,o+n),this.lfo&&(this.lfo.stop(o+n),this.lfo.disconnect(),this.lfo=null),this.panner.pan.cancelScheduledValues(o),this.panner.pan.setTargetAtTime(0,o+n,.1))}setFilter(e){const o=this.audioContext.sampleRate/2,s=40*Math.pow(o/40,e);this.filter.frequency.setTargetAtTime(s,this.audioContext.currentTime,.05)}setDelay(e){this.delayWetGain.gain.setTargetAtTime(e,this.audioContext.currentTime,.05)}setReverb(e){this.reverbWetGain.gain.setTargetAtTime(e,this.audioContext.currentTime,.05)}setDelayTime(e){this.delay.delayTime.setTargetAtTime(e,this.audioContext.currentTime,.05)}setDelayFeedback(e){this.delayFeedback.gain.setTargetAtTime(e,this.audioContext.currentTime,.05)}createReverbImpulse(e,n){const o=this.audioContext.sampleRate,s=o*e,a=this.audioContext.createBuffer(2,s,o);for(let l=0;l<2;l++){const c=a.getChannelData(l);for(let d=0;d<s;d++)c[d]=(Math.random()*2-1)*Math.pow(1-d/s,n)}return a}}/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/class Bp extends EventTarget{constructor(e,n){super(),this.session=null,this.sessionPromise=null,this.filteredPrompts=new Set,this.nextStartTime=0,this.bufferTime=2,this.extraDestination=null,this.playbackState="stopped",this.isRecording=!1,this.mediaRecorder=null,this.streamDestination=null,this.recordedChunks=[],this.playbackRate=1,this.isTryingToConnectOrPlay=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.setWeightedPrompts=Vp(async o=>{if(this.prompts=o,this.activePrompts.length===0&&(this.playbackState==="playing"||this.playbackState==="loading")){this.dispatchEvent(new CustomEvent("error",{detail:"There needs to be one active prompt to play."})),this.pause();return}if(this.session)try{await this.session.setWeightedPrompts({weightedPrompts:this.activePrompts})}catch(s){this.dispatchEvent(new CustomEvent("error",{detail:s.message})),this.pause()}},200),this.ai=e,this.model=n,this.prompts=new Map,this.audioContext=new AudioContext({sampleRate:48e3}),this.outputNode=this.audioContext.createGain(),this.masterVolumeNode=this.audioContext.createGain(),this.masterVolumeNode.gain.value=.8,this.audioEffects=new Gp(this.audioContext),this.outputNode.connect(this.audioEffects.input),this.audioEffects.output.connect(this.masterVolumeNode)}getSession(){return this.sessionPromise||(this.sessionPromise=this.connect()),this.sessionPromise}handleDisconnection(){if(this.session=null,this.sessionPromise=null,this.isTryingToConnectOrPlay&&this.reconnectAttempts<this.maxReconnectAttempts){this.setPlaybackState("loading");const e=Math.pow(2,this.reconnectAttempts)*1e3+Math.random()*1e3;this.dispatchEvent(new CustomEvent("error",{detail:`Connection lost. Reconnecting in ${Math.round(e/1e3)}s... (Attempt ${this.reconnectAttempts+1})`})),this.reconnectAttempts++,setTimeout(()=>{this.isTryingToConnectOrPlay&&this.play()},e)}else this.isTryingToConnectOrPlay&&(this.dispatchEvent(new CustomEvent("error",{detail:"Could not reconnect to the server. Please restart playback."})),this.stop())}async connect(){return this.sessionPromise=this.ai.live.music.connect({model:this.model,callbacks:{onmessage:async e=>{var n;e.setupComplete&&(this.reconnectAttempts=0),e.filteredPrompt&&(this.filteredPrompts=new Set([...this.filteredPrompts,e.filteredPrompt.text]),this.dispatchEvent(new CustomEvent("filtered-prompt",{detail:e.filteredPrompt}))),(n=e.serverContent)!=null&&n.audioChunks&&await this.processAudioChunks(e.serverContent.audioChunks)},onerror:()=>{this.handleDisconnection()},onclose:()=>{this.handleDisconnection()}}}),this.sessionPromise.catch(()=>{this.session||this.handleDisconnection()}),this.sessionPromise}setPlaybackState(e){this.playbackState=e,this.dispatchEvent(new CustomEvent("playback-state-changed",{detail:e}))}async processAudioChunks(e){if(this.playbackState==="paused"||this.playbackState==="stopped")return;const n=await $p(Up(e[0].data),this.audioContext,48e3,2),o=this.audioContext.createBufferSource();if(o.buffer=n,o.playbackRate.value=this.playbackRate,o.connect(this.outputNode),this.nextStartTime===0&&(this.nextStartTime=this.audioContext.currentTime+this.bufferTime,setTimeout(()=>{this.playbackState==="loading"&&this.setPlaybackState("playing")},this.bufferTime*1e3)),this.nextStartTime<this.audioContext.currentTime){this.setPlaybackState("loading"),this.nextStartTime=0;return}o.start(this.nextStartTime),this.nextStartTime+=n.duration}get activePrompts(){return Array.from(this.prompts.values()).filter(e=>!this.filteredPrompts.has(e.text)&&e.weight>0)}async play(){if(this.isTryingToConnectOrPlay=!0,this.activePrompts.length===0){this.dispatchEvent(new CustomEvent("error",{detail:"Add and enable a prompt to start playing."})),this.isTryingToConnectOrPlay=!1;return}if(this.setPlaybackState("loading"),this.session=await this.getSession(),!this.isTryingToConnectOrPlay){this.stop();return}await this.setWeightedPrompts(this.prompts),this.audioContext.resume(),this.session.play(),this.masterVolumeNode.connect(this.audioContext.destination),this.extraDestination&&this.masterVolumeNode.connect(this.extraDestination),this.outputNode.gain.setValueAtTime(0,this.audioContext.currentTime),this.outputNode.gain.linearRampToValueAtTime(1,this.audioContext.currentTime+.1)}pause(){this.isTryingToConnectOrPlay=!1,this.isRecording&&this.stopRecording(),this.session&&this.session.pause(),this.setPlaybackState("paused"),this.outputNode.gain.setValueAtTime(this.outputNode.gain.value,this.audioContext.currentTime),this.outputNode.gain.linearRampToValueAtTime(0,this.audioContext.currentTime+.1),this.nextStartTime=0}stop(){this.isTryingToConnectOrPlay=!1,this.isRecording&&this.stopRecording(),this.session&&this.session.stop(),this.setPlaybackState("stopped"),this.outputNode.gain.setValueAtTime(0,this.audioContext.currentTime),this.nextStartTime=0,this.session=null,this.sessionPromise=null,this.reconnectAttempts=0}async playPause(){switch(this.playbackState){case"playing":return this.pause();case"paused":case"stopped":return this.reconnectAttempts=0,this.play();case"loading":return this.stop()}}startRecording(){if(this.isRecording||this.playbackState!=="playing"){this.playbackState!=="playing"&&this.dispatchEvent(new CustomEvent("error",{detail:"Must be playing to record."}));return}this.streamDestination=this.audioContext.createMediaStreamDestination(),this.masterVolumeNode.connect(this.streamDestination);const e="audio/webm";this.mediaRecorder=new MediaRecorder(this.streamDestination.stream,{mimeType:e}),this.recordedChunks=[],this.mediaRecorder.ondataavailable=s=>{s.data.size>0&&this.recordedChunks.push(s.data)};const n=this.masterVolumeNode,o=this.streamDestination;this.mediaRecorder.onstop=()=>{const s=new Blob(this.recordedChunks,{type:e}),a=this.activePrompts.map(l=>l.text);this.dispatchEvent(new CustomEvent("recording-finished",{detail:{blob:s,prompts:a}})),this.isRecording=!1,this.dispatchEvent(new CustomEvent("recording-state-changed",{detail:{isRecording:this.isRecording}})),n.disconnect(o),this.streamDestination=null,this.mediaRecorder=null},this.mediaRecorder.start(),this.isRecording=!0,this.dispatchEvent(new CustomEvent("recording-state-changed",{detail:{isRecording:this.isRecording}}))}stopRecording(){!this.isRecording||!this.mediaRecorder||this.mediaRecorder.stop()}setMasterVolume(e){this.masterVolumeNode.gain.setTargetAtTime(e,this.audioContext.currentTime,.02)}setPlaybackRate(e){this.playbackRate=e}setPan(e){this.audioEffects.setPan(e)}toggle8D(e){this.audioEffects.toggle8D(e)}setMono(e){this.audioEffects.setMono(e)}setFilter(e){this.audioEffects.setFilter(e)}setDelay(e){this.audioEffects.setDelay(e)}setReverb(e){this.audioEffects.setReverb(e)}setDelayTime(e){this.audioEffects.setDelayTime(e)}setDelayFeedback(e){this.audioEffects.setDelayFeedback(e)}}/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/class qp extends EventTarget{constructor(e){super(),this.rafId=null,this.start=this.loop,this.node=e.createAnalyser(),this.node.smoothingTimeConstant=.3,this.node.fftSize=256,this.freqData=new Uint8Array(this.node.frequencyBinCount),this.loop=this.loop.bind(this)}getCurrentLevel(){return this.node.getByteFrequencyData(this.freqData),this.freqData.reduce((n,o)=>n+o,0)/this.freqData.length/255}getFrequencyData(){return this.node.getByteFrequencyData(this.freqData),this.freqData}loop(){this.rafId=requestAnimationFrame(this.loop);const e=this.getCurrentLevel();this.dispatchEvent(new CustomEvent("audio-level-changed",{detail:e}))}stop(){this.rafId&&cancelAnimationFrame(this.rafId)}}var zp=Object.defineProperty,Hp=Object.getOwnPropertyDescriptor,Ke=(t,e,n,o)=>{for(var s=o>1?void 0:o?Hp(e,n):e,a=t.length-1,l;a>=0;a--)(l=t[a])&&(s=(o?l(e,n,s):l(s))||s);return o&&s&&zp(e,n,s),s};const oo=1,Jp=2,Wp=1;let me=class extends z{constructor(){super(),this.value=0,this.color="#000",this.audioLevel=0,this.dragStartPos=0,this.dragStartValue=0,this.handlePointerDown=this.handlePointerDown.bind(this),this.handlePointerMove=this.handlePointerMove.bind(this),this.handlePointerUp=this.handlePointerUp.bind(this)}handlePointerDown(t){t.preventDefault(),this.dragStartPos=t.clientY,this.dragStartValue=this.value,document.body.classList.add("dragging"),window.addEventListener("pointermove",this.handlePointerMove),window.addEventListener("pointerup",this.handlePointerUp)}handlePointerMove(t){const e=this.dragStartPos-t.clientY;this.value=this.dragStartValue+e*.01,this.value=Math.max(0,Math.min(2,this.value)),this.dispatchEvent(new CustomEvent("input",{detail:this.value}))}handlePointerUp(){window.removeEventListener("pointermove",this.handlePointerMove),window.removeEventListener("pointerup",this.handlePointerUp),document.body.classList.remove("dragging")}handleWheel(t){const e=t.deltaY;this.value=this.value+e*-.0025,this.value=Math.max(0,Math.min(2,this.value)),this.dispatchEvent(new CustomEvent("input",{detail:this.value}))}describeArc(t,e,n,o,s){const a=t+s*Math.cos(n),l=e+s*Math.sin(n),c=t+s*Math.cos(o),d=e+s*Math.sin(o),u=o-n<=Math.PI?"0":"1";return`M ${a} ${l}A ${s} ${s} 0 ${u} 1 ${c} ${d}`}render(){const t=Math.PI*2*.75,e=-t/2-Math.PI/2,n=t/2-Math.PI/2,o=e+this.value/2*(n-e),s=_e({transform:`translate(40px, 40px) rotate(${o}rad)`});let a=this.value/2*(Jp-oo);a+=oo,a+=this.audioLevel*Wp;const l=_e({display:this.value>0?"block":"none",background:this.color,transform:`scale(${a})`});return T`
      <div id="halo" style=${l}></div>
      <!-- Static SVG elements -->
      ${this.renderStaticSvg()}
      <!-- SVG elements that move, separated to limit redraws -->
      <svg
        viewBox="0 0 80 80"
        @pointerdown=${this.handlePointerDown}
        @wheel=${this.handleWheel}>
        <g style=${s}>
          <circle cx="14" cy="0" r="2" fill="#000" />
        </g>
        <path
          d=${this.describeArc(40,40,e,n,34.5)}
          fill="none"
          stroke="#0003"
          stroke-width="3"
          stroke-linecap="round" />
        <path
          d=${this.describeArc(40,40,e,o,34.5)}
          fill="none"
          stroke="#fff"
          stroke-width="3"
          stroke-linecap="round" />
      </svg>
    `}renderStaticSvg(){return T`<svg viewBox="0 0 80 80">
        <ellipse
          opacity="0.4"
          cx="40"
          cy="40"
          rx="40"
          ry="40"
          fill="url(#f1)" />
        <g filter="url(#f2)">
          <ellipse cx="40" cy="40" rx="29" ry="29" fill="url(#f3)" />
        </g>
        <g filter="url(#f4)">
          <circle cx="40" cy="40" r="20.6667" fill="url(#f5)" />
        </g>
        <circle cx="40" cy="40" r="18" fill="url(#f6)" />
        <defs>
          <filter
            id="f2"
            x="8.33301"
            y="10.0488"
            width="63.333"
            height="64"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha" />
            <feOffset dy="2" />
            <feGaussianBlur stdDeviation="1.5" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
            <feBlend mode="normal" in2="BackgroundImageFix" result="shadow1" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="shadow1"
              result="shape" />
          </filter>
          <filter
            id="f4"
            x="11.333"
            y="19.0488"
            width="57.333"
            height="59.334"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha" />
            <feOffset dy="10" />
            <feGaussianBlur stdDeviation="4" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
            <feBlend mode="normal" in2="BackgroundImageFix" result="shadow1" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha" />
            <feMorphology
              radius="5"
              operator="erode"
              in="SourceAlpha"
              result="shadow2" />
            <feOffset dy="8" />
            <feGaussianBlur stdDeviation="3" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
            <feBlend mode="normal" in2="shadow1" result="shadow2" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="shadow2"
              result="shape" />
          </filter>
          <linearGradient
            id="f1"
            x1="40"
            y1="0"
            x2="40"
            y2="80"
            gradientUnits="userSpaceOnUse">
            <stop stop-opacity="0.5" />
            <stop offset="1" stop-color="white" stop-opacity="0.3" />
          </linearGradient>
          <radialGradient
            id="f3"
            cx="0"
            cy="0"
            r="1"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(40 40) rotate(90) scale(29 29)">
            <stop offset="0.6" stop-color="white" />
            <stop offset="1" stop-color="white" stop-opacity="0.7" />
          </radialGradient>
          <linearGradient
            id="f5"
            x1="40"
            y1="19.0488"
            x2="40"
            y2="60.3822"
            gradientUnits="userSpaceOnUse">
            <stop stop-color="white" />
            <stop offset="1" stop-color="#F2F2F2" />
          </linearGradient>
          <linearGradient
            id="f6"
            x1="40"
            y1="21.7148"
            x2="40"
            y2="57.7148"
            gradientUnits="userSpaceOnUse">
            <stop stop-color="#EBEBEB" />
            <stop offset="1" stop-color="white" />
          </linearGradient>
        </defs>
      </svg>`}};me.styles=ae`
    :host {
      cursor: grab;
      position: relative;
      width: 100%;
      aspect-ratio: 1;
      flex-shrink: 0;
      touch-action: none;
    }
    svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    #halo {
      position: absolute;
      z-index: -1;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      mix-blend-mode: lighten;
      transform: scale(2);
      will-change: transform;
    }
  `;Ke([L({type:Number})],me.prototype,"value",2);Ke([L({type:String})],me.prototype,"color",2);Ke([L({type:Number})],me.prototype,"audioLevel",2);me=Ke([te("weight-knob")],me);var Yp=Object.defineProperty,Kp=Object.getOwnPropertyDescriptor,K=(t,e,n,o)=>{for(var s=o>1?void 0:o?Kp(e,n):e,a=t.length-1,l;a>=0;a--)(l=t[a])&&(s=(o?l(e,n,s):l(s))||s);return o&&s&&Yp(e,n,s),s};const io=["#3498db","#2ecc71","#9b59b6","#f1c40f","#e67e22","#e74c3c","#1abc9c","#34495e","#ecf0f1","#7f8c8d","#f39c12","#d35400","#c0392b","#16a085","#27ae60","#2980b9","#8e44ad","#bdc3c7","#5dade2","#58d68d","#af7ac5","#f4d03f","#eb984e","#edbb99","#a3e4d7","#d2b4de","#f5b7b1"];let ro=class extends z{constructor(){super();const t=new bp({apiKey:"AIzaSyBrCqLrpjOIFgAZALQVKmabJFa-tRe6_B0",apiVersion:"v1alpha"}),e="models/lyria-realtime-exp",n=Op(),o=new Bp(t,e);this.pdjMidi=new R(n,t,pt,o),this.toastMessage=new he,o.setWeightedPrompts(new Map);const s=new qp(o.audioContext);o.extraDestination=s.node,this.pdjMidi.addEventListener("prompts-changed",l=>{const d=l.detail;o.setWeightedPrompts(d)}),this.pdjMidi.addEventListener("play-pause",()=>{o.playPause()}),this.pdjMidi.addEventListener("toggle-recording",()=>{o.isRecording?o.stopRecording():o.startRecording()}),o.addEventListener("recording-state-changed",l=>{const{isRecording:c}=l.detail;this.pdjMidi.isRecording=c}),o.addEventListener("recording-finished",l=>{const{blob:c,prompts:d}=l.detail;this.pdjMidi.setDownload(c,d)}),o.addEventListener("playback-state-changed",l=>{const d=l.detail;this.pdjMidi.playbackState=d,d==="playing"?s.start():s.stop()}),o.addEventListener("filtered-prompt",l=>{const d=l.detail;this.toastMessage.show(d.filteredReason),this.pdjMidi.addFilteredPrompt(d.text)});const a=l=>{const d=l.detail;this.toastMessage.show(d)};o.addEventListener("error",a),this.pdjMidi.addEventListener("error",a),s.addEventListener("audio-level-changed",l=>{const d=l.detail;this.pdjMidi.audioLevel=d})}render(){return T`${this.pdjMidi}${this.toastMessage}`}};ro=K([te("prompt-dj-midi-wrapper")],ro);const pt=[{text:"A wistful neo-soul ballad at 75 BPM with a warm Rhodes piano, a deep pocket bassline, and crisp, laid-back drums, featuring sparse, shimmering string pads.",category:"R&B / Soul Genre"},{text:"An aggressive cyberpunk track at 150 BPM with a distorted synthesizer bass, glitched-out drum machine, and pulsing, neon-drenched synth arpeggios.",category:"Electronic Genre"},{text:"A melancholic and introspective acoustic folk piece at 90 BPM, featuring a fingerpicked acoustic guitar, a mournful cello melody, and the gentle sound of falling rain.",category:"Folk Genre"},{text:"A tense and epic cinematic chase scene at 160 BPM, with driving staccato strings, thunderous taiko drums, and heroic brass stabs, building in intensity.",category:"Cinematic Genre"},{text:"A hypnotic and meditative drone piece featuring deep, resonant Mongolian throat singing, layered with slow, evolving atmospheric synth pads and no percussion.",category:"Mood"},{text:"Classic 90s boom bap hip hop beat with a dusty sample and hard-hitting drums",category:"Hip Hop Genre"},{text:"Modern trap beat at 140 BPM with deep 808s, fast hi-hat rolls, and a dark synth melody",category:"Hip Hop Genre"},{text:"Chill, introspective lo-fi hip hop for studying, with a gentle piano, vinyl crackle, and soft rainfall",category:"Hip Hop Genre"},{text:"Ethereal and dreamy cloud rap beat with washed-out synth pads and spaced-out vocal chops",category:"Hip Hop Genre"},{text:"Aggressive and distorted rage trap beat with heavy 808s and chaotic synth leads",category:"Hip Hop Genre"},{text:"Minimalist and bouncy Plugg music with simple, catchy synth melodies and a clean 808",category:"Hip Hop Genre"},{text:"Classic West Coast G-Funk with a whiny Moog synth lead and a funky bassline",category:"Hip Hop Genre"},{text:"Dark and menacing UK Drill beat with sliding 808s and complex, syncopated percussion",category:"Hip Hop Genre"},{text:"High-energy Jersey Club beat with a signature kick pattern and vocal chops",category:"Hip Hop Genre"},{text:"Distorted and aggressive Phonk drift house with a heavy cowbell melody and a saturated bass",category:"Hip Hop Genre"},{text:"Jazzy and conscious hip hop with an upright bass, smooth electric piano, and a laid-back beat, 90s style",category:"Hip Hop Genre"},{text:"Dark and gritty UK Grime beat at 140 BPM with square wave bass sounds and aggressive energy",category:"Hip Hop Genre"},{text:'Upbeat and bouncy New Orleans Bounce music with a "Triggerman" beat and call-and-response chants',category:"Hip Hop Genre"},{text:"Boom Bap drum loop with a crisp snare and a kick drum with punch",category:"Hip Hop Drums"},{text:"Hard-hitting trap drum pattern with rapid-fire hi-hat rolls and a snappy clap",category:"Hip Hop Drums"},{text:"Deep sub-rattling 808 bass pattern with a long decay, suitable for trap",category:"Hip Hop Bass"},{text:"Clean and punchy 808 bass, tuned to C",category:"Hip Hop Bass"},{text:"Gliding and sliding 808 bassline, characteristic of UK Drill music",category:"Hip Hop Bass"},{text:"A catchy, memorable cowbell melody loop in a Memphis rap style",category:"Hip Hop Melodic"},{text:"A chopped and rearranged soul vocal sample, creating a new, soulful melody",category:"Hip Hop Melodic"},{text:"Mellow and warm Rhodes electric piano chords, perfect for a chill hip hop track",category:"Hip Hop Keys"},{text:"A dark, melancholic piano melody with sparse notes, suitable for a trap beat",category:"Hip Hop Keys"},{text:"Atmospheric vinyl crackle and hiss from an old record player",category:"Hip Hop FX"},{text:"Classic turntable scratches and baby scratches performed by a DJ",category:"Hip Hop FX"},{text:"A reversed cymbal swell, used as a transition effect",category:"Hip Hop FX"},{text:`A set of modern rap ad-libs like "yeah", "uh", "let's go"`,category:"Vocal Textures"},{text:"Smoky late-night jazz trio with a walking upright bass, gentle brush drums, and a melancholic piano solo",category:"Jazz Genre"},{text:"Upbeat and energetic Bebop jazz ensemble featuring a rapid trumpet solo and complex rhythms",category:"Jazz Genre"},{text:"Funky 70s jazz fusion with a groovy electric piano, slap bass, and a tight drum break",category:"Jazz Genre"},{text:"Cool and relaxed jazz with a mellow tenor saxophone lead and soft piano comping",category:"Jazz Genre"},{text:"A classic walking bassline on an upright bass, providing a steady harmonic foundation",category:"Jazz Bass"},{text:"A virtuosic and melodic fretless electric bass solo in the style of Jaco Pastorius",category:"Jazz Bass"},{text:"A smooth and soulful saxophone solo with long, expressive notes",category:"Jazz Melodic"},{text:"A muted trumpet solo, creating a cool and intimate atmosphere in the style of Miles Davis",category:"Jazz Brass"},{text:"A lively, syncopated ragtime piano solo",category:"Jazz Keys"},{text:"Improvised scat vocal melody with nonsensical syllables",category:"Vocal Textures"},{text:"The sound of a drummer playing with brushes on a snare, creating a soft, shuffling rhythm",category:"Jazz Drums"},{text:"A swinging big band brass section with tight, powerful horn stabs",category:"Jazz Brass"},{text:"A warm and clean electric guitar melody in the style of Wes Montgomery, played with octaves",category:"Jazz Melodic"},{text:"Upbeat 70s funk band with a tight pocket groove, prominent slap bass, and a powerful horn section",category:"Funk Genre"},{text:"A percussive and groovy slap bass riff that drives the song",category:"Funk Bass"},{text:'A rhythmic, "wacka-wacka" wah-wah guitar riff, quintessential for funk',category:"Funk Guitar"},{text:'The iconic "Funky Drummer" breakbeat by Clyde Stubblefield',category:"Funk Drums"},{text:"Short, powerful brass section stabs that add excitement",category:"Funk Brass"},{text:"A percussive and rhythmic Clavinet melody in the style of Stevie Wonder",category:"Funk Keys"},{text:"A group of conga drums playing a syncopated, groovy rhythm",category:"Funk Percussion"},{text:"A squelchy and futuristic P-Funk synthesizer lead, in the style of Bernie Worrell",category:"Funk Synth"},{text:"A vocal melody processed through a talk box effect, making the voice sound like an instrument",category:"Vocal Textures"},{text:"A vocal grunt in the style of James Brown",category:"Vocal Textures"},{text:"A rainy day lo-fi beat with a gentle, muffled piano melody, soft tape hiss, and a dusty drum loop",category:"Lo-fi Genre"},{text:"Cozy and nostalgic lo-fi with a warbling electric piano, kalimba melody, and sounds of turning pages",category:"Lo-fi Genre"},{text:"The sound of gentle rain falling, perfect for creating a relaxing atmosphere",category:"Lo-fi FX"},{text:"The warm hiss and noise from an old cassette tape",category:"Lo-fi FX"},{text:"A synthesizer pad with a gentle, wavering pitch, creating a wobbly, nostalgic feel",category:"Lo-fi Synth"},{text:"A simple, relaxed electric guitar melody with a clean tone and a hint of reverb",category:"Lo-fi Melodic"},{text:"A sentimental upright piano melody with a soft, felted sound",category:"Lo-fi Keys"},{text:"The sound effect of a vinyl record stopping abruptly",category:"Lo-fi FX"},{text:"A gentle and charming kalimba melody",category:"Lo-fi Melodic"},{text:"Driving, hypnotic Berlin techno groove at 135 BPM with a rumbling kick and atmospheric pads",category:"Electronic Genre"},{text:"Classic Chicago house track with a soulful vocal sample, upright piano chords, and a 909 drum machine beat",category:"Electronic Genre"},{text:"Euphoric, uplifting trance anthem at 140 BPM with soaring supersaw chords, a driving bassline, and a gated vocal pad",category:"Electronic Genre"},{text:"High-energy drum and bass with a complex, chopped amen break, a deep reese bassline, and atmospheric jungle pads",category:"Electronic Genre"},{text:"Aggressive, heavy dubstep with a robotic wobble bass, syncopated drums, and jarring sound effects",category:"Electronic Genre"},{text:"Nostalgic 80s synthwave with retro gated reverb drums, a soaring synth lead, and a pulsating arpeggiated bassline",category:"Electronic Genre"},{text:"Ethereal, atmospheric ambient soundscape with evolving pads, no percussion, designed for deep listening",category:"Electronic Genre"},{text:"Glitchy and chaotic hyperpop with distorted 808s, sped-up vocal chops, and bright, bubbly synth melodies",category:"Pop Genre"},{text:"Hypnotic and rolling psytrance at 145 BPM with a galloping bassline and trippy, psychedelic synth effects",category:"Electronic Genre"},{text:"Minimal and groovy tech house with a punchy kick, a catchy bassline, and quirky percussion elements",category:"Electronic Genre"},{text:"Dark and brooding industrial techno with distorted textures, metallic percussion, and an EBM bassline",category:"Electronic Genre"},{text:"Relaxed and soulful trip-hop with a slow breakbeat, a moody bassline, and a sampled jazz piano",category:"Electronic Genre"},{text:"Old-school 8-bit chiptune video game music with simple square wave melodies and arpeggios",category:"Electronic Genre"},{text:"High-energy hardstyle with a distorted, pitched kick drum and a euphoric, anthemic synthesizer melody",category:"Electronic Genre"},{text:"Dreamy and introspective future garage with shuffled hi-hats, deep sub-bass, and pitched vocal samples",category:"Electronic Genre"},{text:"Futuristic and bass-heavy neurofunk with intricate drum patterns and complex, technical bass sound design",category:"Electronic Genre"},{text:"Sample-heavy French House with a filtered bassline and a four-on-the-floor beat, 90s style",category:"Electronic Genre"},{text:"Uplifting future bass with wide supersaw chords, pitch-bent vocal chops, and a complex rhythm",category:"Electronic Genre"},{text:"Aggressive Dutch Hardcore Gabber at 180 BPM with a heavily distorted 909 kick drum",category:"Electronic Genre"},{text:"Aesthetic and melancholic Vaporwave with slowed-down 80s samples, lush pads, and a feeling of nostalgia",category:"Electronic Genre"},{text:"Chaotic and frenetic Breakcore with rapidly spliced Amen breaks and distorted synth stabs",category:"Electronic Genre"},{text:"Dark and occult-themed Witch House with slow, heavy beats, droning synths, and pitched-down vocal samples",category:"Electronic Genre"},{text:"Deep and hypnotic ambient techno with spacious reverb, subtle textures, and a soft, continuous kick",category:"Electronic Genre"},{text:"A powerful, rumbling techno kick drum, hitting on every beat",category:"Electronic Drums"},{text:"The classic Amen Break, chopped and rearranged at a high tempo",category:"Electronic Drums"},{text:"A heavy, growling dubstep bass synth with complex modulation (FM, wavetable)",category:"Electronic Bass"},{text:"A deep and evolving Reese bassline, created by two detuned sawtooth waves, for Drum & Bass",category:"Electronic Bass"},{text:"An iconic, squelchy, and resonant acid bassline from a TB-303 synthesizer",category:"Electronic Bass"},{text:"Bright, layered supersaw chords, a staple of trance and future bass music",category:"Electronic Melodic"},{text:'A classic "hoover" synth sound, aggressive and detuned, from an Alpha Juno synthesizer',category:"Electronic Synth"},{text:"A gated synthesizer pad, creating a rhythmic, pulsating texture, classic 80s and trance sound",category:"Electronic Synth"},{text:"A rolling, off-beat psytrance bassline pattern",category:"Electronic Bass"},{text:"A distorted and punchy gabber kick drum at a very high tempo",category:"Electronic Drums"},{text:"Spacey and delayed dub techno with echoing chord stabs and a deep, subby kick drum",category:"Electronic Melodic"},{text:"IDM (Intelligent Dance Music) with complex, glitchy, and unpredictable rhythmic patterns",category:"Electronic Drums"},{text:"A bleeping and blooping modular synthesizer sequence with a random, generative feel",category:"Electronic Synth"},{text:"High-energy 90s alternative rock with fuzzy, distorted guitars, a driving bassline, and powerful drums",category:"Rock Genre"},{text:"Jangly and upbeat indie rock with clean, chorus-effected guitars and a simple, catchy melody",category:"Rock Genre"},{text:"Dark and atmospheric post-punk with a prominent, melodic bassline, angular guitars, and a robotic drum machine",category:"Rock Genre"},{text:"Swirling and ethereal shoegaze with layers of distorted, reverb-drenched guitars creating a wall of sound",category:"Rock Genre"},{text:"Fast, aggressive, and palm-muted thrash metal riff with double-bass drumming",category:"Metal Genre"},{text:"Brutal and guttural death metal with lightning-fast blast beat drums and low-tuned, heavily distorted guitars",category:"Metal Genre"},{text:'A polyrhythmic and rhythmically complex "djent" guitar riff with a heavily distorted, tight sound',category:"Metal Guitar"},{text:"A slow, heavy, and down-tuned sludge metal riff with a fuzzy, distorted bass guitar",category:"Metal Genre"},{text:"Fast and energetic pop-punk with simple power chords and an upbeat drum feel",category:"Rock Genre"},{text:"Classic 70s rock with a bluesy guitar riff, a Hammond organ, and a cowbell",category:"Rock Genre"},{text:"Slow, heavy, and fuzzy stoner rock with a hypnotic, repetitive guitar riff",category:"Rock Genre"},{text:"Raw, atmospheric black metal with high-pitched shrieking vocals and fast tremolo-picked guitars",category:"Metal Genre"},{text:"Progressive metal with complex time signatures, technical guitar solos, and intricate song structures",category:"Metal Genre"},{text:"A wall of sound effect created by multiple layers of fuzzy, distorted, and sustained guitars",category:"Rock Guitar"},{text:"A death metal blast beat with rapid-fire kick and snare drums",category:"Metal Drums"},{text:"Twangy surf rock guitar with heavy spring reverb",category:"Rock Guitar"},{text:"Gritty and raw Delta blues with a lone acoustic slide guitar and a soulful vocal",category:"Blues Genre"},{text:"Electrified Chicago blues band with a harmonica solo, driving rhythm section, and an electric guitar lead",category:"Blues Genre"},{text:"Modern country rock anthem with twangy telecaster guitars, a powerful female vocal, and a hard-hitting backbeat",category:"Country Genre"},{text:"Upbeat and fast-paced bluegrass with rapid banjo picking, fiddle melodies, and acoustic guitar strumming",category:"Folk Genre"},{text:"Traditional Appalachian folk music featuring a clawhammer banjo and a mournful fiddle",category:"Folk Genre"},{text:"Classic roots reggae with a one-drop drum beat, a deep bassline, and a rhythmic guitar skank",category:"World Genre"},{text:"Driving and energetic Afrobeat with a complex rhythm section, a horn section, and a funky guitar line",category:"World Genre"},{text:"A modern, percussive Amapiano track with a signature log drum bassline, shakers, and jazzy piano chords",category:"World Genre"},{text:"A romantic and smooth Bossa Nova with a gentle nylon string guitar, soft percussion, and a whispered vocal melody",category:"World Genre"},{text:"Energetic and festive salsa music with a syncopated piano montuno, powerful brass stabs, and a full Latin percussion section",category:"World Genre"},{text:"A passionate and rhythmic flamenco guitar performance with fast strums and percussive hits",category:"World Genre"},{text:"Lively Irish folk music with a fiddle, tin whistle, and a bodhrán drum",category:"World Genre"},{text:"Traditional Japanese music with a koto, shakuhachi flute, and taiko drums",category:"World Genre"},{text:"Mystical Middle Eastern music with an oud, swirling strings, and a darbuka hand drum rhythm",category:"World Genre"},{text:"A grand and colorful Bollywood film score with a dhol beat, sitar, and a large string section",category:"World Genre"},{text:'An iconic Reggaeton drum loop with the "dembow" rhythm',category:"World Drums"},{text:"The signature log drum bass sound from Amapiano music",category:"World Drums"},{text:"An intricate and meditative Sitar melody from Indian classical music",category:"World Melodic"},{text:"A rhythmic and percussive tabla drum pattern",category:"World Percussion"},{text:"A vibrant and festive mariachi brass section with trumpets and trombones",category:"World Brass"},{text:"A looping Baile Funk rhythm from Brazil, with a heavy kick drum and vocal samples",category:"World Drums"},{text:"A continuous, hypnotic drone from a didgeridoo",category:"World Melodic"},{text:"A bright and cheerful melody played on steel pans from Trinidad",category:"World Melodic"},{text:"A contemplative and serene melody on a Japanese Koto",category:"World Melodic"},{text:"A rhythmic and melodic line from an Arabic Oud",category:"World Melodic"},{text:"The sound of Scottish bagpipes playing a traditional march",category:"World Wind"},{text:"Polished, high-energy K-Pop production with a catchy chorus, layered vocals, and a hard-hitting beat drop",category:"Pop Genre"},{text:"Classic 70s disco with a four-on-the-floor beat, a funky bassline, lush string arrangements, and soulful vocals",category:"Pop Genre"},{text:"An epic, sweeping cinematic orchestra score with dramatic strings, powerful brass, and thunderous percussion",category:"Cinematic Genre"},{text:"A tense and suspenseful horror movie soundscape with dissonant strings, eerie sound effects, and sudden piano stabs",category:"Cinematic FX"},{text:"A sentimental and emotional film score piece featuring a solo piano melody with a soft string orchestra background",category:"Cinematic Keys"},{text:"Pulsating and tense staccato strings, perfect for an action movie sequence",category:"Cinematic Strings"},{text:"Thunderous and epic orchestral percussion, featuring taiko drums and timpani",category:"Cinematic Drums"},{text:"An orchestral swell, building from silence to a powerful crescendo",category:"Orchestral"},{text:"Heroic and triumphant cinematic theme with a bold brass fanfare and a soaring string melody",category:"Cinematic Genre"},{text:"Mysterious and magical fantasy film score with a celeste, harp glissandos, and a choir",category:"Cinematic Genre"},{text:"A clean, tight, and well-recorded acoustic drum kit, playing a simple rock beat",category:"Drums"},{text:"The sound of a classic LinnDrum machine, characteristic of 80s pop music",category:"Drums"},{text:"A punchy kick drum with a sharp attack and minimal decay",category:"Drums"},{text:"A fat, deep snare drum sound with a touch of reverb, reminiscent of 80s rock ballads",category:"Drums"},{text:"A syncopated conga rhythm played by a percussionist",category:"Percussion"},{text:"A tambourine playing a steady 8th-note pattern",category:"Percussion"},{text:"A rhythmic cowbell pattern, suitable for funk or Latin music",category:"Percussion"},{text:"The iconic gated reverb snare drum sound from the 1980s",category:"Drums"},{text:"Classic Roland TR-808 drum machine loop with a booming kick and tight snare",category:"Drums"},{text:"Punchy and danceable Roland TR-909 drum machine beat",category:"Drums"},{text:"A deep, clean sine wave sub-bass, providing a powerful low-end foundation",category:"Bass"},{text:"A classic analog synthesizer bass sound from a Moog synthesizer, warm and round",category:"Bass"},{text:"A funky and percussive slap bassline on an electric bass",category:"Bass"},{text:"An upright acoustic bass playing a walking jazz line",category:"Bass"},{text:"Warm, strummed acoustic guitar chords, perfect for a folk or pop song",category:"Guitar"},{text:"A fingerpicked acoustic guitar pattern, delicate and intricate",category:"Guitar"},{text:"A heavy metal guitar riff with high-gain distortion and palm-muting",category:"Guitar"},{text:"A clean electric guitar tone with chorus and reverb, ideal for indie pop or post-punk",category:"Guitar"},{text:"A soulful slide guitar melody with a bluesy feel",category:"Guitar"},{text:"A rich, expressive grand piano playing a classical melody",category:"Keys"},{text:"A mellow, warm Rhodes-style electric piano playing jazzy chords",category:"Keys"},{text:"A percussive and funky Wurlitzer electric piano riff",category:"Keys"},{text:"The powerful and majestic sound of a large church pipe organ",category:"Keys"},{text:"The ethereal and vintage sound of a Mellotron playing flute or string samples",category:"Keys"},{text:"A bright and tinkling celeste melody, like a music box",category:"Keys"},{text:"A classic honky-tonk upright piano, slightly out of tune for a western saloon feel",category:"Keys"},{text:"A soaring and heroic 80s-style synthesizer lead with a sawtooth wave",category:"Synth"},{text:"A lush, warm, and atmospheric analog synthesizer pad from a Juno-60",category:"Synth"},{text:"A bright, sparkling arpeggiated synth sequence, creating a sense of motion",category:"Synth"},{text:"A pluck synth melody with a short, percussive attack, common in electronic music",category:"Synth"},{text:"A gritty and evolving texture from a modular synthesizer",category:"Synth"},{text:"A shimmering, bell-like FM synthesis electric piano sound, like a Yamaha DX7",category:"Synth"},{text:"A complex, evolving pad made with wavetable synthesis",category:"Synth"},{text:"An atmospheric, abstract soundscape created with granular synthesis",category:"Synth"},{text:"A lush, emotional, and sweeping orchestral string section (violins, violas, cellos)",category:"Strings"},{text:"Short, sharp, and percussive pizzicato strings, playing a rhythmic pattern",category:"Strings"},{text:"A soaring and heroic French horn melody",category:"Brass"},{text:"A beautiful and expressive solo flute melody",category:"Wind"},{text:"A haunting and exotic shakuhachi flute solo from Japan",category:"Wind"},{text:"A majestic timpani roll, building tension",category:"Orchestral"},{text:"A dark and ominous cello drone",category:"Strings"},{text:"A shimmering and magical harp glissando",category:"Orchestral"},{text:"A haunting and beautiful Gregorian choir chant, sung by male voices",category:"Vocal Textures"},{text:"A deep, resonant Mongolian throat singing drone",category:"Vocal Textures"},{text:"A dramatic and powerful operatic soprano vocal swell",category:"Vocal Textures"},{text:"An upward-sweeping riser sound effect, building energy for a drop",category:"FX"},{text:"The sound of ocean waves crashing on a shore, a relaxing field recording",category:"FX"},{text:"A long, washing reverb tail, creating a huge sense of space",category:"FX"},{text:"A rhythmic, echoing ping-pong delay effect that bounces between stereo channels",category:"FX"},{text:'A pulsating sidechain compression effect, making pads "duck" in time with a kick drum',category:"Technique"},{text:"A dark and ominous mood, with low drones, dissonant strings, and a slow tempo",category:"Mood"},{text:"An energetic and euphoric feeling, with a fast tempo, uplifting synth chords, and a driving beat",category:"Mood"},{text:"A dreamy, ethereal, and atmospheric mood with floating pads, gentle arpeggios, and lots of reverb",category:"Mood"},{text:"A melancholic and introspective mood, with a slow piano melody, soft strings, and a sense of longing",category:"Mood"},{text:"A funky and groovy feeling with a tight rhythm section, syncopated bass, and wah-wah guitar",category:"Mood"},{text:"A minimalist and sparse arrangement, with only a few key elements and lots of empty space",category:"Style"},{text:"A vintage and retro style, using sounds and production techniques from the 1970s",category:"Style"},{text:"A futuristic and robotic style, with digital synths, glitchy effects, and a mechanical rhythm",category:"Style"},{text:"A peaceful and serene atmosphere with gentle sounds, slow melodies, and no harsh elements",category:"Mood"},{text:"An epic and triumphant mood, with a full orchestra, heroic brass fanfares, and powerful percussion",category:"Mood"},{text:"A mysterious and suspenseful atmosphere, perfect for a spy thriller, with tense strings and a subtle beat",category:"Mood"},{text:"A joyful, celebratory, and uplifting feeling with bright horns, happy melodies, and an energetic rhythm",category:"Mood"},{text:"An aggressive and confrontational mood with distorted sounds, a fast tempo, and a powerful, in-your-face beat",category:"Mood"},{text:"A hypnotic and meditative state, with repetitive patterns, drones, and a deep, steady pulse",category:"Mood"},{text:"A feeling of nostalgia and bittersweet memories, with warm, slightly detuned sounds and a slow, sentimental melody",category:"Mood"},{text:"Dark Brazilian Phonk at 145 BPM with a heavily distorted 808 bass, rapid-fire hi-hats, a menacing cowbell melody, and pitched-down Portuguese vocals.",category:"Brazilian Funk Genre"},{text:"High-energy Funk Rave from São Paulo at 150 BPM, with a hard 4/4 kick, syncopated rave synth stabs, and intense Portuguese hype vocals.",category:"Brazilian Funk Genre"},{text:"Chill and atmospheric Baile Funk at 120 BPM, with a laid-back Tamborzão beat, melodic synth pads, and reverb-drenched vocal chops.",category:"Brazilian Funk Genre"},{text:"Hypnotic Funk Mandelão beat at 130 BPM with a stripped-back, repetitive kick pattern, a single resonant synth stab, and looped acapella samples.",category:"Brazilian Funk Genre"},{text:"Gritty phonk drum loop with saturated kicks, roll-off snares, and crisp hi-hats at 140 BPM.",category:"Brazilian Funk Elements"},{text:"Pitched-down and chopped Portuguese male vocal sample, creating a dark, rhythmic texture.",category:"Brazilian Funk Elements"},{text:"Aggressive Brazilian Phonk at 150 BPM with a distorted cowbell melody, heavy sliding 808s, and Portuguese rave stabs",category:"Brazilian Funk Genre"},{text:"Classic Rio Baile Funk at 130 BPM featuring the Tamborzão drum loop, call-and-response vocals, and a simple synth lead",category:"Brazilian Funk Genre"},{text:"Modern Brazilian Funk Rave track with a hard-hitting 4/4 kick, acid synth lines, and energetic Portuguese hype vocals",category:"Brazilian Funk Genre"},{text:"Melodic and soulful Brazilian Funk with smooth electric piano chords, a groovy bassline, and clean, punchy drums, at 125 BPM",category:"Brazilian Funk Genre"},{text:"Minimalist and percussive Funk Mandelão with a repetitive kick pattern, sparse synth stabs, and rhythmic vocal samples",category:"Brazilian Funk Genre"},{text:"The classic Tamborzão Baile Funk drum rhythm, heavy and syncopated",category:"Brazilian Funk Elements"},{text:"A distorted and aggressive cowbell melody, typical of Brazilian Phonk",category:"Brazilian Funk Elements"},{text:"Energetic Portuguese call-and-response hype vocals for Baile Funk",category:"Brazilian Funk Elements"},{text:"A classic gunshot sound effect, used as a percussive hit in Baile Funk",category:"Brazilian Funk Elements"},{text:"A heavy, sliding 808 bassline with distortion, perfect for Brazilian Phonk",category:"Brazilian Funk Elements"}];function Op(){const t=new Map;for(let e=0;e<pt.length;e++){const n=`prompt-${e}`,o=pt[e];t.set(n,{promptId:n,text:o.text,weight:0,cc:e,color:io[e%io.length],category:o.category})}return t}const Ne=[{title:"AI Composition",description:"Real-time music and soundscape generation using state-of-the-art AI models, perfect for creating dynamic and adaptive audio experiences.",image:"https://images.unsplash.com/photo-1617957743097-0d20aa2ea762?q=80&w=2940&auto=format&fit=crop"},{title:"Audio Synthesis",description:"High-fidelity audio creation from text prompts and creative parameters, allowing for unparalleled control over sound design.",image:"https://images.unsplash.com/photo-1510915361894-db8b60106cb1?q=80&w=2940&auto=format&fit=crop"},{title:"Dimensional Analysis",description:"Forensic analysis of audio properties, structure, and emotional content, providing deep insights into any sound sample.",image:"https://images.unsplash.com/photo-1518709268805-4e9042af2176?q=80&w=2825&auto=format&fit=crop&ixlib=rb-4.0.3"},{title:"Reverse Engineering",description:"Deconstructing existing audio to understand its constituent parts, styles, and production techniques for inspiration or analysis.",image:"https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?q=80&w=2940&auto=format&fit=crop"},{title:"Inspection",description:"Detailed audio quality inspection for artifacts, compliance, and mastering standards, ensuring your audio is flawless.",image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2940&auto=format&fit=crop"},{title:"Creative Playground",description:"Interactive real-time environments for audio exploration and creation, offering a sandbox for boundless sonic creativity.",image:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3"}];let J=class extends z{constructor(){super(),this.route=window.location.hash||"#home",this.isPageTransitioning=!1,this.isMenuOpen=!1,this.observer=null,this.gl=null,this.glProgram=null,this.glTimeLocation=null,this.glResolutionLocation=null,this.glMouseLocation=null,this.mousePos={x:.5,y:.5},this.animationFrameId=null,this.updateCustomCursor=t=>{this.customCursor&&(this.customCursor.style.transform=`translate(${t.clientX}px, ${t.clientY}px)`)},this.handleCaseStudyCardMouseMove=t=>{const e=t,n=e.currentTarget,o=n.querySelector(".card-arrow");if(!o)return;const s=n.getBoundingClientRect(),a=e.clientX-s.left,l=e.clientY-s.top,c=o.getBoundingClientRect(),d=c.left-s.left+c.width/2,u=c.top-s.top+c.height/2,f=a-d,p=l-u,h=Math.sqrt(f*f+p*p),m=200;if(h<m){const g=(m-h)/m;o.style.transform=`translate(${f*-.4*g}px, ${p*-.4*g}px) scale(1)`,o.style.transition="transform 0.1s ease-out"}else o.style.transform="translate(0,0) scale(1)"},this.handleCaseStudyCardMouseLeave=t=>{const n=t.currentTarget.querySelector(".card-arrow");n&&(n.style.transform="translate(0,0) scale(1)",n.style.transition="transform 0.3s ease")},this.updateMousePos=t=>{if(this.heroCanvas){const e=this.heroCanvas.getBoundingClientRect();this.mousePos={x:t.clientX-e.left,y:e.height-(t.clientY-e.top)}}},this.animateGL=(t=0)=>{if(!this.gl||!this.glProgram||!this.heroCanvas)return;const e=this.gl;this.heroCanvas.width=this.heroCanvas.clientWidth,this.heroCanvas.height=this.heroCanvas.clientHeight,e.viewport(0,0,e.canvas.width,e.canvas.height),e.uniform1f(this.glTimeLocation,t*2e-4),e.uniform2f(this.glResolutionLocation,e.canvas.width,e.canvas.height),e.uniform2f(this.glMouseLocation,this.mousePos.x,this.mousePos.y),e.drawArrays(e.TRIANGLE_STRIP,0,4),this.animationFrameId=requestAnimationFrame(this.animateGL)},this.updateBodyClass(),window.addEventListener("hashchange",()=>{this.isMenuOpen=!1,this.route!==(window.location.hash||"#home")&&(this.isPageTransitioning=!0,setTimeout(()=>{this.route=window.location.hash||"#home",this.isPageTransitioning=!1,this.updateBodyClass(),this.route==="#home"?Promise.resolve().then(()=>{this.initializeObserver(),this.initializeInteractiveEffects(),this.initializeServiceHoverEffect(),this.initWebGL()}):this.cleanupWebGL()},500))})}updateBodyClass(){this.route==="#playground"?document.body.classList.add("playground-page"):document.body.classList.remove("playground-page")}firstUpdated(){this.route==="#home"&&(this.initializeObserver(),this.initializeInteractiveEffects(),this.initializeServiceHoverEffect(),this.initWebGL()),this.initCustomCursor()}disconnectedCallback(){var t;super.disconnectedCallback(),(t=this.observer)==null||t.disconnect(),this.cleanupWebGL(),window.removeEventListener("mousemove",this.updateMousePos),window.removeEventListener("mousemove",this.updateCustomCursor)}initCustomCursor(){window.addEventListener("mousemove",this.updateCustomCursor),setTimeout(()=>{var t;(t=this.shadowRoot)==null||t.querySelectorAll("a, button, .case-study-card").forEach(e=>{e.addEventListener("mouseenter",()=>{var n;return(n=this.customCursor)==null?void 0:n.classList.add("hover")}),e.addEventListener("mouseleave",()=>{var n;return(n=this.customCursor)==null?void 0:n.classList.remove("hover")})})},100)}initializeObserver(){var n;const t={root:null,rootMargin:"0px",threshold:.1};this.observer=new IntersectionObserver((o,s)=>{o.forEach(a=>{a.isIntersecting&&(a.target.classList.add("is-visible"),s.unobserve(a.target))})},t);const e=(n=this.shadowRoot)==null?void 0:n.querySelectorAll(".animated-section");e==null||e.forEach(o=>{var s;(s=this.observer)==null||s.observe(o)})}initializeInteractiveEffects(){var t;(t=this.shadowRoot)==null||t.querySelectorAll(".case-study-card").forEach(e=>{e.addEventListener("mousemove",this.handleCaseStudyCardMouseMove),e.addEventListener("mouseleave",this.handleCaseStudyCardMouseLeave)})}initializeServiceHoverEffect(){if(this.servicesListItems.length===0)return;const t=e=>{this.servicesListItems.forEach((n,o)=>n.classList.toggle("active",o===e)),this.servicesDescriptions.forEach((n,o)=>n.classList.toggle("active",o===e)),this.servicesImages.forEach((n,o)=>n.classList.toggle("active",o===e))};this.servicesListItems.forEach((e,n)=>{e.addEventListener("mouseover",()=>t(n)),e.addEventListener("click",()=>t(n))}),t(0)}initWebGL(){if(!this.heroCanvas)return;if(this.gl=this.heroCanvas.getContext("webgl"),!this.gl){console.error("WebGL not supported!");return}const t=this.gl,e=`
          attribute vec2 a_position;
          void main() {
              gl_Position = vec4(a_position, 0.0, 1.0);
          }
      `,n=`
          precision highp float;
          uniform vec2 u_resolution;
          uniform float u_time;
          uniform vec2 u_mouse;

          float random (in vec2 st) {
              return fract(sin(dot(st.xy, vec2(12.9898,78.233))) * 43758.5453123);
          }

          float noise (in vec2 st) {
              vec2 i = floor(st);
              vec2 f = fract(st);
              float a = random(i);
              float b = random(i + vec2(1.0, 0.0));
              float c = random(i + vec2(0.0, 1.0));
              float d = random(i + vec2(1.0, 1.0));
              vec2 u = f*f*(3.0-2.0*f);
              return mix(a, b, u.x) + (c - a)* u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
          }

          #define NUM_OCTAVES 5
          float fbm ( in vec2 st) {
              float v = 0.0;
              float a = 0.5;
              vec2 shift = vec2(100.0);
              mat2 rot = mat2(cos(0.5), sin(0.5), -sin(0.5), cos(0.50));
              for (int i = 0; i < NUM_OCTAVES; ++i) {
                  v += a * noise(st);
                  st = rot * st * 2.0 + shift;
                  a *= 0.5;
              }
              return v;
          }

          void main() {
              vec2 st = gl_FragCoord.xy/u_resolution.xy;
              st.x *= u_resolution.x/u_resolution.y;

              vec2 mouse_normalized = u_mouse / u_resolution;
              float dist_to_mouse = distance(st, mouse_normalized * vec2(u_resolution.x/u_resolution.y, 1.0));

              vec3 color = vec3(0.0);

              vec2 q = vec2(0.);
              q.x = fbm( st + 0.00*u_time );
              q.y = fbm( st + vec2(1.0) );

              vec2 r = vec2(0.);
              r.x = fbm( st + 1.0*q + vec2(1.7,9.2)+ 0.15*u_time );
              r.y = fbm( st + 1.0*q + vec2(8.3,2.8)+ 0.126*u_time);

              float f = fbm(st+r);

              color = mix(vec3(0.06, 0.10, 0.12), // Dark blue/black
                          vec3(0.1, 0.3, 0.4),  // Teal/blue
                          clamp((f*f)*4.0,0.0,1.0));

              color = mix(color,
                          vec3(0.99, 0.93, 0.40), // Yellow accent
                          clamp(length(q),0.0,1.0));

              color = mix(color,
                          vec3(0.8, 0.9, 1.0), // White highlights
                          clamp(length(r.x),0.0,1.0));

              color = (f*f*f+.6*f*f+.5*f)*color;

              // Add mouse interaction
              float mouse_effect = 1.0 - smoothstep(0.0, 0.2, dist_to_mouse);
              color += mouse_effect * 0.2;

              gl_FragColor = vec4(color, 1.0);
          }
      `,o=this.compileShader(e,t.VERTEX_SHADER),s=this.compileShader(n,t.FRAGMENT_SHADER);if(this.glProgram=t.createProgram(),!this.glProgram||!o||!s)return;t.attachShader(this.glProgram,o),t.attachShader(this.glProgram,s),t.linkProgram(this.glProgram),t.useProgram(this.glProgram);const a=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,a),t.bufferData(t.ARRAY_BUFFER,new Float32Array([-1,-1,1,-1,-1,1,1,1]),t.STATIC_DRAW);const l=t.getAttribLocation(this.glProgram,"a_position");t.enableVertexAttribArray(l),t.vertexAttribPointer(l,2,t.FLOAT,!1,0,0),this.glTimeLocation=t.getUniformLocation(this.glProgram,"u_time"),this.glResolutionLocation=t.getUniformLocation(this.glProgram,"u_resolution"),this.glMouseLocation=t.getUniformLocation(this.glProgram,"u_mouse"),window.addEventListener("mousemove",this.updateMousePos),this.animateGL()}compileShader(t,e){if(!this.gl)return null;const n=this.gl.createShader(e);return n?(this.gl.shaderSource(n,t),this.gl.compileShader(n),this.gl.getShaderParameter(n,this.gl.COMPILE_STATUS)?n:(console.error("Shader compile error:",this.gl.getShaderInfoLog(n)),null)):null}cleanupWebGL(){this.animationFrameId&&cancelAnimationFrame(this.animationFrameId),this.gl=null,this.glProgram=null,window.removeEventListener("mousemove",this.updateMousePos)}renderPage(){const t=Y({"page-wrapper":!0,"page-exit":this.isPageTransitioning});return T`
    <div class=${t}>
        ${(()=>{switch(this.route){case"#playground":return T`<prompt-dj-midi-wrapper></prompt-dj-midi-wrapper>`;case"#about":return setTimeout(()=>{this.initializeObserver(),this.initializeInteractiveEffects()},100),this.renderAboutPage();case"#services":return setTimeout(()=>{this.initializeObserver(),this.initializeInteractiveEffects()},100),this.renderServicesPage();case"#contact":return setTimeout(()=>{this.initializeObserver(),this.initializeInteractiveEffects()},100),this.renderContactPage();case"#home":default:return Promise.resolve().then(()=>{this.initializeObserver(),this.initializeInteractiveEffects(),this.initializeServiceHoverEffect(),this.initWebGL()}),this.renderHomePage()}})()}
    </div>`}render(){const t=Y({"page-content":!0,"menu-open":this.isMenuOpen});return T`
      <div id="custom-cursor"></div>
      ${this.renderHeader()}
      ${this.isMenuOpen?this.renderMobileMenu():""}
      <main class=${t}>
        ${this.renderPage()}
      </main>
      ${this.route!=="#playground"?this.renderFooter():""}
    `}toggleMenu(){this.isMenuOpen=!this.isMenuOpen}renderMobileMenu(){return T`
      <nav class="mobile-nav">
        <a href="#home" class="mobile-nav-link">Home</a>
        <a href="#playground" class="mobile-nav-link">Playground</a>
        <a href="#about" class="mobile-nav-link">About Us</a>
        <a href="#services" class="mobile-nav-link">Services</a>
        <a href="#contact" class="mobile-nav-link">Contact</a>
      </nav>
    `}renderHeader(){if(this.route==="#playground")return T``;const t=Y({"site-header":!0,"menu-open":this.isMenuOpen});return T`
      <header class=${t}>
        <a href="#home" class="site-logo" aria-label="PixelVane Home">
            <svg width="40" height="40" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M30 85V15H60C73.8071 15 85 26.1929 85 40C85 53.8071 73.8071 65 60 65H45V85H30Z" fill="white"/>
                <path d="M45 65L70 40L45 15V35L60 40L45 45V65Z" fill="#061a1f"/>
            </svg>
        </a>
        <nav class="header-nav">
          <a href="#playground" class="nav-button login-button">
            Playground
          </a>
          <button @click=${this.toggleMenu} class="nav-button menu-button" aria-label=${this.isMenuOpen?"Close Menu":"Open Menu"}>
            <div class="menu-icon-bars">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
          </button>
        </nav>
      </header>
    `}renderHomePage(){const t=()=>{var o,s;(s=(o=this.shadowRoot)==null?void 0:o.querySelector(".content-section"))==null||s.scrollIntoView({behavior:"smooth"})},e=["SEM","ANDREWS SURVEY","caley","DEEP","FAST","NVIDIA","ORACLE","G-CLOUD"],n=[{image:"https://images.unsplash.com/photo-1511379938547-c1f69419868d?q=80&w=2070&auto=format&fit=crop",category:"Film Scoring",title:"Generative Soundtrack for Indie Sci-Fi Film 'Echoes of Andromeda'"},{image:"https://images.unsplash.com/photo-1516223725307-6f76b9ec8742?q=80&w=2072&auto=format&fit=crop",category:"Live Performance",title:"Interactive Audio-Visual Installation at Digital Arts Festival"},{image:"https://images.unsplash.com/photo-1497366216548-37526070297c?q=80&w=2069&auto=format&fit=crop&ixlib=rb-4.0.3",category:"Brand Identity",title:"Generative Audio Environments for Corporate Soundscaping"}];return T`
      <section class="hero-section">
        <canvas id="hero-canvas"></canvas>
        <div class="hero-content">
            <h1>Specialists in AI music, audio generation and creative analysis.</h1>
        </div>
        <div class="hero-footer">
            <div class="hero-footer-left">
                <p>Unparalleled precision and<br>forensically accurate reports.</p>
            </div>
            <div class="hero-footer-right">
                <button @click=${t} class="explore-button">
                    Explore
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 5v14m-7-7l7 7 7-7"/></svg>
                </button>
            </div>
        </div>
      </section>

      <section class="content-section why-us-section animated-section">
        <div class="why-us-background"></div>
        <div class="section-container-wide why-us-content">
            <div class="section-label">Why PixelVane?</div>
            <h2 class="section-heading">We are pioneers in the digital soundscape, transforming abstract concepts into forensically accurate generative audio.</h2>
            <div class="why-us-description">
                <p>We are full-service providers of generative audio and creative analysis. Our expertise in high-accuracy audio synthesis and dimensional reporting allows us to serve mission-critical industries where precision is paramount.</p>
                <p>Our capabilities include using your prompts, our proprietary models, or fine-tuned systems to perform uncompromised, sub-millisecond audio generation, as well as 'quick-pass' high-level data collection for larger sites.</p>
            </div>
            <a href="#" class="section-button">About Us
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
            </a>
        </div>
      </section>

      <section class="partners-section animated-section">
          <div class="partners-scroller">
            <div class="partners-track">
                ${e.map(o=>T`<div class="partner-logo">${o}</div>`)}
                ${e.map(o=>T`<div class="partner-logo">${o}</div>`)}
            </div>
          </div>
      </section>

      <section class="content-section services-section-reimagined animated-section">
        <div class="services-container">
            <div class="section-label">What we do</div>
            <h2 class="section-heading">Our core services cover the full project lifecycle, from data capture and processing, to analysis and visualisation.</h2>
            <div class="services-interactive-wrapper">
                <ul class="services-list">
                    ${Ne.map(o=>T`<li><h3>${o.title}</h3></li>`)}
                </ul>
                <div class="services-visuals">
                    <div class="service-description-panel">
                        ${Ne.map(o=>T`<p>${o.description}</p>`)}
                    </div>
                    <div class="service-image-panel">
                        ${Ne.map(o=>T`
                            <div class="service-image" style="background-image: url(${o.image})"></div>
                        `)}
                    </div>
                </div>
            </div>
            <a href="#" class="section-button">View all services
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
            </a>
        </div>
      </section>

      <section class="software-section animated-section">
        <div class="software-content">
            <div class="section-label">Software</div>
            <h2 class="section-heading">Powerful tools to give you full control of the creative process.</h2>
            <p class="section-description">Our proprietary software provides an intuitive interface for interacting with generative models, enabling precise control and real-time feedback.</p>
            <a href="#" class="section-button">Learn more
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
            </a>
        </div>
      </section>

      <section class="content-section card-section animated-section">
        <div class="section-container-wide">
            <div class="card-section-header">
                <div>
                    <div class="section-label">Case Studies</div>
                    <h2 class="section-heading">Explore our recent work.</h2>
                </div>
                <a href="#" class="section-button">View all
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
                </a>
            </div>
            <div class="case-studies-grid">
                ${n.map(o=>T`
                    <a href="#" class="case-study-card">
                        <div class="card-background" style="background-image: url(${o.image})"></div>
                        <div class="card-content">
                            <div>
                                <span class="card-category">${o.category}</span>
                                <h3 class="card-title">${o.title}</h3>
                            </div>
                            <div class="card-arrow">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
                            </div>
                        </div>
                    </a>
                `)}
            </div>
        </div>
      </section>

      <section class="content-section cta-section animated-section">
        <div class="section-container">
          <h2 class="section-heading">Let's create the future of audio together.</h2>
          <a href="#contact" class="section-button">Contact Us
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
          </a>
        </div>
      </section>
    `}renderAboutPage(){return T`
      <section class="page-hero">
        <div class="page-hero-content">
          <h1>About PixelVane</h1>
          <p>Pioneering the future of AI-powered music and audio generation with cutting-edge technology and creative innovation.</p>
        </div>
      </section>

      <section class="content-section about-content animated-section">
        <div class="section-container">
          <div class="about-grid">
            <div class="about-text">
              <div class="section-label">Our Story</div>
              <h2>Transforming Audio Through AI Innovation</h2>
              <p>Founded with a vision to revolutionize the audio industry, PixelVane combines state-of-the-art artificial intelligence with deep musical understanding. Our team of audio engineers, AI researchers, and creative professionals work together to push the boundaries of what's possible in music and sound generation.</p>
              <p>We believe that AI should enhance human creativity, not replace it. Our tools are designed to inspire artists, producers, and creators by providing them with unprecedented control over audio generation and analysis.</p>
            </div>
            <div class="about-image">
              <div class="image-placeholder">
                <svg width="100" height="100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                  <path d="M9 18V5l12-2v13"></path>
                  <circle cx="6" cy="18" r="3"></circle>
                  <circle cx="18" cy="16" r="3"></circle>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section class="content-section team-section animated-section">
        <div class="section-container">
          <div class="section-label">Our Team</div>
          <h2 class="section-heading">Meet the Innovators</h2>
          <div class="team-grid">
            <div class="team-member">
              <div class="member-avatar">
                <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <h3>Dr. Sarah Chen</h3>
              <p class="member-role">Chief Technology Officer</p>
              <p>Leading AI researcher with 15+ years in machine learning and audio processing.</p>
            </div>
            <div class="team-member">
              <div class="member-avatar">
                <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <h3>Marcus Rodriguez</h3>
              <p class="member-role">Head of Audio Engineering</p>
              <p>Grammy-nominated producer and audio engineer with expertise in generative music systems.</p>
            </div>
            <div class="team-member">
              <div class="member-avatar">
                <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <h3>Elena Kowalski</h3>
              <p class="member-role">Creative Director</p>
              <p>Visionary artist bridging the gap between technology and human expression.</p>
            </div>
          </div>
        </div>
      </section>

      <section class="content-section values-section animated-section">
        <div class="section-container">
          <div class="section-label">Our Values</div>
          <h2 class="section-heading">What Drives Us</h2>
          <div class="values-grid">
            <div class="value-item">
              <div class="value-icon">🎵</div>
              <h3>Innovation</h3>
              <p>Constantly pushing the boundaries of what's possible in AI-powered audio generation.</p>
            </div>
            <div class="value-item">
              <div class="value-icon">🤝</div>
              <h3>Collaboration</h3>
              <p>Working closely with artists and creators to understand their needs and challenges.</p>
            </div>
            <div class="value-item">
              <div class="value-icon">🎯</div>
              <h3>Precision</h3>
              <p>Delivering forensically accurate analysis and high-fidelity audio generation.</p>
            </div>
            <div class="value-item">
              <div class="value-icon">🌟</div>
              <h3>Creativity</h3>
              <p>Empowering human creativity through intelligent AI tools and interfaces.</p>
            </div>
          </div>
        </div>
      </section>
    `}renderServicesPage(){return T`
      <section class="page-hero">
        <div class="page-hero-content">
          <h1>Our Services</h1>
          <p>Comprehensive AI-powered audio solutions for every stage of your creative journey.</p>
        </div>
      </section>

      <section class="content-section services-detail animated-section">
        <div class="section-container">
          <div class="services-detail-grid">
            ${Ne.map(t=>T`
              <div class="service-detail-card">
                <div class="service-detail-image" style="background-image: url(${t.image})"></div>
                <div class="service-detail-content">
                  <h3>${t.title}</h3>
                  <p>${t.description}</p>
                  <div class="service-features">
                    <h4>Key Features:</h4>
                    <ul>
                      ${t.title==="AI Composition"?T`
                        <li>Real-time music generation</li>
                        <li>Style-aware composition</li>
                        <li>Multi-instrument orchestration</li>
                        <li>Adaptive tempo and mood</li>
                      `:""}
                      ${t.title==="Audio Synthesis"?T`
                        <li>Text-to-audio generation</li>
                        <li>High-fidelity output</li>
                        <li>Custom parameter control</li>
                        <li>Multiple format support</li>
                      `:""}
                      ${t.title==="Dimensional Analysis"?T`
                        <li>Spectral analysis</li>
                        <li>Emotional content mapping</li>
                        <li>Structural decomposition</li>
                        <li>Forensic-grade accuracy</li>
                      `:""}
                      ${t.title==="Reverse Engineering"?T`
                        <li>Style extraction</li>
                        <li>Production technique analysis</li>
                        <li>Instrument separation</li>
                        <li>Composition breakdown</li>
                      `:""}
                      ${t.title==="Inspection"?T`
                        <li>Quality assessment</li>
                        <li>Artifact detection</li>
                        <li>Compliance checking</li>
                        <li>Mastering analysis</li>
                      `:""}
                      ${t.title==="Creative Playground"?T`
                        <li>Interactive environments</li>
                        <li>Real-time experimentation</li>
                        <li>Collaborative tools</li>
                        <li>Export capabilities</li>
                      `:""}
                    </ul>
                  </div>
                  <a href="#contact" class="service-cta">Get Started</a>
                </div>
              </div>
            `)}
          </div>
        </div>
      </section>

      <section class="content-section pricing-coming-soon animated-section">
        <div class="section-container">
          <div class="coming-soon-content">
            <div class="coming-soon-icon">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12,6 12,12 16,14"></polyline>
              </svg>
            </div>
            <div class="section-label">Pricing</div>
            <h2 class="section-heading">Coming Soon</h2>
            <p class="coming-soon-description">
              We're crafting the perfect pricing plans for our AI-powered audio services.
              Until then, enjoy <strong>free access</strong> to explore all our features and capabilities.
            </p>
            <div class="coming-soon-features">
              <div class="feature-highlight">
                <div class="feature-icon">🎵</div>
                <div class="feature-text">
                  <h4>Free Access</h4>
                  <p>Full access to all services during our preview period</p>
                </div>
              </div>
              <div class="feature-highlight">
                <div class="feature-icon">⚡</div>
                <div class="feature-text">
                  <h4>No Limits</h4>
                  <p>Unlimited usage while we finalize our pricing structure</p>
                </div>
              </div>
              <div class="feature-highlight">
                <div class="feature-icon">🚀</div>
                <div class="feature-text">
                  <h4>Early Access</h4>
                  <p>Be among the first to experience the future of AI audio</p>
                </div>
              </div>
            </div>
            <div class="coming-soon-cta">
              <a href="#playground" class="section-button primary">Try It Free Now</a>
              <a href="#contact" class="section-button secondary">Get Notified</a>
            </div>
          </div>
        </div>
      </section>
    `}renderContactPage(){return T`
      <section class="page-hero">
        <div class="page-hero-content">
          <h1>Contact Us</h1>
          <p>Ready to transform your audio projects? Let's discuss how PixelVane can help you achieve your creative goals.</p>
        </div>
      </section>

      <section class="content-section contact-content animated-section">
        <div class="section-container">
          <div class="contact-grid">
            <div class="contact-info">
              <div class="section-label">Get in Touch</div>
              <h2>Let's Create Something Amazing</h2>
              <p>Whether you're looking to integrate AI audio generation into your workflow, need custom solutions, or want to explore our services, we're here to help.</p>

              <div class="contact-methods">
                <div class="contact-method">
                  <div class="contact-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                  </div>
                  <div>
                    <h4>Email</h4>
                    <p><EMAIL></p>
                  </div>
                </div>

                <div class="contact-method">
                  <div class="contact-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                    </svg>
                  </div>
                  <div>
                    <h4>Phone</h4>
                    <p>+****************</p>
                  </div>
                </div>


              </div>
            </div>

            <div class="contact-form">
              <form class="contact-form-container">
                <div class="form-group">
                  <label for="name">Name *</label>
                  <input type="text" id="name" name="name" required>
                </div>

                <div class="form-group">
                  <label for="email">Email *</label>
                  <input type="email" id="email" name="email" required>
                </div>

                <div class="form-group">
                  <label for="company">Company</label>
                  <input type="text" id="company" name="company">
                </div>

                <div class="form-group">
                  <label for="service">Service Interest</label>
                  <select id="service" name="service">
                    <option value="">Select a service</option>
                    <option value="ai-composition">AI Composition</option>
                    <option value="audio-synthesis">Audio Synthesis</option>
                    <option value="dimensional-analysis">Dimensional Analysis</option>
                    <option value="reverse-engineering">Reverse Engineering</option>
                    <option value="inspection">Inspection</option>
                    <option value="creative-playground">Creative Playground</option>
                    <option value="custom">Custom Solution</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="message">Message *</label>
                  <textarea id="message" name="message" rows="5" required placeholder="Tell us about your project and how we can help..."></textarea>
                </div>

                <button type="submit" class="form-submit">Send Message</button>
              </form>
            </div>
          </div>
        </div>
      </section>

      <section class="content-section faq-section animated-section">
        <div class="section-container">
          <div class="section-label">FAQ</div>
          <h2 class="section-heading">Frequently Asked Questions</h2>
          <div class="faq-grid">
            <div class="faq-item">
              <h4>How quickly can I get started?</h4>
              <p>Most clients can begin using our services within 24-48 hours of initial contact. Enterprise solutions may require additional setup time.</p>
            </div>
            <div class="faq-item">
              <h4>Do you offer custom integrations?</h4>
              <p>Yes, we provide custom API integrations and can work with your existing workflow and tools.</p>
            </div>
            <div class="faq-item">
              <h4>What file formats do you support?</h4>
              <p>We support all major audio formats including WAV, MP3, FLAC, AIFF, and more. Custom formats can be accommodated.</p>
            </div>
            <div class="faq-item">
              <h4>Is there a free trial available?</h4>
              <p>Yes, we offer a 14-day free trial for new customers to explore our services and capabilities.</p>
            </div>
          </div>
        </div>
      </section>
    `}renderFooter(){return T`
        <footer class="site-footer-bottom">
            <div class="footer-container">
                <div class="footer-column about-column">
                    <a href="#home" class="footer-logo" aria-label="PixelVane Home">
                        <svg width="40" height="40" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M30 85V15H60C73.8071 15 85 26.1929 85 40C85 53.8071 73.8071 65 60 65H45V85H30Z" fill="white"/>
                            <path d="M45 65L70 40L45 15V35L60 40L45 45V65Z" fill="#061a1f"/>
                        </svg>
                    </a>
                    <p>Pioneering the future of generative audio and creative analysis.</p>
                </div>
                <div class="footer-column links-column">
                    <h4>Sectors</h4>
                    <ul>
                        <li><a href="#">Energy</a></li>
                        <li><a href="#">Film & Media</a></li>
                        <li><a href="#">Gaming</a></li>
                        <li><a href="#">Art Installations</a></li>
                    </ul>
                </div>
                <div class="footer-column links-column">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="#services">AI Composition</a></li>
                        <li><a href="#services">Audio Synthesis</a></li>
                        <li><a href="#services">Analysis</a></li>
                        <li><a href="#services">Inspection</a></li>
                    </ul>
                </div>
                <div class="footer-column links-column">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="#about">About Us</a></li>
                        <li><a href="#about">News</a></li>
                        <li><a href="#about">Careers</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom-bar">
                <div class="copyright">© 2025 PixelVane Ltd. All rights reserved.</div>
                <div class="social-links">
                    <a href="https://x.com/Epicfusion_" target="_blank" rel="noopener noreferrer" aria-label="Follow us on X"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24h-6.617l-5.21-6.817-6.022 6.817h-3.308l7.732-8.818-8.318-10.68h6.723l4.584 6.222 5.274-6.222z"/></svg></a>
                </div>
            </div>
        </footer>
      `}};J.styles=ae`
    :host {
      display: block;
      background-color: var(--color-background);
      color: var(--color-text);
      --cursor-size: 10px;
      --cursor-scale: 2.5;
    }

    @media (hover: hover) and (pointer: fine) {
      #custom-cursor {
          position: fixed;
          top: 0;
          left: 0;
          width: var(--cursor-size);
          height: var(--cursor-size);
          background-color: var(--color-accent);
          border-radius: 50%;
          pointer-events: none;
          z-index: 9999;
          mix-blend-mode: difference;
          transition: transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                      width 0.3s ease, height 0.3s ease;
          transform-origin: center center;
      }

      #custom-cursor.hover {
          width: calc(var(--cursor-size) * var(--cursor-scale));
          height: calc(var(--cursor-size) * var(--cursor-scale));
      }
    }


    .page-content {
      display: flex;
      flex-direction: column;
      transition: transform 0.5s cubic-bezier(0.22, 1, 0.36, 1);
    }
    .page-content.menu-open {
        transform: scale(0.9);
        filter: blur(4px);
        pointer-events: none;
        user-select: none;
    }

    .page-wrapper {
        transition: opacity 0.5s ease-in-out;
    }
    .page-wrapper.page-exit {
        opacity: 0;
    }

    /* Animation */
    .animated-section {
        opacity: 0;
        transform: translateY(30px);
        transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        will-change: opacity, transform;
    }

    .animated-section.is-visible {
        opacity: 1;
        transform: translateY(0);
    }


    /* Header */
    .site-header {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      padding: 1.5rem 2rem;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 100;
      transition: color 0.3s ease;
    }
    .site-header.menu-open .site-logo path {
        fill: white;
    }
    .site-header.menu-open .menu-icon-bars .bar {
        background-color: white;
    }
    /* Invert colors for open menu */
    .site-header.menu-open .menu-icon-bars .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }
    .site-header.menu-open .menu-icon-bars .bar:nth-child(2) {
        opacity: 0;
    }
    .site-header.menu-open .menu-icon-bars .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .site-logo svg {
      transition: transform 0.3s ease;
    }
    .site-logo:hover svg {
      transform: rotate(5deg) scale(1.05);
    }

    .header-nav {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .nav-button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.65rem 1.25rem;
      border: 1px solid var(--color-border);
      border-radius: 5rem;
      background: transparent;
      color: white;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.2s ease-in-out;
      text-decoration: none;
    }

    .login-button {
      backdrop-filter: blur(5px);
      background: rgba(10, 10, 10, 0.1);
    }

    .nav-button:hover {
      border-color: white;
      background: rgba(255, 255, 255, 0.1);
    }

    .menu-button {
        background-color: var(--color-accent);
        border: 1px solid var(--color-accent);
        width: 50px;
        height: 50px;
        padding: 0;
        border-radius: 50%;
        position: relative;
        z-index: 101; /* Above mobile nav */
    }
    .menu-button:hover {
        background-color: white;
        border-color: white;
    }

    .menu-icon-bars {
        width: 24px;
        height: 24px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        padding: 4px 0;
        box-sizing: border-box;
    }
    .menu-icon-bars .bar {
        display: block;
        width: 20px;
        height: 2px;
        background-color: black;
        border-radius: 2px;
        transition: transform 0.3s ease, opacity 0.3s ease;
    }
    .site-header.menu-open .menu-button:hover {
      background-color: var(--color-accent);
    }


    /* Mobile Nav */
    .mobile-nav {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #031114;
        z-index: 99;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .mobile-nav-link {
        font-size: 2.5rem;
        font-weight: 500;
        color: var(--color-text-secondary);
        text-decoration: none;
        padding: 0.5rem 1rem;
        transition: color 0.3s ease;
    }

    .mobile-nav-link:hover {
        color: white;
    }

    /* Hero Section */
    .hero-section {
      height: 100vh;
      min-height: 700px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: left;
      padding: 2rem;
      box-sizing: border-box;
      overflow: hidden;
      color: white;
      background-color: #000;
    }

    #hero-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .hero-content {
        position: relative;
        z-index: 4;
        width: 100%;
        max-width: 1400px;
        padding: 0 2rem;
        box-sizing: border-box;
    }

    .hero-section h1 {
      font-size: clamp(3rem, 6vw, 5rem);
      font-weight: 500;
      line-height: 1.15;
      max-width: 900px;
      margin: 0;
      letter-spacing: -0.03em;
    }

    .hero-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 2rem 4rem;
        box-sizing: border-box;
        z-index: 4;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
    }

    .hero-footer-left p {
        margin: 0;
        font-size: 1rem;
        color: var(--color-text-secondary);
        line-height: 1.5;
    }

    .explore-button {
      padding: 0.75rem 1.5rem;
      border-radius: 2rem;
      border: 1px solid var(--color-border);
      background: rgba(10, 10, 10, 0.2);
      backdrop-filter: blur(10px);
      color: white;
      display: flex;
      align-items: center;
      gap: 1rem;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 500;
      transition: all 0.2s ease;
    }
    .explore-button:hover {
        border-color: white;
        background: rgba(255, 255, 255, 0.1);
    }
    .explore-button svg {
        transition: transform 0.3s ease;
    }
    .explore-button:hover svg {
        transform: translateY(3px);
    }

    /* General Content Sections */
    .content-section {
      padding: 8rem 2rem;
      box-sizing: border-box;
      background-color: var(--color-background-section);
      border-top: 1px solid var(--color-border);
    }
    .hero-section + .content-section {
        border-top: none;
    }

    .section-container, .section-container-wide {
      max-width: 1400px;
      margin: 0 auto;
    }

    .section-label {
      text-transform: uppercase;
      color: var(--color-text-secondary);
      font-weight: 500;
      font-size: 0.9rem;
      margin-bottom: 2rem;
      letter-spacing: 0.05em;
    }

    .section-heading {
      font-size: clamp(2rem, 3.5vw, 3rem);
      font-weight: 500;
      line-height: 1.25;
      margin: 0 0 2rem 0;
      letter-spacing: -0.03em;
      color: white;
    }

    .section-description {
      font-size: 1.1rem;
      line-height: 1.8;
      color: var(--color-text-secondary);
      max-width: 50ch;
      margin-bottom: 2rem;
    }

    .section-button {
      display: inline-flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem 1.5rem;
      border: 1px solid var(--color-border);
      border-radius: 2rem;
      transition: all 0.2s ease;
      color: white;
      font-weight: 500;
      text-decoration: none;
    }

    .section-button:hover {
      background-color: white;
      color: black;
      border-color: white;
    }
    .section-button:hover svg {
      stroke: black;
    }

    /* Why Us Section - Overhauled */
    .why-us-section {
      position: relative;
      padding: 10rem 2rem;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: left;
      overflow: hidden;
    }
    .why-us-background {
      position: absolute;
      inset: 0;
      background-image: url(https://images.unsplash.com/photo-1679085299933-228473a2a681?q=80&w=2787&auto=format&fit=crop);
      background-size: cover;
      background-position: center;
      z-index: 0;
      filter: brightness(0.6);
    }
     .why-us-section::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(to top, var(--color-background-section), transparent 50%);
      z-index: 1;
    }
    .why-us-content {
      position: relative;
      z-index: 2;
      max-width: 1200px;
    }
    .why-us-section .section-heading {
      max-width: 35ch;
      margin-bottom: 3rem;
    }
    .why-us-description {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem 4rem;
      color: var(--color-text-secondary);
      font-size: 1.1rem;
      line-height: 1.8;
      margin-bottom: 3rem;
    }
    .why-us-description p {
      margin: 0;
      max-width: 50ch;
    }

    /* Partners Section */
    .partners-section {
        background-color: var(--color-background-section);
        padding: 4rem 0;
        border-top: 1px solid var(--color-border);
        overflow: hidden;
    }
    .partners-scroller {
      width: 100%;
      display: flex;
    }
    .partners-track {
        display: flex;
        animation: scroll 40s linear infinite;
    }
    @keyframes scroll {
        from { transform: translateX(0); }
        to { transform: translateX(-50%); }
    }
    .partner-logo {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--color-text-secondary);
        filter: brightness(0.7);
        transition: filter 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        white-space: nowrap;
        padding: 0 3rem;
    }
    .partner-logo:hover {
        filter: brightness(1);
    }

    /* Services Section - Reimagined */
    .services-section-reimagined {
        background-color: var(--color-background);
    }
    .services-section-reimagined .services-container {
        max-width: 1400px;
        margin: 0 auto;
    }
    .services-section-reimagined .section-heading {
        max-width: 45ch;
        margin-bottom: 4rem;
    }
    .services-interactive-wrapper {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 4rem;
        min-height: 500px;
        margin-bottom: 4rem;
    }
    .services-list {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        border-left: 1px solid var(--color-border);
    }
    .services-list li {
        padding: 1.5rem 2rem;
        cursor: pointer;
        position: relative;
        transition: background-color 0.3s ease;
    }
    .services-list li h3 {
        font-size: 1.5rem;
        font-weight: 500;
        margin: 0;
        color: var(--color-text-secondary);
        transition: color 0.3s ease;
    }
    .services-list li.active h3 {
        color: white;
    }
    .services-list li::before {
        content: '';
        position: absolute;
        left: -1px;
        top: 0;
        width: 2px;
        height: 100%;
        background-color: var(--color-accent);
        transform: scaleY(0);
        transform-origin: center;
        transition: transform 0.4s cubic-bezier(0.22, 1, 0.36, 1);
    }
    .services-list li.active::before {
        transform: scaleY(1);
    }
    .services-visuals {
        display: grid;
        grid-template-rows: auto 1fr;
        gap: 2rem;
        position: relative;
    }
    .service-description-panel {
        position: relative;
        color: var(--color-text-secondary);
        font-size: 1.1rem;
        line-height: 1.7;
        height: 5em; /* Reserve space for 3 lines of text */
    }
    .service-description-panel p {
        margin: 0;
        position: absolute;
        inset: 0;
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
    }
    .service-description-panel p.active {
        opacity: 1;
    }
    .service-image-panel {
        position: relative;
        border-radius: 0.75rem;
        overflow: hidden;
    }
    .service-image {
        position: absolute;
        inset: 0;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        opacity: 0;
        transform: scale(1.05);
        transition: opacity 0.6s ease-in-out, transform 0.6s ease-in-out;
        background-color: var(--color-background-section);
    }
    .service-image.active {
        opacity: 1;
        transform: scale(1);
    }
    /* Fallback for missing service images */
    .service-image:not([style*="background-image"]) {
        background: linear-gradient(135deg, var(--color-background-section) 0%, rgba(var(--color-accent-rgb), 0.1) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--color-text-secondary);
        font-size: 3rem;
    }
    .service-image:not([style*="background-image"])::before {
        content: '🎵';
    }


    /* Software Section */
    .software-section {
        padding: 10rem 2rem;
        position: relative;
        display: flex;
        align-items: center;
        background-image: url(https://images.unsplash.com/photo-1620121692029-d088224ddc74?q=80&w=2832&auto=format&fit=crop);
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
    }
    .software-section::before {
        content: '';
        position: absolute;
        inset: 0;
        background-color: rgba(6, 26, 31, 0.8);
    }
    .software-content {
        position: relative;
        z-index: 1;
        max-width: 1400px;
        margin: 0 auto;
    }
    .software-section .section-heading {
        max-width: 25ch;
    }
    .software-section .section-description {
        margin-bottom: 3rem;
    }

    /* Card Sections (Case Studies) - Overhauled */
    .card-section {
        background-color: var(--color-background);
    }
    .card-section-header {
        margin: 0 auto;
        padding-bottom: 4rem;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        gap: 2rem;
        flex-wrap: wrap;
    }
    .card-section-header .section-heading {
        margin: 0;
    }
    .case-studies-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: auto auto;
        gap: 2rem;
    }
    .case-study-card {
        text-decoration: none;
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        position: relative;
        border-radius: 0.75rem;
        overflow: hidden;
        aspect-ratio: 4 / 3;
        padding: 2rem;
        box-sizing: border-box;
    }
    .case-study-card:first-child {
        grid-column: 1 / -1;
        aspect-ratio: 2 / 1;
    }
    .card-background {
        position: absolute;
        inset: 0;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-color: var(--color-background-section);
        transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    /* Fallback for missing case study images */
    .card-background:not([style*="background-image"]) {
        background: linear-gradient(135deg, var(--color-background-section) 0%, rgba(var(--color-accent-rgb), 0.2) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--color-accent);
        font-size: 4rem;
    }
    .card-background:not([style*="background-image"])::before {
        content: '🎼';
    }
    .case-study-card:hover .card-background {
        transform: scale(1.05);
    }
    .card-background::after {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0) 100%);
        transition: background 0.3s ease;
    }
     .case-study-card:hover .card-background::after {
        background: linear-gradient(to top, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.5) 50%, rgba(0,0,0,0.1) 100%);
    }
    .case-study-card .card-content {
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        width: 100%;
    }
    .card-category {
        font-size: 0.9rem;
        font-weight: 500;
        color: var(--color-text-secondary);
        display: block;
        margin-bottom: 0.5rem;
    }
    .card-title {
        font-size: 1.5rem;
        font-weight: 500;
        line-height: 1.4;
        margin: 0;
        max-width: 30ch;
    }
    .card-arrow {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: rgba(255,255,255,0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(5px);
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.3s ease;
    }
    .case-study-card:hover .card-arrow {
        transform: scale(1);
        opacity: 1;
    }


    /* Final CTA Section */
    .cta-section {
        background: var(--color-background-section);
        text-align: left;
        padding-top: 6rem;
    }
    .cta-section .section-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        max-width: 900px;
    }
    .cta-section .section-heading {
        margin-bottom: 3rem;
        font-size: clamp(2rem, 3vw, 2.5rem);
    }

    /* Footer */
    .site-footer-bottom {
        background-color: var(--color-background);
        padding: 6rem 2rem 2rem;
        border-top: 1px solid var(--color-border);
    }
    .footer-container {
        max-width: 1400px;
        margin: 0 auto 4rem auto;
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr;
        gap: 2rem;
    }
    .footer-logo {
        display: block;
        margin-bottom: 1rem;
    }
    .about-column p {
        color: var(--color-text-secondary);
        max-width: 35ch;
        line-height: 1.6;
    }
    .links-column h4 {
        font-size: 1.1rem;
        font-weight: 500;
        margin: 0 0 1.5rem 0;
        color: white;
    }
    .links-column ul {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    .links-column a {
        color: var(--color-text-secondary);
        text-decoration: none;
        transition: color 0.2s ease;
    }
    .links-column a:hover {
        color: white;
    }
    .footer-bottom-bar {
        max-width: 1400px;
        margin: 0 auto;
        padding-top: 2rem;
        border-top: 1px solid var(--color-border);
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--color-text-secondary);
        font-size: 0.9rem;
    }
    .social-links {
        display: flex;
        gap: 1.5rem;
    }
    .social-links a {
        color: var(--color-text-secondary);
        transition: color 0.2s ease;
    }
    .social-links a:hover {
        color: white;
    }
    .social-links svg {
        width: 20px;
        height: 20px;
    }


    /* Playground Wrapper */
    prompt-dj-midi-wrapper {
      display: block;
      width: 100%;
      height: 100vh;
      position: relative;
    }

    /* When in playground mode, adjust layout but keep header */
    body.playground prompt-dj-midi-wrapper {
      height: calc(100vh - 80px);
      margin-top: 80px;
    }

    body.playground .site-header {
      position: fixed;
      background: rgba(var(--color-background-section), 0.95);
      backdrop-filter: blur(10px);
      z-index: 1000;
    }

    /* Responsive adjustments */
    @media (max-width: 991px) {
        .header-nav .login-button {
            display: none;
        }
        .footer-container {
            grid-template-columns: 1fr 1fr;
            gap: 4rem 2rem;
        }
        .about-column {
            grid-column: 1 / -1;
        }
        .case-studies-grid {
            grid-template-columns: 1fr;
        }
        .case-study-card:first-child {
            grid-column: auto;
            aspect-ratio: 4/3;
        }
        .services-interactive-wrapper {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        .services-list {
            flex-direction: row;
            border-left: none;
            border-bottom: 1px solid var(--color-border);
            overflow-x: auto;
            /* Hide scrollbar */
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
        .services-list::-webkit-scrollbar {
            display: none; /* Chrome, Safari and Opera */
        }
        .services-list li {
            padding: 1rem 1.5rem;
            border-bottom: 2px solid transparent;
            border-top: 2px solid transparent;
            white-space: nowrap;
        }
        .services-list li h3 {
            font-size: 1.2rem;
        }
        .services-list li::before {
            top: auto;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
        }
        .services-visuals {
            min-height: 400px;
        }
    }

    @media (max-width: 768px) {
      .site-header {
        padding: 1.5rem;
      }
      .menu-button {
          width: 44px;
          height: 44px;
      }
      .hero-section h1 {
        font-size: 2.5rem;
      }
      .hero-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 2rem;
        text-align: left;
        padding: 2rem;
      }
      .why-us-description {
        grid-template-columns: 1fr;
      }
      .footer-container {
        grid-template-columns: 1fr;
        gap: 3rem;
      }
      .footer-bottom-bar {
          flex-direction: column-reverse;
          gap: 1.5rem;
          text-align: center;
      }
      .card-title {
        font-size: 1.25rem;
      }
      .content-section {
        padding: 6rem 1rem;
      }
      .why-us-section {
        padding: 8rem 1rem;
      }
      .software-section {
        padding: 8rem 1rem;
        background-attachment: scroll; /* Performance on mobile */
      }
      .mobile-nav-link {
        font-size: 2rem;
      }
    }

    /* Page-specific styles matching home page design */
    .page-hero {
      height: 60vh;
      min-height: 500px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      background: var(--color-background);
      overflow: hidden;
    }
    .page-hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.05) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.02) 100%);
      pointer-events: none;
    }
    .page-hero-content {
      position: relative;
      z-index: 2;
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 2rem;
      width: 100%;
      box-sizing: border-box;
    }
    .page-hero-content h1 {
      font-size: clamp(2.5rem, 5vw, 4rem);
      font-weight: 500;
      line-height: 1.15;
      max-width: 800px;
      margin: 0 0 2rem 0;
      color: var(--color-text);
    }
    .page-hero-content p {
      font-size: 1.25rem;
      color: var(--color-text-secondary);
      max-width: 600px;
      margin: 0;
      line-height: 1.6;
      font-weight: 300;
    }

    /* About page styles matching home page design */
    .about-content {
      padding: 8rem 2rem;
      background-color: var(--color-background-section);
      border-top: 1px solid var(--color-border);
    }
    .about-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 6rem;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }
    .about-text h2 {
      font-size: clamp(2rem, 4vw, 3rem);
      margin: 1rem 0 2rem 0;
      line-height: 1.2;
      font-weight: 500;
      color: var(--color-text);
    }
    .about-text p {
      font-size: 1.125rem;
      line-height: 1.7;
      margin-bottom: 2rem;
      color: var(--color-text-secondary);
      font-weight: 300;
    }
    .about-image {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
    }
    .image-placeholder {
      width: 400px;
      height: 400px;
      background: linear-gradient(135deg, var(--color-background) 0%, var(--color-background-section) 100%);
      border-radius: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid var(--color-border);
      position: relative;
      overflow: hidden;
    }
    .image-placeholder::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg,
        rgba(var(--color-accent-rgb), 0.1) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.05) 100%);
    }
    .image-placeholder svg {
      color: var(--color-accent);
      position: relative;
      z-index: 2;
    }

    .team-section {
      background: var(--color-background);
      padding: 8rem 2rem;
      border-top: 1px solid var(--color-border);
    }
    .team-section .section-container {
      max-width: 1400px;
      margin: 0 auto;
    }
    .team-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 3rem;
      margin-top: 4rem;
    }
    .team-member {
      text-align: center;
      padding: 3rem 2rem;
      background: var(--color-background-section);
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;
    }
    .team-member::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.02) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .team-member:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15);
      border-color: rgba(var(--color-accent-rgb), 0.3);
    }
    .team-member:hover::before {
      opacity: 1;
    }
    .member-avatar {
      width: 100px;
      height: 100px;
      background: linear-gradient(135deg, var(--color-background) 0%, var(--color-accent) 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 2rem;
      border: 3px solid var(--color-accent);
      position: relative;
      z-index: 2;
    }
    .member-avatar svg {
      color: var(--color-background);
    }
    .team-member h3 {
      font-size: 1.5rem;
      margin: 0 0 0.5rem 0;
      font-weight: 500;
      position: relative;
      z-index: 2;
    }
    .member-role {
      color: var(--color-accent);
      font-weight: 600;
      margin: 0 0 1.5rem 0;
      font-size: 0.95rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      position: relative;
      z-index: 2;
    }
    .team-member p:last-child {
      color: var(--color-text-secondary);
      line-height: 1.6;
      font-weight: 300;
      position: relative;
      z-index: 2;
    }

    .values-section {
      padding: 8rem 2rem;
      background: var(--color-background-section);
      border-top: 1px solid var(--color-border);
    }
    .values-section .section-container {
      max-width: 1400px;
      margin: 0 auto;
    }
    .values-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 3rem;
      margin-top: 4rem;
    }
    .value-item {
      text-align: center;
      padding: 3rem 2rem;
      background: var(--color-background);
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;
    }
    .value-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.05) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.02) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .value-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 40px rgba(0,0,0,0.1);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .value-item:hover::before {
      opacity: 1;
    }
    .value-icon {
      font-size: 3.5rem;
      margin-bottom: 1.5rem;
      position: relative;
      z-index: 2;
    }
    .value-item h3 {
      font-size: 1.5rem;
      margin: 0 0 1.5rem 0;
      font-weight: 500;
      position: relative;
      z-index: 2;
    }
    .value-item p {
      color: var(--color-text-secondary);
      line-height: 1.6;
      font-weight: 300;
      position: relative;
      z-index: 2;
    }

    /* Services page styles matching home page design */
    .services-detail {
      padding: 8rem 2rem;
      background: var(--color-background-section);
      border-top: 1px solid var(--color-border);
    }
    .services-detail .section-container {
      max-width: 1400px;
      margin: 0 auto;
    }
    .services-detail-grid {
      display: grid;
      gap: 6rem;
    }
    .service-detail-card {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 4rem;
      align-items: center;
      padding: 4rem;
      background: var(--color-background);
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;
    }
    .service-detail-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.02) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .service-detail-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 25px 70px rgba(0,0,0,0.15);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .service-detail-card:hover::before {
      opacity: 1;
    }
    .service-detail-card:nth-child(even) {
      direction: rtl;
    }
    .service-detail-card:nth-child(even) > * {
      direction: ltr;
    }
    .service-detail-image {
      width: 100%;
      height: 350px;
      background-size: cover;
      background-position: center;
      border-radius: 1rem;
      background-color: var(--color-background-section);
      border: 1px solid var(--color-border);
      position: relative;
      z-index: 2;
    }
    .service-detail-content {
      position: relative;
      z-index: 2;
    }
    .service-detail-content h3 {
      font-size: clamp(1.75rem, 3vw, 2.5rem);
      margin: 0 0 1.5rem 0;
      color: var(--color-accent);
      font-weight: 500;
    }
    .service-detail-content p {
      font-size: 1.125rem;
      line-height: 1.7;
      color: var(--color-text-secondary);
      margin-bottom: 2.5rem;
      font-weight: 300;
    }
    .service-features h4 {
      margin: 0 0 1.5rem 0;
      color: var(--color-text);
      font-size: 1.25rem;
      font-weight: 500;
    }
    .service-features ul {
      list-style: none;
      padding: 0;
      margin: 0 0 3rem 0;
    }
    .service-features li {
      padding: 0.75rem 0;
      color: var(--color-text-secondary);
      position: relative;
      padding-left: 2rem;
      font-weight: 300;
      border-bottom: 1px solid var(--color-border);
    }
    .service-features li:last-child {
      border-bottom: none;
    }
    .service-features li::before {
      content: '✓';
      position: absolute;
      left: 0;
      color: var(--color-accent);
      font-weight: bold;
      font-size: 1.1rem;
    }
    .service-cta {
      display: inline-flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem 2rem;
      background: var(--color-accent);
      color: var(--color-background);
      text-decoration: none;
      border-radius: 0.75rem;
      font-weight: 600;
      font-size: 1rem;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border: 2px solid var(--color-accent);
    }
    .service-cta:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 35px rgba(var(--color-accent-rgb), 0.4);
      background: transparent;
      color: var(--color-accent);
    }

    /* Coming Soon Section - Matching Home Page Design */
    .pricing-coming-soon {
      background: var(--color-background);
      padding: 8rem 2rem;
      border-top: 1px solid var(--color-border);
      position: relative;
      overflow: hidden;
    }
    .pricing-coming-soon::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.02) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      pointer-events: none;
    }
    .pricing-coming-soon .section-container {
      max-width: 1000px;
      margin: 0 auto;
      position: relative;
      z-index: 2;
    }
    .coming-soon-content {
      text-align: center;
      padding: 4rem 2rem;
      background: var(--color-background-section);
      border-radius: 1.5rem;
      border: 1px solid var(--color-border);
      position: relative;
      overflow: hidden;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    .coming-soon-content::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.03) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .coming-soon-content:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .coming-soon-content:hover::before {
      opacity: 1;
    }
    .coming-soon-icon {
      margin: 0 auto 2rem;
      width: 120px;
      height: 120px;
      background: linear-gradient(135deg, var(--color-accent) 0%, #e6a000 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 2;
      animation: pulse 2s infinite;
    }
    .coming-soon-icon svg {
      color: var(--color-background);
    }
    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }
    .coming-soon-description {
      font-size: 1.25rem;
      line-height: 1.7;
      color: var(--color-text-secondary);
      margin: 2rem auto 3rem;
      max-width: 600px;
      font-weight: 300;
      position: relative;
      z-index: 2;
    }
    .coming-soon-description strong {
      color: var(--color-accent);
      font-weight: 600;
    }
    .coming-soon-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin: 3rem 0;
      position: relative;
      z-index: 2;
    }
    .feature-highlight {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1.5rem;
      background: var(--color-background);
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      transition: all 0.3s ease;
      text-align: left;
    }
    .feature-highlight:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .feature-icon {
      font-size: 2rem;
      flex-shrink: 0;
    }
    .feature-text h4 {
      margin: 0 0 0.5rem 0;
      font-size: 1.1rem;
      font-weight: 500;
      color: var(--color-text);
    }
    .feature-text p {
      margin: 0;
      font-size: 0.95rem;
      color: var(--color-text-secondary);
      line-height: 1.5;
    }
    .coming-soon-cta {
      display: flex;
      gap: 1rem;
      justify-content: center;
      align-items: center;
      margin-top: 3rem;
      position: relative;
      z-index: 2;
    }
    .section-button.primary {
      background: var(--color-accent);
      color: var(--color-background);
      border: 2px solid var(--color-accent);
    }
    .section-button.primary:hover {
      background: transparent;
      color: var(--color-accent);
    }
    .section-button.secondary {
      background: transparent;
      color: var(--color-accent);
      border: 2px solid var(--color-accent);
    }
    .section-button.secondary:hover {
      background: var(--color-accent);
      color: var(--color-background);
    }

    /* Contact page styles matching home page design */
    .contact-content {
      padding: 8rem 2rem;
      background: var(--color-background-section);
      border-top: 1px solid var(--color-border);
    }
    .contact-content .section-container {
      max-width: 1400px;
      margin: 0 auto;
    }
    .contact-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 6rem;
      align-items: start;
    }
    .contact-info h2 {
      font-size: clamp(2rem, 4vw, 3rem);
      margin: 1rem 0 2rem 0;
      line-height: 1.2;
      font-weight: 500;
    }
    .contact-info p {
      font-size: 1.125rem;
      line-height: 1.7;
      color: var(--color-text-secondary);
      margin-bottom: 4rem;
      font-weight: 300;
    }
    .contact-methods {
      display: flex;
      flex-direction: column;
      gap: 3rem;
    }
    .contact-method {
      display: flex;
      align-items: flex-start;
      gap: 1.5rem;
      padding: 2rem;
      background: var(--color-background);
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;
    }
    .contact-method::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.02) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .contact-method:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 40px rgba(0,0,0,0.1);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .contact-method:hover::before {
      opacity: 1;
    }
    .contact-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, var(--color-accent) 0%, #e6a000 100%);
      border-radius: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      border: none;
      position: relative;
      z-index: 2;
    }
    .contact-icon svg {
      color: var(--color-background);
    }
    .contact-method div:last-child {
      position: relative;
      z-index: 2;
    }
    .contact-method h4 {
      margin: 0 0 0.75rem 0;
      font-size: 1.25rem;
      font-weight: 500;
    }
    .contact-method p {
      margin: 0;
      color: var(--color-text-secondary);
      line-height: 1.5;
      font-weight: 300;
    }

    .contact-form {
      background: var(--color-background);
      border-radius: 1rem;
      padding: 3rem;
      border: 1px solid var(--color-border);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;
    }
    .contact-form::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.02) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .contact-form:hover {
      box-shadow: 0 20px 60px rgba(0,0,0,0.1);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .contact-form:hover::before {
      opacity: 1;
    }
    .contact-form-container {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      position: relative;
      z-index: 2;
    }
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }
    .form-group label {
      font-weight: 500;
      color: var(--color-text);
      font-size: 1rem;
    }
    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 1rem 1.25rem;
      border: 1px solid var(--color-border);
      border-radius: 0.75rem;
      background: var(--color-background-section);
      color: var(--color-text);
      font-family: inherit;
      font-size: 1rem;
      transition: all 0.3s ease;
      font-weight: 300;
    }
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: var(--color-accent);
      box-shadow: 0 0 0 3px rgba(var(--color-accent-rgb), 0.1);
      background: var(--color-background);
    }
    .form-group textarea {
      resize: vertical;
      min-height: 140px;
      line-height: 1.6;
    }
    .form-submit {
      padding: 1.25rem 2.5rem;
      background: var(--color-accent);
      color: var(--color-background);
      border: 2px solid var(--color-accent);
      border-radius: 0.75rem;
      font-weight: 600;
      font-size: 1rem;
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      margin-top: 1rem;
    }
    .form-submit:hover {
      background: transparent;
      color: var(--color-accent);
      transform: translateY(-3px);
      box-shadow: 0 15px 35px rgba(var(--color-accent-rgb), 0.4);
    }

    .faq-section {
      background: var(--color-background);
      padding: 8rem 2rem;
      border-top: 1px solid var(--color-border);
    }
    .faq-section .section-container {
      max-width: 1400px;
      margin: 0 auto;
    }
    .faq-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 3rem;
      margin-top: 4rem;
    }
    .faq-item {
      background: var(--color-background-section);
      padding: 3rem 2.5rem;
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;
    }
    .faq-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.02) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .faq-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 50px rgba(0,0,0,0.1);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .faq-item:hover::before {
      opacity: 1;
    }
    .faq-item h4 {
      margin: 0 0 1.5rem 0;
      color: var(--color-accent);
      font-size: 1.25rem;
      font-weight: 500;
      position: relative;
      z-index: 2;
    }
    .faq-item p {
      margin: 0;
      color: var(--color-text-secondary);
      line-height: 1.6;
      font-weight: 300;
      position: relative;
      z-index: 2;
    }

    /* Responsive styles for new pages matching home page */
    @media (max-width: 991px) {
      .page-hero {
        height: 50vh;
        min-height: 400px;
      }
      .page-hero-content h1 {
        font-size: 2.5rem;
      }
      .about-grid,
      .contact-grid {
        grid-template-columns: 1fr;
        gap: 4rem;
      }
      .service-detail-card {
        grid-template-columns: 1fr;
        gap: 3rem;
        padding: 3rem 2rem;
      }
      .service-detail-card:nth-child(even) {
        direction: ltr;
      }
      .coming-soon-features {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
      .coming-soon-cta {
        flex-direction: column;
        gap: 1rem;
      }
      .team-grid,
      .values-grid,
      .faq-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      }
    }

    @media (max-width: 767px) {
      .page-hero {
        height: 40vh;
        min-height: 350px;
        padding: 0 1rem;
      }
      .page-hero-content h1 {
        font-size: 2rem;
      }
      .about-content,
      .contact-content,
      .services-detail,
      .team-section,
      .values-section,
      .pricing-section,
      .faq-section {
        padding: 6rem 1rem;
      }
      .about-grid,
      .contact-grid {
        gap: 3rem;
      }
      .service-detail-card {
        padding: 2rem 1.5rem;
        gap: 2rem;
      }
      .team-grid,
      .values-grid,
      .faq-grid,
      .coming-soon-features {
        grid-template-columns: 1fr;
        gap: 2rem;
      }
      .contact-form {
        padding: 2rem 1.5rem;
      }
      .contact-methods {
        gap: 2rem;
      }
      .contact-method {
        padding: 1.5rem;
      }
      .image-placeholder {
        width: 300px;
        height: 300px;
      }
    }

    @media (max-width: 480px) {
      .page-hero-content h1 {
        font-size: 1.75rem;
      }
      .about-content,
      .contact-content,
      .services-detail,
      .team-section,
      .values-section,
      .pricing-coming-soon,
      .faq-section {
        padding: 4rem 1rem;
      }
      .service-detail-card,
      .team-member,
      .value-item,
      .coming-soon-content,
      .faq-item {
        padding: 1.5rem;
      }
      .contact-form {
        padding: 1.5rem;
      }
      .image-placeholder {
        width: 250px;
        height: 250px;
      }
    }
  `;K([N()],J.prototype,"route",2);K([N()],J.prototype,"isPageTransitioning",2);K([N()],J.prototype,"isMenuOpen",2);K([vt("#hero-canvas")],J.prototype,"heroCanvas",2);K([vt("#custom-cursor")],J.prototype,"customCursor",2);K([xt(".services-list li")],J.prototype,"servicesListItems",2);K([xt(".service-description-panel p")],J.prototype,"servicesDescriptions",2);K([xt(".service-image")],J.prototype,"servicesImages",2);J=K([te("app-shell")],J);function Xp(){document.body.appendChild(new J)}Xp();
