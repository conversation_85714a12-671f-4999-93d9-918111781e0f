<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='50' cy='50' r='45' fill='%23F9B200'/%3E%3Cpath d='M30 40h40v5H30zm0 10h40v5H30zm0 10h40v5H30z' fill='%23000'/%3E%3C/svg%3E" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PixelVane - AI Music & Audio Generation</title>
    <meta name="description" content="Specialists in AI music, audio generation and creative analysis. Unparalleled precision and forensically accurate reports.">
    <script type="module" crossorigin src="/assets/index-CyH7uHxT.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-DB1UuxUZ.css">
  </head>
  <body>
  </body>
</html>
