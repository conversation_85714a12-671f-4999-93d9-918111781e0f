/**
 * @fileoverview Main application shell for PixelVane.
 * Handles routing between the homepage and the PromptDJ MIDI playground.
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

import { LitElement, html, css } from 'lit';
import { customElement, state, query, queryAll } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';

// Import CSS
import './index.css';

// Original app imports
import type { PlaybackState, Prompt } from './types';
import { GoogleGenAI, LiveMusicFilteredPrompt } from '@google/genai';
import { PromptDjMidi } from './components/PromptDjMidi';
import { ToastMessage } from './components/ToastMessage';
import { LiveMusicHelper } from './utils/LiveMusicHelper';
import { AudioAnalyser } from './utils/AudioAnalyser';

// Import all components so they are registered
import './components/PlayPauseButton';
import './components/PromptController';
import './components/PromptDjMidi';
import './components/ToastMessage';
import './components/WeightKnob';
import './components/FxPad';
import './components/DeepgramTestWindow';
import './components/SongwriterWindow';

// Prompt definitions for the playground
const PALETTE = ['#3498db', '#2ecc71', '#9b59b6', '#f1c40f', '#e67e22', '#e74c3c', '#1abc9c', '#34495e', '#ecf0f1', '#7f8c8d', '#f39c12', '#d35400', '#c0392b', '#16a085', '#27ae60', '#2980b9', '#8e44ad', '#bdc3c7', '#5dade2', '#58d68d', '#af7ac5', '#f4d03f', '#eb984e', '#edbb99', '#a3e4d7', '#d2b4de', '#f5b7b1'];





/**
 * A wrapper component that encapsulates the entire PromptDJ MIDI application logic.
 */
@customElement('prompt-dj-midi-wrapper')
export class PromptDjMidiWrapper extends LitElement {
  private pdjMidi: PromptDjMidi;
  private toastMessage: ToastMessage;

  constructor() {
    super();

    // The entire setup from the original `main` function is now here.
    const ai = new GoogleGenAI({
      apiKey: import.meta.env.VITE_API_KEY,
      apiVersion: 'v1alpha'  // Required for Lyria RealTime experimental model
    });
    const model = 'models/lyria-realtime-exp';  // Use full model path

    const initialPrompts = buildPlaygroundPrompts();
    const liveMusicHelper = new LiveMusicHelper(ai, model);

    this.pdjMidi = new PromptDjMidi(initialPrompts, ai, PROMPT_DEFINITIONS, liveMusicHelper);
    this.toastMessage = new ToastMessage();

    liveMusicHelper.setWeightedPrompts(new Map());

    const audioAnalyser = new AudioAnalyser(liveMusicHelper.audioContext);
    liveMusicHelper.extraDestination = audioAnalyser.node;

    this.pdjMidi.addEventListener('prompts-changed', ((e: Event) => {
      const customEvent = e as CustomEvent<Map<string, Prompt>>;
      const prompts = customEvent.detail;
      liveMusicHelper.setWeightedPrompts(prompts);
    }) as EventListener);

    this.pdjMidi.addEventListener('play-pause', () => {
      liveMusicHelper.playPause();
    });

    // Recording listeners
    this.pdjMidi.addEventListener('toggle-recording', () => {
      if (liveMusicHelper.isRecording) {
        liveMusicHelper.stopRecording();
      } else {
        liveMusicHelper.startRecording();
      }
    });

    liveMusicHelper.addEventListener('recording-state-changed', (e) => {
      const { isRecording } = (e as CustomEvent<{isRecording: boolean}>).detail;
      this.pdjMidi.isRecording = isRecording;
    });

    liveMusicHelper.addEventListener('recording-finished', (e) => {
      const { blob, prompts } = (e as CustomEvent<{blob: Blob, prompts: string[]}>).detail;
      this.pdjMidi.setDownload(blob, prompts);
    });

    liveMusicHelper.addEventListener('playback-state-changed', ((e: Event) => {
      const customEvent = e as CustomEvent<PlaybackState>;
      const playbackState = customEvent.detail;
      this.pdjMidi.playbackState = playbackState;
      playbackState === 'playing' ? audioAnalyser.start() : audioAnalyser.stop();
    }));

    liveMusicHelper.addEventListener('filtered-prompt', ((e: Event) => {
      const customEvent = e as CustomEvent<LiveMusicFilteredPrompt>;
      const filteredPrompt = customEvent.detail;
      this.toastMessage.show(filteredPrompt.filteredReason!)
      this.pdjMidi.addFilteredPrompt(filteredPrompt.text!);
    }));

    const errorToast = ((e: Event) => {
      const customEvent = e as CustomEvent<string>;
      const error = customEvent.detail;
      this.toastMessage.show(error);
    });

    liveMusicHelper.addEventListener('error', errorToast);
    this.pdjMidi.addEventListener('error', errorToast as EventListener);

    audioAnalyser.addEventListener('audio-level-changed', ((e: Event) => {
      const customEvent = e as CustomEvent<number>;
      const level = customEvent.detail;
      this.pdjMidi.audioLevel = level;
    }));
  }

  // Render the components into the wrapper's shadow DOM.
  override render() {
    return html`${this.pdjMidi}${this.toastMessage}`;
  }
}

// New structured prompt definitions from the updated playground
const PROMPT_DEFINITIONS: { text: string, category: string }[] = [
  // --- NEW STRUCTURED PROMPTS ---
  { text: 'A wistful neo-soul ballad at 75 BPM with a warm Rhodes piano, a deep pocket bassline, and crisp, laid-back drums, featuring sparse, shimmering string pads.', category: 'R&B / Soul Genre' },
  { text: 'An aggressive cyberpunk track at 150 BPM with a distorted synthesizer bass, glitched-out drum machine, and pulsing, neon-drenched synth arpeggios.', category: 'Electronic Genre' },
  { text: 'A melancholic and introspective acoustic folk piece at 90 BPM, featuring a fingerpicked acoustic guitar, a mournful cello melody, and the gentle sound of falling rain.', category: 'Folk Genre' },
  { text: 'A tense and epic cinematic chase scene at 160 BPM, with driving staccato strings, thunderous taiko drums, and heroic brass stabs, building in intensity.', category: 'Cinematic Genre' },
  { text: 'A hypnotic and meditative drone piece featuring deep, resonant Mongolian throat singing, layered with slow, evolving atmospheric synth pads and no percussion.', category: 'Mood' },

  // --- HIP HOP GENRE ---
  { text: 'Classic 90s boom bap hip hop beat with a dusty sample and hard-hitting drums', category: 'Hip Hop Genre' },
  { text: 'Modern trap beat at 140 BPM with deep 808s, fast hi-hat rolls, and a dark synth melody', category: 'Hip Hop Genre' },
  { text: 'Chill, introspective lo-fi hip hop for studying, with a gentle piano, vinyl crackle, and soft rainfall', category: 'Hip Hop Genre' },
  { text: 'Ethereal and dreamy cloud rap beat with washed-out synth pads and spaced-out vocal chops', category: 'Hip Hop Genre' },
  { text: 'Aggressive and distorted rage trap beat with heavy 808s and chaotic synth leads', category: 'Hip Hop Genre' },
  { text: 'Minimalist and bouncy Plugg music with simple, catchy synth melodies and a clean 808', category: 'Hip Hop Genre' },
  { text: 'Classic West Coast G-Funk with a whiny Moog synth lead and a funky bassline', category: 'Hip Hop Genre' },
  { text: 'Dark and menacing UK Drill beat with sliding 808s and complex, syncopated percussion', category: 'Hip Hop Genre' },
  { text: 'High-energy Jersey Club beat with a signature kick pattern and vocal chops', category: 'Hip Hop Genre' },
  { text: 'Distorted and aggressive Phonk drift house with a heavy cowbell melody and a saturated bass', category: 'Hip Hop Genre' },
  { text: 'Jazzy and conscious hip hop with an upright bass, smooth electric piano, and a laid-back beat, 90s style', category: 'Hip Hop Genre' },
  { text: 'Dark and gritty UK Grime beat at 140 BPM with square wave bass sounds and aggressive energy', category: 'Hip Hop Genre' },
  { text: 'Upbeat and bouncy New Orleans Bounce music with a "Triggerman" beat and call-and-response chants', category: 'Hip Hop Genre' },

  // --- HIP HOP ELEMENTS ---
  { text: 'Boom Bap drum loop with a crisp snare and a kick drum with punch', category: 'Hip Hop Drums' },
  { text: 'Hard-hitting trap drum pattern with rapid-fire hi-hat rolls and a snappy clap', category: 'Hip Hop Drums' },
  { text: 'Deep sub-rattling 808 bass pattern with a long decay, suitable for trap', category: 'Hip Hop Bass' },
  { text: 'Clean and punchy 808 bass, tuned to C', category: 'Hip Hop Bass' },
  { text: 'Gliding and sliding 808 bassline, characteristic of UK Drill music', category: 'Hip Hop Bass' },
  { text: 'A catchy, memorable cowbell melody loop in a Memphis rap style', category: 'Hip Hop Melodic' },
  { text: 'A chopped and rearranged soul vocal sample, creating a new, soulful melody', category: 'Hip Hop Melodic' },
  { text: 'Mellow and warm Rhodes electric piano chords, perfect for a chill hip hop track', category: 'Hip Hop Keys' },
  { text: 'A dark, melancholic piano melody with sparse notes, suitable for a trap beat', category: 'Hip Hop Keys' },
  { text: 'Atmospheric vinyl crackle and hiss from an old record player', category: 'Hip Hop FX' },
  { text: 'Classic turntable scratches and baby scratches performed by a DJ', category: 'Hip Hop FX' },
  { text: 'A reversed cymbal swell, used as a transition effect', category: 'Hip Hop FX' },
  { text: 'A set of modern rap ad-libs like "yeah", "uh", "let\'s go"', category: 'Vocal Textures' },

  // --- JAZZ GENRE & ELEMENTS ---
  { text: 'Smoky late-night jazz trio with a walking upright bass, gentle brush drums, and a melancholic piano solo', category: 'Jazz Genre' },
  { text: 'Upbeat and energetic Bebop jazz ensemble featuring a rapid trumpet solo and complex rhythms', category: 'Jazz Genre' },
  { text: 'Funky 70s jazz fusion with a groovy electric piano, slap bass, and a tight drum break', category: 'Jazz Genre' },
  { text: 'Cool and relaxed jazz with a mellow tenor saxophone lead and soft piano comping', category: 'Jazz Genre' },
  { text: 'A classic walking bassline on an upright bass, providing a steady harmonic foundation', category: 'Jazz Bass' },
  { text: 'A virtuosic and melodic fretless electric bass solo in the style of Jaco Pastorius', category: 'Jazz Bass' },
  { text: 'A smooth and soulful saxophone solo with long, expressive notes', category: 'Jazz Melodic' },
  { text: 'A muted trumpet solo, creating a cool and intimate atmosphere in the style of Miles Davis', category: 'Jazz Brass' },
  { text: 'A lively, syncopated ragtime piano solo', category: 'Jazz Keys' },
  { text: 'Improvised scat vocal melody with nonsensical syllables', category: 'Vocal Textures' },
  { text: 'The sound of a drummer playing with brushes on a snare, creating a soft, shuffling rhythm', category: 'Jazz Drums' },
  { text: 'A swinging big band brass section with tight, powerful horn stabs', category: 'Jazz Brass' },
  { text: 'A warm and clean electric guitar melody in the style of Wes Montgomery, played with octaves', category: 'Jazz Melodic' },

  // --- FUNK ---
  { text: 'Upbeat 70s funk band with a tight pocket groove, prominent slap bass, and a powerful horn section', category: 'Funk Genre' },
  { text: 'A percussive and groovy slap bass riff that drives the song', category: 'Funk Bass' },
  { text: 'A rhythmic, "wacka-wacka" wah-wah guitar riff, quintessential for funk', category: 'Funk Guitar' },
  { text: 'The iconic "Funky Drummer" breakbeat by Clyde Stubblefield', category: 'Funk Drums' },
  { text: 'Short, powerful brass section stabs that add excitement', category: 'Funk Brass' },
  { text: 'A percussive and rhythmic Clavinet melody in the style of Stevie Wonder', category: 'Funk Keys' },
  { text: 'A group of conga drums playing a syncopated, groovy rhythm', category: 'Funk Percussion' },
  { text: 'A squelchy and futuristic P-Funk synthesizer lead, in the style of Bernie Worrell', category: 'Funk Synth' },
  { text: 'A vocal melody processed through a talk box effect, making the voice sound like an instrument', category: 'Vocal Textures' },
  { text: 'A vocal grunt in the style of James Brown', category: 'Vocal Textures' },

  // --- LO-FI ---
  { text: 'A rainy day lo-fi beat with a gentle, muffled piano melody, soft tape hiss, and a dusty drum loop', category: 'Lo-fi Genre' },
  { text: 'Cozy and nostalgic lo-fi with a warbling electric piano, kalimba melody, and sounds of turning pages', category: 'Lo-fi Genre' },
  { text: 'The sound of gentle rain falling, perfect for creating a relaxing atmosphere', category: 'Lo-fi FX' },
  { text: 'The warm hiss and noise from an old cassette tape', category: 'Lo-fi FX' },
  { text: 'A synthesizer pad with a gentle, wavering pitch, creating a wobbly, nostalgic feel', category: 'Lo-fi Synth' },
  { text: 'A simple, relaxed electric guitar melody with a clean tone and a hint of reverb', category: 'Lo-fi Melodic' },
  { text: 'A sentimental upright piano melody with a soft, felted sound', category: 'Lo-fi Keys' },
  { text: 'The sound effect of a vinyl record stopping abruptly', category: 'Lo-fi FX' },
  { text: 'A gentle and charming kalimba melody', category: 'Lo-fi Melodic' },

  // --- ELECTRONIC GENRE ---
  { text: 'Driving, hypnotic Berlin techno groove at 135 BPM with a rumbling kick and atmospheric pads', category: 'Electronic Genre' },
  { text: 'Classic Chicago house track with a soulful vocal sample, upright piano chords, and a 909 drum machine beat', category: 'Electronic Genre' },
  { text: 'Euphoric, uplifting trance anthem at 140 BPM with soaring supersaw chords, a driving bassline, and a gated vocal pad', category: 'Electronic Genre' },
  { text: 'High-energy drum and bass with a complex, chopped amen break, a deep reese bassline, and atmospheric jungle pads', category: 'Electronic Genre' },
  { text: 'Aggressive, heavy dubstep with a robotic wobble bass, syncopated drums, and jarring sound effects', category: 'Electronic Genre' },
  { text: 'Nostalgic 80s synthwave with retro gated reverb drums, a soaring synth lead, and a pulsating arpeggiated bassline', category: 'Electronic Genre' },
  { text: 'Ethereal, atmospheric ambient soundscape with evolving pads, no percussion, designed for deep listening', category: 'Electronic Genre' },
  { text: 'Glitchy and chaotic hyperpop with distorted 808s, sped-up vocal chops, and bright, bubbly synth melodies', category: 'Pop Genre' },
  { text: 'Hypnotic and rolling psytrance at 145 BPM with a galloping bassline and trippy, psychedelic synth effects', category: 'Electronic Genre' },
  { text: 'Minimal and groovy tech house with a punchy kick, a catchy bassline, and quirky percussion elements', category: 'Electronic Genre' },
  { text: 'Dark and brooding industrial techno with distorted textures, metallic percussion, and an EBM bassline', category: 'Electronic Genre' },
  { text: 'Relaxed and soulful trip-hop with a slow breakbeat, a moody bassline, and a sampled jazz piano', category: 'Electronic Genre' },
  { text: 'Old-school 8-bit chiptune video game music with simple square wave melodies and arpeggios', category: 'Electronic Genre' },
  { text: 'High-energy hardstyle with a distorted, pitched kick drum and a euphoric, anthemic synthesizer melody', category: 'Electronic Genre' },
  { text: 'Dreamy and introspective future garage with shuffled hi-hats, deep sub-bass, and pitched vocal samples', category: 'Electronic Genre' },
  { text: 'Futuristic and bass-heavy neurofunk with intricate drum patterns and complex, technical bass sound design', category: 'Electronic Genre' },
  { text: 'Sample-heavy French House with a filtered bassline and a four-on-the-floor beat, 90s style', category: 'Electronic Genre' },
  { text: 'Uplifting future bass with wide supersaw chords, pitch-bent vocal chops, and a complex rhythm', category: 'Electronic Genre' },
  { text: 'Aggressive Dutch Hardcore Gabber at 180 BPM with a heavily distorted 909 kick drum', category: 'Electronic Genre' },
  { text: 'Aesthetic and melancholic Vaporwave with slowed-down 80s samples, lush pads, and a feeling of nostalgia', category: 'Electronic Genre' },
  { text: 'Chaotic and frenetic Breakcore with rapidly spliced Amen breaks and distorted synth stabs', category: 'Electronic Genre' },
  { text: 'Dark and occult-themed Witch House with slow, heavy beats, droning synths, and pitched-down vocal samples', category: 'Electronic Genre' },
  { text: 'Deep and hypnotic ambient techno with spacious reverb, subtle textures, and a soft, continuous kick', category: 'Electronic Genre' },


  // --- ELECTRONIC ELEMENTS ---
  { text: 'A powerful, rumbling techno kick drum, hitting on every beat', category: 'Electronic Drums' },
  { text: 'The classic Amen Break, chopped and rearranged at a high tempo', category: 'Electronic Drums' },
  { text: 'A heavy, growling dubstep bass synth with complex modulation (FM, wavetable)', category: 'Electronic Bass' },
  { text: 'A deep and evolving Reese bassline, created by two detuned sawtooth waves, for Drum & Bass', category: 'Electronic Bass' },
  { text: 'An iconic, squelchy, and resonant acid bassline from a TB-303 synthesizer', category: 'Electronic Bass' },
  { text: 'Bright, layered supersaw chords, a staple of trance and future bass music', category: 'Electronic Melodic' },
  { text: 'A classic "hoover" synth sound, aggressive and detuned, from an Alpha Juno synthesizer', category: 'Electronic Synth' },
  { text: 'A gated synthesizer pad, creating a rhythmic, pulsating texture, classic 80s and trance sound', category: 'Electronic Synth' },
  { text: 'A rolling, off-beat psytrance bassline pattern', category: 'Electronic Bass' },
  { text: 'A distorted and punchy gabber kick drum at a very high tempo', category: 'Electronic Drums' },
  { text: 'Spacey and delayed dub techno with echoing chord stabs and a deep, subby kick drum', category: 'Electronic Melodic' },
  { text: 'IDM (Intelligent Dance Music) with complex, glitchy, and unpredictable rhythmic patterns', category: 'Electronic Drums' },
  { text: 'A bleeping and blooping modular synthesizer sequence with a random, generative feel', category: 'Electronic Synth' },

  // --- ROCK & METAL ---
  { text: 'High-energy 90s alternative rock with fuzzy, distorted guitars, a driving bassline, and powerful drums', category: 'Rock Genre' },
  { text: 'Jangly and upbeat indie rock with clean, chorus-effected guitars and a simple, catchy melody', category: 'Rock Genre' },
  { text: 'Dark and atmospheric post-punk with a prominent, melodic bassline, angular guitars, and a robotic drum machine', category: 'Rock Genre' },
  { text: 'Swirling and ethereal shoegaze with layers of distorted, reverb-drenched guitars creating a wall of sound', category: 'Rock Genre' },
  { text: 'Fast, aggressive, and palm-muted thrash metal riff with double-bass drumming', category: 'Metal Genre' },
  { text: 'Brutal and guttural death metal with lightning-fast blast beat drums and low-tuned, heavily distorted guitars', category: 'Metal Genre' },
  { text: 'A polyrhythmic and rhythmically complex "djent" guitar riff with a heavily distorted, tight sound', category: 'Metal Guitar' },
  { text: 'A slow, heavy, and down-tuned sludge metal riff with a fuzzy, distorted bass guitar', category: 'Metal Genre' },
  { text: 'Fast and energetic pop-punk with simple power chords and an upbeat drum feel', category: 'Rock Genre' },
  { text: 'Classic 70s rock with a bluesy guitar riff, a Hammond organ, and a cowbell', category: 'Rock Genre' },
  { text: 'Slow, heavy, and fuzzy stoner rock with a hypnotic, repetitive guitar riff', category: 'Rock Genre' },
  { text: 'Raw, atmospheric black metal with high-pitched shrieking vocals and fast tremolo-picked guitars', category: 'Metal Genre' },
  { text: 'Progressive metal with complex time signatures, technical guitar solos, and intricate song structures', category: 'Metal Genre' },

  // --- ROCK & METAL ELEMENTS ---
  { text: 'A wall of sound effect created by multiple layers of fuzzy, distorted, and sustained guitars', category: 'Rock Guitar' },
  { text: 'A death metal blast beat with rapid-fire kick and snare drums', category: 'Metal Drums' },
  { text: 'Twangy surf rock guitar with heavy spring reverb', category: 'Rock Guitar' },

  // --- BLUES, COUNTRY, FOLK ---
  { text: 'Gritty and raw Delta blues with a lone acoustic slide guitar and a soulful vocal', category: 'Blues Genre' },
  { text: 'Electrified Chicago blues band with a harmonica solo, driving rhythm section, and an electric guitar lead', category: 'Blues Genre' },
  { text: 'Modern country rock anthem with twangy telecaster guitars, a powerful female vocal, and a hard-hitting backbeat', category: 'Country Genre' },
  { text: 'Upbeat and fast-paced bluegrass with rapid banjo picking, fiddle melodies, and acoustic guitar strumming', category: 'Folk Genre' },
  { text: 'Traditional Appalachian folk music featuring a clawhammer banjo and a mournful fiddle', category: 'Folk Genre' },

  // --- WORLD & TRADITIONAL ---
  { text: 'Classic roots reggae with a one-drop drum beat, a deep bassline, and a rhythmic guitar skank', category: 'World Genre' },
  { text: 'Driving and energetic Afrobeat with a complex rhythm section, a horn section, and a funky guitar line', category: 'World Genre' },
  { text: 'A modern, percussive Amapiano track with a signature log drum bassline, shakers, and jazzy piano chords', category: 'World Genre' },
  { text: 'A romantic and smooth Bossa Nova with a gentle nylon string guitar, soft percussion, and a whispered vocal melody', category: 'World Genre' },
  { text: 'Energetic and festive salsa music with a syncopated piano montuno, powerful brass stabs, and a full Latin percussion section', category: 'World Genre' },
  { text: 'A passionate and rhythmic flamenco guitar performance with fast strums and percussive hits', category: 'World Genre' },
  { text: 'Lively Irish folk music with a fiddle, tin whistle, and a bodhrán drum', category: 'World Genre' },
  { text: 'Traditional Japanese music with a koto, shakuhachi flute, and taiko drums', category: 'World Genre' },
  { text: 'Mystical Middle Eastern music with an oud, swirling strings, and a darbuka hand drum rhythm', category: 'World Genre' },
  { text: 'A grand and colorful Bollywood film score with a dhol beat, sitar, and a large string section', category: 'World Genre' },

  // --- WORLD ELEMENTS ---
  { text: 'An iconic Reggaeton drum loop with the "dembow" rhythm', category: 'World Drums' },
  { text: 'The signature log drum bass sound from Amapiano music', category: 'World Drums' },
  { text: 'An intricate and meditative Sitar melody from Indian classical music', category: 'World Melodic' },
  { text: 'A rhythmic and percussive tabla drum pattern', category: 'World Percussion' },
  { text: 'A vibrant and festive mariachi brass section with trumpets and trombones', category: 'World Brass' },
  { text: 'A looping Baile Funk rhythm from Brazil, with a heavy kick drum and vocal samples', category: 'World Drums' },
  { text: 'A continuous, hypnotic drone from a didgeridoo', category: 'World Melodic' },
  { text: 'A bright and cheerful melody played on steel pans from Trinidad', category: 'World Melodic' },
  { text: 'A contemplative and serene melody on a Japanese Koto', category: 'World Melodic' },
  { text: 'A rhythmic and melodic line from an Arabic Oud', category: 'World Melodic' },
  { text: 'The sound of Scottish bagpipes playing a traditional march', category: 'World Wind' },

  // --- POP, CINEMATIC, and OTHER ---
  { text: 'Polished, high-energy K-Pop production with a catchy chorus, layered vocals, and a hard-hitting beat drop', category: 'Pop Genre' },
  { text: 'Classic 70s disco with a four-on-the-floor beat, a funky bassline, lush string arrangements, and soulful vocals', category: 'Pop Genre' },
  { text: 'An epic, sweeping cinematic orchestra score with dramatic strings, powerful brass, and thunderous percussion', category: 'Cinematic Genre' },
  { text: 'A tense and suspenseful horror movie soundscape with dissonant strings, eerie sound effects, and sudden piano stabs', category: 'Cinematic FX' },
  { text: 'A sentimental and emotional film score piece featuring a solo piano melody with a soft string orchestra background', category: 'Cinematic Keys' },
  { text: 'Pulsating and tense staccato strings, perfect for an action movie sequence', category: 'Cinematic Strings' },
  { text: 'Thunderous and epic orchestral percussion, featuring taiko drums and timpani', category: 'Cinematic Drums' },
  { text: 'An orchestral swell, building from silence to a powerful crescendo', category: 'Orchestral' },
  { text: 'Heroic and triumphant cinematic theme with a bold brass fanfare and a soaring string melody', category: 'Cinematic Genre' },
  { text: 'Mysterious and magical fantasy film score with a celeste, harp glissandos, and a choir', category: 'Cinematic Genre' },


  // --- INSTRUMENTS (stand-alone) ---
  // Drums & Percussion
  { text: 'A clean, tight, and well-recorded acoustic drum kit, playing a simple rock beat', category: 'Drums' },
  { text: 'The sound of a classic LinnDrum machine, characteristic of 80s pop music', category: 'Drums' },
  { text: 'A punchy kick drum with a sharp attack and minimal decay', category: 'Drums' },
  { text: 'A fat, deep snare drum sound with a touch of reverb, reminiscent of 80s rock ballads', category: 'Drums' },
  { text: 'A syncopated conga rhythm played by a percussionist', category: 'Percussion' },
  { text: 'A tambourine playing a steady 8th-note pattern', category: 'Percussion' },
  { text: 'A rhythmic cowbell pattern, suitable for funk or Latin music', category: 'Percussion' },
  { text: 'The iconic gated reverb snare drum sound from the 1980s', category: 'Drums' },
  { text: 'Classic Roland TR-808 drum machine loop with a booming kick and tight snare', category: 'Drums' },
  { text: 'Punchy and danceable Roland TR-909 drum machine beat', category: 'Drums' },

  // Bass
  { text: 'A deep, clean sine wave sub-bass, providing a powerful low-end foundation', category: 'Bass' },
  { text: 'A classic analog synthesizer bass sound from a Moog synthesizer, warm and round', category: 'Bass' },
  { text: 'A funky and percussive slap bassline on an electric bass', category: 'Bass' },
  { text: 'An upright acoustic bass playing a walking jazz line', category: 'Bass' },

  // Guitars
  { text: 'Warm, strummed acoustic guitar chords, perfect for a folk or pop song', category: 'Guitar' },
  { text: 'A fingerpicked acoustic guitar pattern, delicate and intricate', category: 'Guitar' },
  { text: 'A heavy metal guitar riff with high-gain distortion and palm-muting', category: 'Guitar' },
  { text: 'A clean electric guitar tone with chorus and reverb, ideal for indie pop or post-punk', category: 'Guitar' },
  { text: 'A soulful slide guitar melody with a bluesy feel', category: 'Guitar' },

  // Piano & Keys
  { text: 'A rich, expressive grand piano playing a classical melody', category: 'Keys' },
  { text: 'A mellow, warm Rhodes-style electric piano playing jazzy chords', category: 'Keys' },
  { text: 'A percussive and funky Wurlitzer electric piano riff', category: 'Keys' },
  { text: 'The powerful and majestic sound of a large church pipe organ', category: 'Keys' },
  { text: 'The ethereal and vintage sound of a Mellotron playing flute or string samples', category: 'Keys' },
  { text: 'A bright and tinkling celeste melody, like a music box', category: 'Keys' },
  { text: 'A classic honky-tonk upright piano, slightly out of tune for a western saloon feel', category: 'Keys' },

  // Synths
  { text: 'A soaring and heroic 80s-style synthesizer lead with a sawtooth wave', category: 'Synth' },
  { text: 'A lush, warm, and atmospheric analog synthesizer pad from a Juno-60', category: 'Synth' },
  { text: 'A bright, sparkling arpeggiated synth sequence, creating a sense of motion', category: 'Synth' },
  { text: 'A pluck synth melody with a short, percussive attack, common in electronic music', category: 'Synth' },
  { text: 'A gritty and evolving texture from a modular synthesizer', category: 'Synth' },
  { text: 'A shimmering, bell-like FM synthesis electric piano sound, like a Yamaha DX7', category: 'Synth' },
  { text: 'A complex, evolving pad made with wavetable synthesis', category: 'Synth' },
  { text: 'An atmospheric, abstract soundscape created with granular synthesis', category: 'Synth' },

  // Orchestral
  { text: 'A lush, emotional, and sweeping orchestral string section (violins, violas, cellos)', category: 'Strings' },
  { text: 'Short, sharp, and percussive pizzicato strings, playing a rhythmic pattern', category: 'Strings' },
  { text: 'A soaring and heroic French horn melody', category: 'Brass' },
  { text: 'A beautiful and expressive solo flute melody', category: 'Wind' },
  { text: 'A haunting and exotic shakuhachi flute solo from Japan', category: 'Wind' },
  { text: 'A majestic timpani roll, building tension', category: 'Orchestral' },
  { text: 'A dark and ominous cello drone', category: 'Strings' },
  { text: 'A shimmering and magical harp glissando', category: 'Orchestral' },

  // Vocals & FX
  { text: 'A haunting and beautiful Gregorian choir chant, sung by male voices', category: 'Vocal Textures' },
  { text: 'A deep, resonant Mongolian throat singing drone', category: 'Vocal Textures' },
  { text: 'A dramatic and powerful operatic soprano vocal swell', category: 'Vocal Textures' },
  { text: 'An upward-sweeping riser sound effect, building energy for a drop', category: 'FX' },
  { text: 'The sound of ocean waves crashing on a shore, a relaxing field recording', category: 'FX' },
  { text: 'A long, washing reverb tail, creating a huge sense of space', category: 'FX' },
  { text: 'A rhythmic, echoing ping-pong delay effect that bounces between stereo channels', category: 'FX' },
  { text: 'A pulsating sidechain compression effect, making pads "duck" in time with a kick drum', category: 'Technique' },

  // --- MOODS & STYLES (more descriptive) ---
  { text: 'A dark and ominous mood, with low drones, dissonant strings, and a slow tempo', category: 'Mood' },
  { text: 'An energetic and euphoric feeling, with a fast tempo, uplifting synth chords, and a driving beat', category: 'Mood' },
  { text: 'A dreamy, ethereal, and atmospheric mood with floating pads, gentle arpeggios, and lots of reverb', category: 'Mood' },
  { text: 'A melancholic and introspective mood, with a slow piano melody, soft strings, and a sense of longing', category: 'Mood' },
  { text: 'A funky and groovy feeling with a tight rhythm section, syncopated bass, and wah-wah guitar', category: 'Mood' },
  { text: 'A minimalist and sparse arrangement, with only a few key elements and lots of empty space', category: 'Style' },
  { text: 'A vintage and retro style, using sounds and production techniques from the 1970s', category: 'Style' },
  { text: 'A futuristic and robotic style, with digital synths, glitchy effects, and a mechanical rhythm', category: 'Style' },
  { text: 'A peaceful and serene atmosphere with gentle sounds, slow melodies, and no harsh elements', category: 'Mood' },
  { text: 'An epic and triumphant mood, with a full orchestra, heroic brass fanfares, and powerful percussion', category: 'Mood' },
  { text: 'A mysterious and suspenseful atmosphere, perfect for a spy thriller, with tense strings and a subtle beat', category: 'Mood' },
  { text: 'A joyful, celebratory, and uplifting feeling with bright horns, happy melodies, and an energetic rhythm', category: 'Mood' },
  { text: 'An aggressive and confrontational mood with distorted sounds, a fast tempo, and a powerful, in-your-face beat', category: 'Mood' },
  { text: 'A hypnotic and meditative state, with repetitive patterns, drones, and a deep, steady pulse', category: 'Mood' },
  { text: 'A feeling of nostalgia and bittersweet memories, with warm, slightly detuned sounds and a slow, sentimental melody', category: 'Mood' },

  // --- BRAZILIAN FUNK / PHONK ---
  { text: 'Dark Brazilian Phonk at 145 BPM with a heavily distorted 808 bass, rapid-fire hi-hats, a menacing cowbell melody, and pitched-down Portuguese vocals.', category: 'Brazilian Funk Genre' },
  { text: 'High-energy Funk Rave from São Paulo at 150 BPM, with a hard 4/4 kick, syncopated rave synth stabs, and intense Portuguese hype vocals.', category: 'Brazilian Funk Genre' },
  { text: 'Chill and atmospheric Baile Funk at 120 BPM, with a laid-back Tamborzão beat, melodic synth pads, and reverb-drenched vocal chops.', category: 'Brazilian Funk Genre' },
  { text: 'Hypnotic Funk Mandelão beat at 130 BPM with a stripped-back, repetitive kick pattern, a single resonant synth stab, and looped acapella samples.', category: 'Brazilian Funk Genre' },
  { text: 'Gritty phonk drum loop with saturated kicks, roll-off snares, and crisp hi-hats at 140 BPM.', category: 'Brazilian Funk Elements' },
  { text: 'Pitched-down and chopped Portuguese male vocal sample, creating a dark, rhythmic texture.', category: 'Brazilian Funk Elements' },
  { text: 'Aggressive Brazilian Phonk at 150 BPM with a distorted cowbell melody, heavy sliding 808s, and Portuguese rave stabs', category: 'Brazilian Funk Genre' },
  { text: 'Classic Rio Baile Funk at 130 BPM featuring the Tamborzão drum loop, call-and-response vocals, and a simple synth lead', category: 'Brazilian Funk Genre' },
  { text: 'Modern Brazilian Funk Rave track with a hard-hitting 4/4 kick, acid synth lines, and energetic Portuguese hype vocals', category: 'Brazilian Funk Genre' },
  { text: 'Melodic and soulful Brazilian Funk with smooth electric piano chords, a groovy bassline, and clean, punchy drums, at 125 BPM', category: 'Brazilian Funk Genre' },
  { text: 'Minimalist and percussive Funk Mandelão with a repetitive kick pattern, sparse synth stabs, and rhythmic vocal samples', category: 'Brazilian Funk Genre' },
  { text: 'The classic Tamborzão Baile Funk drum rhythm, heavy and syncopated', category: 'Brazilian Funk Elements' },
  { text: 'A distorted and aggressive cowbell melody, typical of Brazilian Phonk', category: 'Brazilian Funk Elements' },
  { text: 'Energetic Portuguese call-and-response hype vocals for Baile Funk', category: 'Brazilian Funk Elements' },
  { text: 'A classic gunshot sound effect, used as a percussive hit in Baile Funk', category: 'Brazilian Funk Elements' },
  { text: 'A heavy, sliding 808 bassline with distortion, perfect for Brazilian Phonk', category: 'Brazilian Funk Elements' },
];

function buildPlaygroundPrompts() {
  const prompts = new Map<string, Prompt>();

  for (let i = 0; i < PROMPT_DEFINITIONS.length; i++) {
    const promptId = `prompt-${i}`;
    const prompt = PROMPT_DEFINITIONS[i];
    prompts.set(promptId, {
      promptId,
      text: prompt.text,
      weight: 0,
      cc: i,
      color: PALETTE[i % PALETTE.length],
      category: prompt.category,
    });
  }

  return prompts;
}

const servicesData = [
    {
      title: 'AI Composition',
      description: 'Real-time music and soundscape generation using state-of-the-art AI models, perfect for creating dynamic and adaptive audio experiences.',
      image: 'https://images.unsplash.com/photo-1617957743097-0d20aa2ea762?q=80&w=2940&auto=format&fit=crop'
    },
    {
      title: 'Audio Synthesis',
      description: 'High-fidelity audio creation from text prompts and creative parameters, allowing for unparalleled control over sound design.',
      image: 'https://images.unsplash.com/photo-1510915361894-db8b60106cb1?q=80&w=2940&auto=format&fit=crop'
    },
    {
      title: 'Dimensional Analysis',
      description: 'Forensic analysis of audio properties, structure, and emotional content, providing deep insights into any sound sample.',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?q=80&w=2825&auto=format&fit=crop&ixlib=rb-4.0.3'
    },
    {
      title: 'Reverse Engineering',
      description: 'Deconstructing existing audio to understand its constituent parts, styles, and production techniques for inspiration or analysis.',
      image: 'https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?q=80&w=2940&auto=format&fit=crop'
    },
    {
      title: 'Inspection',
      description: 'Detailed audio quality inspection for artifacts, compliance, and mastering standards, ensuring your audio is flawless.',
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2940&auto=format&fit=crop'
    },
    {
      title: 'Creative Playground',
      description: 'Interactive real-time environments for audio exploration and creation, offering a sandbox for boundless sonic creativity.',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3'
    }
];

/**
 * The main application shell. It renders the header and routes between pages.
 */
@customElement('app-shell')
class AppShell extends LitElement {
  @state() private route = window.location.hash || '#home';
  @state() private isPageTransitioning = false;
  @state() private isMenuOpen = false;

  private observer: IntersectionObserver | null = null;

  private updateBodyClass() {
    if (this.route === '#playground') {
      document.body.classList.add('playground-page');
    } else {
      document.body.classList.remove('playground-page');
    }
  }

  private scrollToTop() {
    // Scroll to top of the page smoothly
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // WebGL hero properties
  @query('#hero-canvas') private heroCanvas!: HTMLCanvasElement | null;
  private gl: WebGLRenderingContext | null = null;
  private glProgram: WebGLProgram | null = null;
  private glTimeLocation: WebGLUniformLocation | null = null;
  private glResolutionLocation: WebGLUniformLocation | null = null;
  private glMouseLocation: WebGLUniformLocation | null = null;
  private mousePos = { x: 0.5, y: 0.5 };
  private animationFrameId: number | null = null;

  // Custom cursor properties
  @query('#custom-cursor') private customCursor!: HTMLElement | null;

  // Services section interactive elements
  @queryAll('.services-list li') private servicesListItems!: NodeListOf<HTMLElement>;
  @queryAll('.service-description-panel p') private servicesDescriptions!: NodeListOf<HTMLElement>;
  @queryAll('.service-image') private servicesImages!: NodeListOf<HTMLElement>;

  constructor() {
    super();

    // Apply initial body class
    this.updateBodyClass();

    window.addEventListener('hashchange', () => {
      this.isMenuOpen = false; // Close menu on navigation
      if (this.route === (window.location.hash || '#home')) return;

      this.isPageTransitioning = true;
      setTimeout(() => {
          this.route = window.location.hash || '#home';
          this.isPageTransitioning = false;

          // Update body class for proper styling
          this.updateBodyClass();

          // Scroll to top of page when navigating
          this.scrollToTop();

          if (this.route === '#home') {
            // Re-initialize observers and effects for the home page
             Promise.resolve().then(() => {
                this.initializeObserver();
                this.initializeInteractiveEffects();
                this.initializeServiceHoverEffect();
                this.initWebGL();
            });
          } else {
             this.cleanupWebGL();
          }

      }, 500); // Match CSS transition duration
    });
  }

  override firstUpdated() {
    if (this.route === '#home') {
        this.initializeObserver();
        this.initializeInteractiveEffects();
        this.initializeServiceHoverEffect();
        this.initWebGL();
    }
    this.initCustomCursor();
  }

  override disconnectedCallback() {
    super.disconnectedCallback();
    this.observer?.disconnect();
    this.cleanupWebGL();
    window.removeEventListener('mousemove', this.updateMousePos);
    window.removeEventListener('mousemove', this.updateCustomCursor);
  }

  private initCustomCursor() {
    window.addEventListener('mousemove', this.updateCustomCursor);
    // Debounce adding these listeners to ensure elements are available
    setTimeout(() => {
        this.shadowRoot?.querySelectorAll('a, button, .case-study-card').forEach(el => {
            el.addEventListener('mouseenter', () => this.customCursor?.classList.add('hover'));
            el.addEventListener('mouseleave', () => this.customCursor?.classList.remove('hover'));
        })
    }, 100);
  }

  private updateCustomCursor = (e: MouseEvent) => {
    if (this.customCursor) {
        this.customCursor.style.transform = `translate(${e.clientX}px, ${e.clientY}px)`;
    }
  }

  private initializeObserver() {
    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };

    this.observer = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('is-visible');
          observer.unobserve(entry.target);
        }
      });
    }, options);

    const sections = this.shadowRoot?.querySelectorAll('.animated-section');
    sections?.forEach(section => {
      this.observer?.observe(section);
    });
  }

  private initializeInteractiveEffects() {
    this.shadowRoot?.querySelectorAll('.case-study-card').forEach(card => {
        card.addEventListener('mousemove', this.handleCaseStudyCardMouseMove);
        card.addEventListener('mouseleave', this.handleCaseStudyCardMouseLeave);
    });
  }

  private initializeServiceHoverEffect() {
    if (this.servicesListItems.length === 0) return;

    const setActive = (index: number) => {
        this.servicesListItems.forEach((item, i) => item.classList.toggle('active', i === index));
        this.servicesDescriptions.forEach((item, i) => item.classList.toggle('active', i === index));
        this.servicesImages.forEach((item, i) => item.classList.toggle('active', i === index));
    };

    this.servicesListItems.forEach((item, index) => {
        item.addEventListener('mouseover', () => setActive(index));
        item.addEventListener('click', () => setActive(index)); // For touch devices
    });

    // Set initial active state
    setActive(0);
  }

  private handleCaseStudyCardMouseMove = (e: Event) => {
    const mouseEvent = e as MouseEvent;
    const card = mouseEvent.currentTarget as HTMLElement;
    const arrow = card.querySelector('.card-arrow') as HTMLElement;
    if (!arrow) return;

    const rect = card.getBoundingClientRect();
    const x = mouseEvent.clientX - rect.left;
    const y = mouseEvent.clientY - rect.top;

    const arrowRect = arrow.getBoundingClientRect();
    const arrowCenterX = (arrowRect.left - rect.left) + arrowRect.width / 2;
    const arrowCenterY = (arrowRect.top - rect.top) + arrowRect.height / 2;

    const deltaX = x - arrowCenterX;
    const deltaY = y - arrowCenterY;

    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const maxDist = 200;

    if (distance < maxDist) {
        const pullFactor = (maxDist - distance) / maxDist;
        arrow.style.transform = `translate(${deltaX * -0.4 * pullFactor}px, ${deltaY * -0.4 * pullFactor}px) scale(1)`;
        arrow.style.transition = 'transform 0.1s ease-out';
    } else {
        arrow.style.transform = `translate(0,0) scale(1)`;
    }
  };

  private handleCaseStudyCardMouseLeave = (e: Event) => {
    const card = e.currentTarget as HTMLElement;
    const arrow = card.querySelector('.card-arrow') as HTMLElement;
    if (arrow) {
        arrow.style.transform = 'translate(0,0) scale(1)';
        arrow.style.transition = 'transform 0.3s ease';
    }
  };

  private initWebGL() {
      if (!this.heroCanvas) return;
      this.gl = this.heroCanvas.getContext('webgl');
      if (!this.gl) {
          console.error("WebGL not supported!");
          return;
      }

      const gl = this.gl;

      const vertexShaderSource = `
          attribute vec2 a_position;
          void main() {
              gl_Position = vec4(a_position, 0.0, 1.0);
          }
      `;
      const fragmentShaderSource = `
          precision highp float;
          uniform vec2 u_resolution;
          uniform float u_time;
          uniform vec2 u_mouse;

          float random (in vec2 st) {
              return fract(sin(dot(st.xy, vec2(12.9898,78.233))) * 43758.5453123);
          }

          float noise (in vec2 st) {
              vec2 i = floor(st);
              vec2 f = fract(st);
              float a = random(i);
              float b = random(i + vec2(1.0, 0.0));
              float c = random(i + vec2(0.0, 1.0));
              float d = random(i + vec2(1.0, 1.0));
              vec2 u = f*f*(3.0-2.0*f);
              return mix(a, b, u.x) + (c - a)* u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
          }

          #define NUM_OCTAVES 5
          float fbm ( in vec2 st) {
              float v = 0.0;
              float a = 0.5;
              vec2 shift = vec2(100.0);
              mat2 rot = mat2(cos(0.5), sin(0.5), -sin(0.5), cos(0.50));
              for (int i = 0; i < NUM_OCTAVES; ++i) {
                  v += a * noise(st);
                  st = rot * st * 2.0 + shift;
                  a *= 0.5;
              }
              return v;
          }

          void main() {
              vec2 st = gl_FragCoord.xy/u_resolution.xy;
              st.x *= u_resolution.x/u_resolution.y;

              vec2 mouse_normalized = u_mouse / u_resolution;
              float dist_to_mouse = distance(st, mouse_normalized * vec2(u_resolution.x/u_resolution.y, 1.0));

              vec3 color = vec3(0.0);

              vec2 q = vec2(0.);
              q.x = fbm( st + 0.00*u_time );
              q.y = fbm( st + vec2(1.0) );

              vec2 r = vec2(0.);
              r.x = fbm( st + 1.0*q + vec2(1.7,9.2)+ 0.15*u_time );
              r.y = fbm( st + 1.0*q + vec2(8.3,2.8)+ 0.126*u_time);

              float f = fbm(st+r);

              color = mix(vec3(0.06, 0.10, 0.12), // Dark blue/black
                          vec3(0.1, 0.3, 0.4),  // Teal/blue
                          clamp((f*f)*4.0,0.0,1.0));

              color = mix(color,
                          vec3(0.99, 0.93, 0.40), // Yellow accent
                          clamp(length(q),0.0,1.0));

              color = mix(color,
                          vec3(0.8, 0.9, 1.0), // White highlights
                          clamp(length(r.x),0.0,1.0));

              color = (f*f*f+.6*f*f+.5*f)*color;

              // Add mouse interaction
              float mouse_effect = 1.0 - smoothstep(0.0, 0.2, dist_to_mouse);
              color += mouse_effect * 0.2;

              gl_FragColor = vec4(color, 1.0);
          }
      `;

      const vertexShader = this.compileShader(vertexShaderSource, gl.VERTEX_SHADER);
      const fragmentShader = this.compileShader(fragmentShaderSource, gl.FRAGMENT_SHADER);

      this.glProgram = gl.createProgram();
      if (!this.glProgram || !vertexShader || !fragmentShader) return;

      gl.attachShader(this.glProgram, vertexShader);
      gl.attachShader(this.glProgram, fragmentShader);
      gl.linkProgram(this.glProgram);
      gl.useProgram(this.glProgram);

      const positionBuffer = gl.createBuffer();
      gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
      gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([-1,-1, 1,-1, -1,1, 1,1]), gl.STATIC_DRAW);
      const positionAttributeLocation = gl.getAttribLocation(this.glProgram, 'a_position');
      gl.enableVertexAttribArray(positionAttributeLocation);
      gl.vertexAttribPointer(positionAttributeLocation, 2, gl.FLOAT, false, 0, 0);

      this.glTimeLocation = gl.getUniformLocation(this.glProgram, 'u_time');
      this.glResolutionLocation = gl.getUniformLocation(this.glProgram, 'u_resolution');
      this.glMouseLocation = gl.getUniformLocation(this.glProgram, 'u_mouse');

      window.addEventListener('mousemove', this.updateMousePos);

      this.animateGL();
  }

  private compileShader(source: string, type: number) {
      if (!this.gl) return null;
      const shader = this.gl.createShader(type);
      if (!shader) return null;
      this.gl.shaderSource(shader, source);
      this.gl.compileShader(shader);
      if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
          console.error('Shader compile error:', this.gl.getShaderInfoLog(shader));
          return null;
      }
      return shader;
  }

  private updateMousePos = (e: MouseEvent) => {
    if (this.heroCanvas) {
        const rect = this.heroCanvas.getBoundingClientRect();
        this.mousePos = { x: e.clientX - rect.left, y: rect.height - (e.clientY - rect.top) };
    }
  }

  private animateGL = (time: number = 0) => {
      if (!this.gl || !this.glProgram || !this.heroCanvas) return;

      const gl = this.gl;
      this.heroCanvas.width = this.heroCanvas.clientWidth;
      this.heroCanvas.height = this.heroCanvas.clientHeight;
      gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);

      gl.uniform1f(this.glTimeLocation, time * 0.0002);
      gl.uniform2f(this.glResolutionLocation, gl.canvas.width, gl.canvas.height);
      gl.uniform2f(this.glMouseLocation, this.mousePos.x, this.mousePos.y);

      gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);

      this.animationFrameId = requestAnimationFrame(this.animateGL);
  }

  private cleanupWebGL() {
      if (this.animationFrameId) {
          cancelAnimationFrame(this.animationFrameId);
      }
      this.gl = null;
      this.glProgram = null;
      window.removeEventListener('mousemove', this.updateMousePos);
  }

  private renderPage() {
    const pageClasses = classMap({
        'page-wrapper': true,
        'page-exit': this.isPageTransitioning
    });

    return html`
    <div class=${pageClasses}>
        ${(() => {
            switch (this.route) {
                case '#playground':
                    return html`<prompt-dj-midi-wrapper></prompt-dj-midi-wrapper>`;
                case '#about':
                    // Initialize after DOM is rendered
                    setTimeout(() => {
                        this.initializeObserver();
                        this.initializeInteractiveEffects();
                    }, 100);
                    return this.renderAboutPage();
                case '#services':
                    // Initialize after DOM is rendered
                    setTimeout(() => {
                        this.initializeObserver();
                        this.initializeInteractiveEffects();
                    }, 100);
                    return this.renderServicesPage();
                case '#ai-composition':
                    // Initialize after DOM is rendered
                    setTimeout(() => {
                        this.initializeObserver();
                        this.initializeInteractiveEffects();
                    }, 100);
                    return this.renderAiCompositionPage();
                case '#audio-synthesis':
                    // Initialize after DOM is rendered
                    setTimeout(() => {
                        this.initializeObserver();
                        this.initializeInteractiveEffects();
                    }, 100);
                    return this.renderAudioSynthesisPage();
                case '#analysis':
                    // Initialize after DOM is rendered
                    setTimeout(() => {
                        this.initializeObserver();
                        this.initializeInteractiveEffects();
                    }, 100);
                    return this.renderAnalysisPage();
                case '#inspection':
                    // Initialize after DOM is rendered
                    setTimeout(() => {
                        this.initializeObserver();
                        this.initializeInteractiveEffects();
                    }, 100);
                    return this.renderInspectionPage();
                case '#contact':
                    // Initialize after DOM is rendered
                    setTimeout(() => {
                        this.initializeObserver();
                        this.initializeInteractiveEffects();
                    }, 100);
                    return this.renderContactPage();
                case '#home':
                default:
                    // This promise ensures observers/effects are set up AFTER the DOM is rendered
                    Promise.resolve().then(() => {
                        this.initializeObserver();
                        this.initializeInteractiveEffects();
                        this.initializeServiceHoverEffect();
                        this.initWebGL();
                    });
                    return this.renderHomePage();
                }
        })()}
    </div>`;
  }

  override render() {
    const mainClasses = classMap({
        'page-content': true,
        'menu-open': this.isMenuOpen
    });

    return html`
      <div id="custom-cursor"></div>
      ${this.renderHeader()}
      ${this.isMenuOpen ? this.renderMobileMenu() : ''}
      <main class=${mainClasses}>
        ${this.renderPage()}
      </main>
      ${this.route !== '#playground' ? this.renderFooter() : ''}
    `;
  }

  private toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
  }

  private renderMobileMenu() {
    return html`
      <nav class="mobile-nav">
        <a href="#home" class="mobile-nav-link">Home</a>
        <a href="#playground" class="mobile-nav-link">Playground</a>
        <a href="#about" class="mobile-nav-link">About Us</a>
        <a href="#services" class="mobile-nav-link">Services</a>
        <a href="#contact" class="mobile-nav-link">Contact</a>
      </nav>
    `;
  }

  private renderHeader() {
    if (this.route === '#playground') {
        return html``;
    }

    const headerClasses = classMap({
        'site-header': true,
        'menu-open': this.isMenuOpen,
    });

    return html`
      <header class=${headerClasses}>
        <a href="#home" class="site-logo" aria-label="PixelVane Home">
            <svg width="40" height="40" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M30 85V15H60C73.8071 15 85 26.1929 85 40C85 53.8071 73.8071 65 60 65H45V85H30Z" fill="white"/>
                <path d="M45 65L70 40L45 15V35L60 40L45 45V65Z" fill="#061a1f"/>
            </svg>
        </a>
        <nav class="header-nav">
          <a href="#playground" class="nav-button login-button">
            Playground
          </a>
          <button @click=${this.toggleMenu} class="nav-button menu-button" aria-label=${this.isMenuOpen ? "Close Menu" : "Open Menu"}>
            <div class="menu-icon-bars">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
          </button>
        </nav>
      </header>
    `;
  }

  private renderHomePage() {
    const scrollToContent = () => {
        this.shadowRoot?.querySelector('.content-section')?.scrollIntoView({ behavior: 'smooth' });
    }

    const partnerLogos = ['SEM', 'ANDREWS SURVEY', 'caley', 'DEEP', 'FAST', 'NVIDIA', 'ORACLE', 'G-CLOUD'];

    const caseStudyCardsData = [
        {
            image: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?q=80&w=2070&auto=format&fit=crop',
            category: 'Film Scoring',
            title: 'Generative Soundtrack for Indie Sci-Fi Film \'Echoes of Andromeda\''
        },
        {
            image: 'https://images.unsplash.com/photo-1516223725307-6f76b9ec8742?q=80&w=2072&auto=format&fit=crop',
            category: 'Live Performance',
            title: 'Interactive Audio-Visual Installation at Digital Arts Festival'
        },
        {
            image: 'https://images.unsplash.com/photo-1497366216548-37526070297c?q=80&w=2069&auto=format&fit=crop&ixlib=rb-4.0.3',
            category: 'Brand Identity',
            title: 'Generative Audio Environments for Corporate Soundscaping'
        }
    ];

    return html`
      <section class="hero-section">
        <canvas id="hero-canvas"></canvas>
        <div class="hero-content">
            <h1>Specialists in AI music, audio generation and creative analysis.</h1>
        </div>
        <div class="hero-footer">
            <div class="hero-footer-left">
                <p>Unparalleled precision and<br>forensically accurate reports.</p>
            </div>
            <div class="hero-footer-right">
                <button @click=${scrollToContent} class="explore-button">
                    Explore
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 5v14m-7-7l7 7 7-7"/></svg>
                </button>
            </div>
        </div>
      </section>

      <section class="content-section why-us-section animated-section">
        <div class="why-us-background"></div>
        <div class="section-container-wide why-us-content">
            <div class="section-label">Why PixelVane?</div>
            <h2 class="section-heading">We are pioneers in the digital soundscape, transforming abstract concepts into forensically accurate generative audio.</h2>
            <div class="why-us-description">
                <p>We are full-service providers of generative audio and creative analysis. Our expertise in high-accuracy audio synthesis and dimensional reporting allows us to serve mission-critical industries where precision is paramount.</p>
                <p>Our capabilities include using your prompts, our proprietary models, or fine-tuned systems to perform uncompromised, sub-millisecond audio generation, as well as 'quick-pass' high-level data collection for larger sites.</p>
            </div>
            <a href="#about" class="section-button">About Us
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
            </a>
        </div>
      </section>

      <section class="partners-section animated-section">
          <div class="partners-scroller">
            <div class="partners-track">
                ${partnerLogos.map(logo => html`<div class="partner-logo">${logo}</div>`)}
                ${partnerLogos.map(logo => html`<div class="partner-logo">${logo}</div>`)}
            </div>
          </div>
      </section>

      <section class="content-section services-section-reimagined animated-section">
        <div class="services-container">
            <div class="section-label">What we do</div>
            <h2 class="section-heading">Our core services cover the full project lifecycle, from data capture and processing, to analysis and visualisation.</h2>
            <div class="services-interactive-wrapper">
                <ul class="services-list">
                    ${servicesData.map(service => html`<li><h3>${service.title}</h3></li>`)}
                </ul>
                <div class="services-visuals">
                    <div class="service-description-panel">
                        ${servicesData.map(service => html`<p>${service.description}</p>`)}
                    </div>
                    <div class="service-image-panel">
                        ${servicesData.map(service => html`
                            <div class="service-image" style="background-image: url(${service.image})"></div>
                        `)}
                    </div>
                </div>
            </div>
            <a href="#services" class="section-button">View all services
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
            </a>
        </div>
      </section>

      <section class="software-section animated-section">
        <div class="software-content">
            <div class="section-label">Software</div>
            <h2 class="section-heading">Powerful tools to give you full control of the creative process.</h2>
            <p class="section-description">Our proprietary software provides an intuitive interface for interacting with generative models, enabling precise control and real-time feedback.</p>
            <a href="#playground" class="section-button">Learn more
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
            </a>
        </div>
      </section>

      <section class="content-section card-section animated-section">
        <div class="section-container-wide">
            <div class="card-section-header">
                <div>
                    <div class="section-label">Case Studies</div>
                    <h2 class="section-heading">Explore our recent work.</h2>
                </div>
                <a href="#services" class="section-button">View all
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
                </a>
            </div>
            <div class="case-studies-grid">
                ${caseStudyCardsData.map((card) => html`
                    <a href="#services" class="case-study-card">
                        <div class="card-background" style="background-image: url(${card.image})"></div>
                        <div class="card-content">
                            <div>
                                <span class="card-category">${card.category}</span>
                                <h3 class="card-title">${card.title}</h3>
                            </div>
                            <div class="card-arrow">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
                            </div>
                        </div>
                    </a>
                `)}
            </div>
        </div>
      </section>

      <section class="content-section cta-section animated-section">
        <div class="section-container">
          <h2 class="section-heading">Let's create the future of audio together.</h2>
          <a href="#contact" class="section-button">Contact Us
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
          </a>
        </div>
      </section>
    `;
  }

  private renderAboutPage() {
    return html`
      <section class="page-hero">
        <div class="page-hero-content">
          <h1>About PixelVane</h1>
          <p>Pioneering the future of AI-powered music and audio generation with cutting-edge technology and creative innovation.</p>
        </div>
      </section>

      <section class="content-section about-content animated-section">
        <div class="section-container">
          <div class="about-grid">
            <div class="about-text">
              <div class="section-label">Our Story</div>
              <h2>Transforming Audio Through AI Innovation</h2>
              <p>Founded with a vision to revolutionize the audio industry, PixelVane combines state-of-the-art artificial intelligence with deep musical understanding. Our team of audio engineers, AI researchers, and creative professionals work together to push the boundaries of what's possible in music and sound generation.</p>
              <p>We believe that AI should enhance human creativity, not replace it. Our tools are designed to inspire artists, producers, and creators by providing them with unprecedented control over audio generation and analysis.</p>
            </div>
            <div class="about-image">
              <div class="image-placeholder">
                <svg width="100" height="100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                  <path d="M9 18V5l12-2v13"></path>
                  <circle cx="6" cy="18" r="3"></circle>
                  <circle cx="18" cy="16" r="3"></circle>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section>


      <section class="content-section values-section animated-section">
        <div class="section-container">
          <div class="section-label">Our Values</div>
          <h2 class="section-heading">What Drives Us</h2>
          <div class="values-grid">
            <div class="value-item">
              <div class="value-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                </svg>
              </div>
              <h3>Innovation</h3>
              <p>Constantly pushing the boundaries of what's possible in AI-powered audio generation.</p>
            </div>
            <div class="value-item">
              <div class="value-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
              </div>
              <h3>Collaboration</h3>
              <p>Working closely with artists and creators to understand their needs and challenges.</p>
            </div>
            <div class="value-item">
              <div class="value-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12,6 12,12 16,14"></polyline>
                </svg>
              </div>
              <h3>Precision</h3>
              <p>Delivering forensically accurate analysis and high-fidelity audio generation.</p>
            </div>
            <div class="value-item">
              <div class="value-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
              </div>
              <h3>Creativity</h3>
              <p>Empowering human creativity through intelligent AI tools and interfaces.</p>
            </div>
          </div>
        </div>
      </section>
    `;
  }

  private renderServicesPage() {
    return html`
      <section class="page-hero">
        <div class="page-hero-content">
          <h1>Our Services</h1>
          <p>Comprehensive AI-powered audio solutions for every stage of your creative journey.</p>
        </div>
      </section>

      <section class="content-section services-detail animated-section">
        <div class="section-container">
          <div class="services-detail-grid">
            ${servicesData.map(service => html`
              <div class="service-detail-card">
                <div class="service-detail-image" style="background-image: url(${service.image})"></div>
                <div class="service-detail-content">
                  <h3>${service.title}</h3>
                  <p>${service.description}</p>
                  <div class="service-features">
                    <h4>Key Features:</h4>
                    <ul>
                      ${service.title === 'AI Composition' ? html`
                        <li>Real-time music generation</li>
                        <li>Style-aware composition</li>
                        <li>Multi-instrument orchestration</li>
                        <li>Adaptive tempo and mood</li>
                      ` : ''}
                      ${service.title === 'Audio Synthesis' ? html`
                        <li>Text-to-audio generation</li>
                        <li>High-fidelity output</li>
                        <li>Custom parameter control</li>
                        <li>Multiple format support</li>
                      ` : ''}
                      ${service.title === 'Dimensional Analysis' ? html`
                        <li>Spectral analysis</li>
                        <li>Emotional content mapping</li>
                        <li>Structural decomposition</li>
                        <li>Forensic-grade accuracy</li>
                      ` : ''}
                      ${service.title === 'Reverse Engineering' ? html`
                        <li>Style extraction</li>
                        <li>Production technique analysis</li>
                        <li>Instrument separation</li>
                        <li>Composition breakdown</li>
                      ` : ''}
                      ${service.title === 'Inspection' ? html`
                        <li>Quality assessment</li>
                        <li>Artifact detection</li>
                        <li>Compliance checking</li>
                        <li>Mastering analysis</li>
                      ` : ''}
                      ${service.title === 'Creative Playground' ? html`
                        <li>Interactive environments</li>
                        <li>Real-time experimentation</li>
                        <li>Collaborative tools</li>
                        <li>Export capabilities</li>
                      ` : ''}
                    </ul>
                  </div>
                  <a href="#contact" class="service-cta">Get Started</a>
                </div>
              </div>
            `)}
          </div>
        </div>
      </section>

      <section class="content-section pricing-coming-soon animated-section">
        <div class="section-container">
          <div class="coming-soon-content">
            <div class="coming-soon-icon">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12,6 12,12 16,14"></polyline>
              </svg>
            </div>
            <div class="section-label">Pricing</div>
            <h2 class="section-heading">Coming Soon</h2>
            <p class="coming-soon-description">
              We're crafting the perfect pricing plans for our AI-powered audio services.
              Until then, enjoy <strong>free access</strong> to explore all our features and capabilities.
            </p>
            <div class="coming-soon-features">
              <div class="feature-highlight">
                <div class="feature-icon">🎵</div>
                <div class="feature-text">
                  <h4>Free Access</h4>
                  <p>Full access to all services during our preview period</p>
                </div>
              </div>
              <div class="feature-highlight">
                <div class="feature-icon">⚡</div>
                <div class="feature-text">
                  <h4>No Limits</h4>
                  <p>Unlimited usage while we finalize our pricing structure</p>
                </div>
              </div>
              <div class="feature-highlight">
                <div class="feature-icon">🚀</div>
                <div class="feature-text">
                  <h4>Early Access</h4>
                  <p>Be among the first to experience the future of AI audio</p>
                </div>
              </div>
            </div>
            <div class="coming-soon-cta">
              <a href="#playground" class="section-button primary">Try It Free Now</a>
              <a href="#contact" class="section-button secondary">Get Notified</a>
            </div>
          </div>
        </div>
      </section>
    `;
  }

  private renderContactPage() {
    return html`
      <section class="page-hero">
        <div class="page-hero-content">
          <h1>Contact Us</h1>
          <p>Ready to transform your audio projects? Let's discuss how PixelVane can help you achieve your creative goals.</p>
        </div>
      </section>

      <section class="content-section contact-content animated-section">
        <div class="section-container">
          <div class="contact-grid">
            <div class="contact-info">
              <div class="section-label">Get in Touch</div>
              <h2>Let's Create Something Amazing</h2>
              <p>Whether you're looking to integrate AI audio generation into your workflow, need custom solutions, or want to explore our services, we're here to help.</p>

              <div class="contact-methods">
                <div class="contact-method">
                  <div class="contact-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                  </div>
                  <div>
                    <h4>Email</h4>
                    <p><EMAIL></p>
                  </div>
                </div>

                <div class="contact-method">
                  <div class="contact-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                    </svg>
                  </div>
                  <div>
                    <h4>Phone</h4>
                    <p>+****************</p>
                  </div>
                </div>


              </div>
            </div>

            <div class="contact-form">
              <form class="contact-form-container">
                <div class="form-group">
                  <label for="name">Name *</label>
                  <input type="text" id="name" name="name" required>
                </div>

                <div class="form-group">
                  <label for="email">Email *</label>
                  <input type="email" id="email" name="email" required>
                </div>

                <div class="form-group">
                  <label for="company">Company</label>
                  <input type="text" id="company" name="company">
                </div>

                <div class="form-group">
                  <label for="service">Service Interest</label>
                  <select id="service" name="service">
                    <option value="">Select a service</option>
                    <option value="ai-composition">AI Composition</option>
                    <option value="audio-synthesis">Audio Synthesis</option>
                    <option value="dimensional-analysis">Dimensional Analysis</option>
                    <option value="reverse-engineering">Reverse Engineering</option>
                    <option value="inspection">Inspection</option>
                    <option value="creative-playground">Creative Playground</option>
                    <option value="custom">Custom Solution</option>
                  </select>
                </div>

                <div class="form-group">
                  <label for="message">Message *</label>
                  <textarea id="message" name="message" rows="5" required placeholder="Tell us about your project and how we can help..."></textarea>
                </div>

                <button type="submit" class="form-submit">Send Message</button>
              </form>
            </div>
          </div>
        </div>
      </section>


    `;
  }

  private renderAiCompositionPage() {
    return html`
      <section class="page-hero service-hero">
        <div class="hero-background">
          <div class="hero-image" style="background-image: url('https://images.unsplash.com/photo-1617957743097-0d20aa2ea762?q=80&w=2940&auto=format&fit=crop')"></div>
          <div class="hero-overlay"></div>
        </div>
        <div class="page-hero-content">
          <div class="service-badge animated-section">AI Composition</div>
          <h1>Intelligent Music Creation</h1>
          <p>Real-time music and soundscape generation using state-of-the-art AI models, perfect for creating dynamic and adaptive audio experiences.</p>
          <a href="#playground" class="hero-cta-button">Try It Now
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
          </a>
        </div>
      </section>

      <section class="content-section service-features animated-section">
        <div class="section-container">
          <div class="section-label">Capabilities</div>
          <h2 class="section-heading">What Makes Our AI Composition Special</h2>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9 18V5l12-2v13"></path>
                  <circle cx="6" cy="18" r="3"></circle>
                  <circle cx="18" cy="16" r="3"></circle>
                </svg>
              </div>
              <h3>Real-Time Generation</h3>
              <p>Sub-millisecond latency for live performance and interactive applications.</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                </svg>
              </div>
              <h3>Style-Aware</h3>
              <p>Understands and adapts to different musical genres and compositional styles.</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
                </svg>
              </div>
              <h3>Multi-Instrument</h3>
              <p>Full orchestration capabilities with intelligent arrangement and mixing.</p>
            </div>
          </div>
        </div>
      </section>

      <section class="content-section service-showcase animated-section">
        <div class="section-container">
          <div class="showcase-grid">
            <div class="showcase-content">
              <div class="section-label">Applications</div>
              <h2>Perfect For Every Creative Project</h2>
              <div class="use-cases-list">
                <div class="use-case-item">
                  <div class="use-case-icon">🎬</div>
                  <div>
                    <h4>Film & Television</h4>
                    <p>Dynamic scoring that adapts to narrative flow and emotional beats.</p>
                  </div>
                </div>
                <div class="use-case-item">
                  <div class="use-case-icon">🎮</div>
                  <div>
                    <h4>Video Games</h4>
                    <p>Interactive soundtracks that respond to player actions and game states.</p>
                  </div>
                </div>
                <div class="use-case-item">
                  <div class="use-case-icon">🎵</div>
                  <div>
                    <h4>Live Performance</h4>
                    <p>Real-time composition for concerts, installations, and interactive experiences.</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="showcase-visual">
              <div class="visual-grid">
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?q=80&w=2940&auto=format&fit=crop')"></div>
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=2940&auto=format&fit=crop')"></div>
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?q=80&w=2940&auto=format&fit=crop')"></div>
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1516280440614-37939bbacd81?q=80&w=2940&auto=format&fit=crop')"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section class="content-section cta-section animated-section">
        <div class="section-container">
          <h2 class="section-heading">Ready to create the future of music?</h2>
          <a href="#contact" class="section-button">Get Started
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
          </a>
        </div>
      </section>
    `;
  }

  private renderAudioSynthesisPage() {
    return html`
      <section class="page-hero service-hero">
        <div class="hero-background">
          <div class="hero-image" style="background-image: url('https://images.unsplash.com/photo-1510915361894-db8b60106cb1?q=80&w=2940&auto=format&fit=crop')"></div>
          <div class="hero-overlay"></div>
        </div>
        <div class="page-hero-content">
          <div class="service-badge animated-section">Audio Synthesis</div>
          <h1>Text-to-Audio Generation</h1>
          <p>High-fidelity audio creation from text prompts and creative parameters, allowing for unparalleled control over sound design.</p>
          <a href="#playground" class="hero-cta-button">Try It Now
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
          </a>
        </div>
      </section>

      <section class="content-section service-features animated-section">
        <div class="section-container">
          <div class="section-label">Technology</div>
          <h2 class="section-heading">Advanced Sound Synthesis</h2>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                  <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                  <line x1="12" y1="19" x2="12" y2="23"></line>
                  <line x1="8" y1="23" x2="16" y2="23"></line>
                </svg>
              </div>
              <h3>Natural Language</h3>
              <p>Describe sounds in plain English and watch them come to life instantly.</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9 12l2 2 4-4"></path>
                  <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                  <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                  <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"></path>
                  <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"></path>
                </svg>
              </div>
              <h3>High Fidelity</h3>
              <p>Professional quality output up to 96kHz/32-bit for pristine audio.</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="3"></circle>
                  <path d="M12 1v6m0 6v6"></path>
                  <path d="M21 12h-6m-6 0H3"></path>
                </svg>
              </div>
              <h3>Real-Time Control</h3>
              <p>Adjust parameters and hear changes instantly with live preview.</p>
            </div>
          </div>
        </div>
      </section>

      <section class="content-section service-showcase animated-section">
        <div class="section-container">
          <div class="showcase-grid">
            <div class="showcase-content">
              <div class="section-label">Applications</div>
              <h2>Endless Creative Possibilities</h2>
              <div class="use-cases-list">
                <div class="use-case-item">
                  <div class="use-case-icon">🎭</div>
                  <div>
                    <h4>Media Production</h4>
                    <p>Custom sound effects and atmospheric audio for films, podcasts, and content.</p>
                  </div>
                </div>
                <div class="use-case-item">
                  <div class="use-case-icon">🌊</div>
                  <div>
                    <h4>Ambient Soundscapes</h4>
                    <p>Immersive environmental audio for relaxation, focus, and meditation.</p>
                  </div>
                </div>
                <div class="use-case-item">
                  <div class="use-case-icon">🎹</div>
                  <div>
                    <h4>Instrument Synthesis</h4>
                    <p>Create unique instruments and textures that don't exist in the real world.</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="showcase-visual">
              <div class="visual-grid">
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?q=80&w=2940&auto=format&fit=crop')"></div>
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=2940&auto=format&fit=crop')"></div>
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?q=80&w=2940&auto=format&fit=crop')"></div>
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1516280440614-37939bbacd81?q=80&w=2940&auto=format&fit=crop')"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section class="content-section cta-section animated-section">
        <div class="section-container">
          <h2 class="section-heading">Transform your ideas into sound.</h2>
          <a href="#contact" class="section-button">Start Creating
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
          </a>
        </div>
      </section>
    `;
  }

  private renderAnalysisPage() {
    return html`
      <section class="page-hero service-hero">
        <div class="hero-background">
          <div class="hero-image" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?q=80&w=2825&auto=format&fit=crop&ixlib=rb-4.0.3')"></div>
          <div class="hero-overlay"></div>
        </div>
        <div class="page-hero-content">
          <div class="service-badge animated-section">Dimensional Analysis</div>
          <h1>Deep Audio Intelligence</h1>
          <p>Forensic analysis of audio properties, structure, and emotional content, providing deep insights into any sound sample.</p>
          <a href="#contact" class="hero-cta-button">Get Analysis
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
          </a>
        </div>
      </section>

      <section class="content-section service-features animated-section">
        <div class="section-container">
          <div class="section-label">Intelligence</div>
          <h2 class="section-heading">Comprehensive Audio Analysis</h2>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M3 3v18h18"></path>
                  <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
                </svg>
              </div>
              <h3>Spectral Analysis</h3>
              <p>Deep frequency domain insights and harmonic structure identification.</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="7.5,4.21 12,6.81 16.5,4.21"></polyline>
                  <polyline points="7.5,19.79 7.5,14.6 3,12"></polyline>
                  <polyline points="21,12 16.5,14.6 16.5,19.79"></polyline>
                  <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                  <line x1="12" y1="22.08" x2="12" y2="12"></line>
                </svg>
              </div>
              <h3>Emotional Content</h3>
              <p>AI-powered mood classification and emotional characteristic detection.</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12,6 12,12 16,14"></polyline>
                </svg>
              </div>
              <h3>Forensic Quality</h3>
              <p>Professional-grade analysis for authentication and quality assessment.</p>
            </div>
          </div>
        </div>
      </section>

      <section class="content-section service-showcase animated-section">
        <div class="section-container">
          <div class="showcase-grid">
            <div class="showcase-content">
              <div class="section-label">Applications</div>
              <h2>Unlock Hidden Audio Insights</h2>
              <div class="use-cases-list">
                <div class="use-case-item">
                  <div class="use-case-icon">🎚️</div>
                  <div>
                    <h4>Music Production</h4>
                    <p>Detailed analysis to guide mixing, mastering, and creative decisions.</p>
                  </div>
                </div>
                <div class="use-case-item">
                  <div class="use-case-icon">🔍</div>
                  <div>
                    <h4>Audio Forensics</h4>
                    <p>Authentication, source identification, and tampering detection.</p>
                  </div>
                </div>
                <div class="use-case-item">
                  <div class="use-case-icon">📊</div>
                  <div>
                    <h4>Content Classification</h4>
                    <p>Automated tagging and categorization for large audio libraries.</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="showcase-visual">
              <div class="visual-grid">
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2940&auto=format&fit=crop')"></div>
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?q=80&w=2940&auto=format&fit=crop')"></div>
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?q=80&w=2940&auto=format&fit=crop')"></div>
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=2940&auto=format&fit=crop')"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section class="content-section cta-section animated-section">
        <div class="section-container">
          <h2 class="section-heading">Unlock the secrets in your audio.</h2>
          <a href="#contact" class="section-button">Analyze Now
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
          </a>
        </div>
      </section>
    `;
  }

  private renderInspectionPage() {
    return html`
      <section class="page-hero service-hero">
        <div class="hero-background">
          <div class="hero-image" style="background-image: url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2940&auto=format&fit=crop')"></div>
          <div class="hero-overlay"></div>
        </div>
        <div class="page-hero-content">
          <div class="service-badge animated-section">Audio Inspection</div>
          <h1>Professional Quality Assurance</h1>
          <p>Detailed audio quality inspection for artifacts, compliance, and mastering standards, ensuring your audio is flawless.</p>
          <a href="#contact" class="hero-cta-button">Inspect Audio
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
          </a>
        </div>
      </section>

      <section class="content-section service-features animated-section">
        <div class="section-container">
          <div class="section-label">Quality Control</div>
          <h2 class="section-heading">Comprehensive Audio Inspection</h2>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14,2 14,8 20,8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10,9 9,9 8,9"></polyline>
                </svg>
              </div>
              <h3>Automated Detection</h3>
              <p>AI-powered identification of artifacts, distortion, and quality issues.</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9 12l2 2 4-4"></path>
                  <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                  <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                  <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"></path>
                  <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"></path>
                </svg>
              </div>
              <h3>Compliance Checking</h3>
              <p>Verify adherence to broadcast and streaming platform standards.</p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
                </svg>
              </div>
              <h3>Detailed Reports</h3>
              <p>Comprehensive analysis with visual representations and recommendations.</p>
            </div>
          </div>
        </div>
      </section>

      <section class="content-section service-showcase animated-section">
        <div class="section-container">
          <div class="showcase-grid">
            <div class="showcase-content">
              <div class="section-label">Applications</div>
              <h2>Professional Quality Control</h2>
              <div class="use-cases-list">
                <div class="use-case-item">
                  <div class="use-case-icon">🎛️</div>
                  <div>
                    <h4>Mastering & Post-Production</h4>
                    <p>Final quality checks before release to ensure professional standards.</p>
                  </div>
                </div>
                <div class="use-case-item">
                  <div class="use-case-icon">📺</div>
                  <div>
                    <h4>Broadcast Compliance</h4>
                    <p>Verify audio meets television and radio broadcasting requirements.</p>
                  </div>
                </div>
                <div class="use-case-item">
                  <div class="use-case-icon">💿</div>
                  <div>
                    <h4>Archive Validation</h4>
                    <p>Quality assessment for digitization and preservation projects.</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="showcase-visual">
              <div class="visual-grid">
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?q=80&w=2940&auto=format&fit=crop')"></div>
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?q=80&w=2940&auto=format&fit=crop')"></div>
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=2940&auto=format&fit=crop')"></div>
                <div class="visual-item" style="background-image: url('https://images.unsplash.com/photo-1516280440614-37939bbacd81?q=80&w=2940&auto=format&fit=crop')"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section class="content-section cta-section animated-section">
        <div class="section-container">
          <h2 class="section-heading">Ensure perfect audio quality.</h2>
          <a href="#contact" class="section-button">Inspect Audio
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><path d="M5 12h14m-7-7 7 7-7 7"/></svg>
          </a>
        </div>
      </section>
    `;
  }

  private renderFooter() {
      return html`
        <footer class="site-footer-bottom">
            <div class="footer-container">
                <div class="footer-column about-column">
                    <a href="#home" class="footer-logo" aria-label="PixelVane Home">
                        <svg width="40" height="40" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M30 85V15H60C73.8071 15 85 26.1929 85 40C85 53.8071 73.8071 65 60 65H45V85H30Z" fill="white"/>
                            <path d="M45 65L70 40L45 15V35L60 40L45 45V65Z" fill="#061a1f"/>
                        </svg>
                    </a>
                    <p>Pioneering the future of generative audio and creative analysis.</p>
                </div>
                <div class="footer-column links-column">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="#ai-composition">AI Composition</a></li>
                        <li><a href="#audio-synthesis">Audio Synthesis</a></li>
                        <li><a href="#analysis">Analysis</a></li>
                        <li><a href="#inspection">Inspection</a></li>
                    </ul>
                </div>
                <div class="footer-column links-column">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="#about">About Us</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom-bar">
                <div class="copyright">© 2025 PixelVane Ltd. All rights reserved.</div>
                <div class="social-links">
                    <a href="https://x.com/Epicfusion_" target="_blank" rel="noopener noreferrer" aria-label="Follow us on X"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24h-6.617l-5.21-6.817-6.022 6.817h-3.308l7.732-8.818-8.318-10.68h6.723l4.584 6.222 5.274-6.222z"/></svg></a>
                </div>
            </div>
        </footer>
      `;
  }

  static override styles = css`
    :host {
      display: block;
      background-color: var(--color-background);
      color: var(--color-text);
      --cursor-size: 10px;
      --cursor-scale: 2.5;
    }

    @media (hover: hover) and (pointer: fine) {
      #custom-cursor {
          position: fixed;
          top: 0;
          left: 0;
          width: var(--cursor-size);
          height: var(--cursor-size);
          background-color: var(--color-accent);
          border-radius: 50%;
          pointer-events: none;
          z-index: 9999;
          mix-blend-mode: difference;
          transition: transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                      width 0.3s ease, height 0.3s ease;
          transform-origin: center center;
      }

      #custom-cursor.hover {
          width: calc(var(--cursor-size) * var(--cursor-scale));
          height: calc(var(--cursor-size) * var(--cursor-scale));
      }
    }


    .page-content {
      display: flex;
      flex-direction: column;
      transition: transform 0.5s cubic-bezier(0.22, 1, 0.36, 1);
    }
    .page-content.menu-open {
        transform: scale(0.9);
        filter: blur(4px);
        pointer-events: none;
        user-select: none;
    }

    .page-wrapper {
        transition: opacity 0.5s ease-in-out;
    }
    .page-wrapper.page-exit {
        opacity: 0;
    }

    /* Animation */
    .animated-section {
        opacity: 0;
        transform: translateY(30px);
        transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        will-change: opacity, transform;
    }

    .animated-section.is-visible {
        opacity: 1;
        transform: translateY(0);
    }


    /* Header */
    .site-header {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      padding: 1.5rem 2rem;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 100;
      transition: color 0.3s ease;
    }
    .site-header.menu-open .site-logo path {
        fill: white;
    }
    .site-header.menu-open .menu-icon-bars .bar {
        background-color: white;
    }
    /* Invert colors for open menu */
    .site-header.menu-open .menu-icon-bars .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }
    .site-header.menu-open .menu-icon-bars .bar:nth-child(2) {
        opacity: 0;
    }
    .site-header.menu-open .menu-icon-bars .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .site-logo svg {
      transition: transform 0.3s ease;
    }
    .site-logo:hover svg {
      transform: rotate(5deg) scale(1.05);
    }

    .header-nav {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .nav-button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.65rem 1.25rem;
      border: 1px solid var(--color-border);
      border-radius: 5rem;
      background: transparent;
      color: white;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.2s ease-in-out;
      text-decoration: none;
    }

    .login-button {
      backdrop-filter: blur(5px);
      background: rgba(10, 10, 10, 0.1);
    }

    .nav-button:hover {
      border-color: white;
      background: rgba(255, 255, 255, 0.1);
    }

    .menu-button {
        background-color: var(--color-accent);
        border: 1px solid var(--color-accent);
        width: 50px;
        height: 50px;
        padding: 0;
        border-radius: 50%;
        position: relative;
        z-index: 101; /* Above mobile nav */
    }
    .menu-button:hover {
        background-color: white;
        border-color: white;
    }

    .menu-icon-bars {
        width: 24px;
        height: 24px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        padding: 4px 0;
        box-sizing: border-box;
    }
    .menu-icon-bars .bar {
        display: block;
        width: 20px;
        height: 2px;
        background-color: black;
        border-radius: 2px;
        transition: transform 0.3s ease, opacity 0.3s ease;
    }
    .site-header.menu-open .menu-button:hover {
      background-color: var(--color-accent);
    }


    /* Mobile Nav */
    .mobile-nav {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #031114;
        z-index: 99;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .mobile-nav-link {
        font-size: 2.5rem;
        font-weight: 500;
        color: var(--color-text-secondary);
        text-decoration: none;
        padding: 0.5rem 1rem;
        transition: color 0.3s ease;
    }

    .mobile-nav-link:hover {
        color: white;
    }

    /* Hero Section */
    .hero-section {
      height: 100vh;
      min-height: 700px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: left;
      padding: 2rem;
      box-sizing: border-box;
      overflow: hidden;
      color: white;
      background-color: #000;
    }

    #hero-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .hero-content {
        position: relative;
        z-index: 4;
        width: 100%;
        max-width: 1400px;
        padding: 0 2rem;
        box-sizing: border-box;
    }

    .hero-section h1 {
      font-size: clamp(3rem, 6vw, 5rem);
      font-weight: 500;
      line-height: 1.15;
      max-width: 900px;
      margin: 0;
      letter-spacing: -0.03em;
    }

    .hero-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 2rem 4rem;
        box-sizing: border-box;
        z-index: 4;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
    }

    .hero-footer-left p {
        margin: 0;
        font-size: 1rem;
        color: var(--color-text-secondary);
        line-height: 1.5;
    }

    .explore-button {
      padding: 0.75rem 1.5rem;
      border-radius: 2rem;
      border: 1px solid var(--color-border);
      background: rgba(10, 10, 10, 0.2);
      backdrop-filter: blur(10px);
      color: white;
      display: flex;
      align-items: center;
      gap: 1rem;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 500;
      transition: all 0.2s ease;
    }
    .explore-button:hover {
        border-color: white;
        background: rgba(255, 255, 255, 0.1);
    }
    .explore-button svg {
        transition: transform 0.3s ease;
    }
    .explore-button:hover svg {
        transform: translateY(3px);
    }

    /* General Content Sections */
    .content-section {
      padding: 8rem 2rem;
      box-sizing: border-box;
      background-color: var(--color-background-section);
      border-top: 1px solid var(--color-border);
    }
    .hero-section + .content-section {
        border-top: none;
    }

    .section-container, .section-container-wide {
      max-width: 1400px;
      margin: 0 auto;
    }

    .section-label {
      text-transform: uppercase;
      color: var(--color-text-secondary);
      font-weight: 500;
      font-size: 0.9rem;
      margin-bottom: 2rem;
      letter-spacing: 0.05em;
    }

    .section-heading {
      font-size: clamp(2rem, 3.5vw, 3rem);
      font-weight: 500;
      line-height: 1.25;
      margin: 0 0 2rem 0;
      letter-spacing: -0.03em;
      color: white;
    }

    .section-description {
      font-size: 1.1rem;
      line-height: 1.8;
      color: var(--color-text-secondary);
      max-width: 50ch;
      margin-bottom: 2rem;
    }

    .section-button {
      display: inline-flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem 1.5rem;
      border: 1px solid var(--color-border);
      border-radius: 2rem;
      transition: all 0.2s ease;
      color: white;
      font-weight: 500;
      text-decoration: none;
    }

    .section-button:hover {
      background-color: white;
      color: black;
      border-color: white;
    }
    .section-button:hover svg {
      stroke: black;
    }

    /* Why Us Section - Overhauled */
    .why-us-section {
      position: relative;
      padding: 10rem 2rem;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: left;
      overflow: hidden;
    }
    .why-us-background {
      position: absolute;
      inset: 0;
      background-image: url(https://images.unsplash.com/photo-1679085299933-228473a2a681?q=80&w=2787&auto=format&fit=crop);
      background-size: cover;
      background-position: center;
      z-index: 0;
      filter: brightness(0.6);
    }
     .why-us-section::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(to top, var(--color-background-section), transparent 50%);
      z-index: 1;
    }
    .why-us-content {
      position: relative;
      z-index: 2;
      max-width: 1200px;
    }
    .why-us-section .section-heading {
      max-width: 35ch;
      margin-bottom: 3rem;
    }
    .why-us-description {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem 4rem;
      color: var(--color-text-secondary);
      font-size: 1.1rem;
      line-height: 1.8;
      margin-bottom: 3rem;
    }
    .why-us-description p {
      margin: 0;
      max-width: 50ch;
    }

    /* Partners Section */
    .partners-section {
        background-color: var(--color-background-section);
        padding: 4rem 0;
        border-top: 1px solid var(--color-border);
        overflow: hidden;
    }
    .partners-scroller {
      width: 100%;
      display: flex;
    }
    .partners-track {
        display: flex;
        animation: scroll 40s linear infinite;
    }
    @keyframes scroll {
        from { transform: translateX(0); }
        to { transform: translateX(-50%); }
    }
    .partner-logo {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--color-text-secondary);
        filter: brightness(0.7);
        transition: filter 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        white-space: nowrap;
        padding: 0 3rem;
    }
    .partner-logo:hover {
        filter: brightness(1);
    }

    /* Services Section - Reimagined */
    .services-section-reimagined {
        background-color: var(--color-background);
    }
    .services-section-reimagined .services-container {
        max-width: 1400px;
        margin: 0 auto;
    }
    .services-section-reimagined .section-heading {
        max-width: 45ch;
        margin-bottom: 4rem;
    }
    .services-interactive-wrapper {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 4rem;
        min-height: 500px;
        margin-bottom: 4rem;
    }
    .services-list {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        border-left: 1px solid var(--color-border);
    }
    .services-list li {
        padding: 1.5rem 2rem;
        cursor: pointer;
        position: relative;
        transition: background-color 0.3s ease;
    }
    .services-list li h3 {
        font-size: 1.5rem;
        font-weight: 500;
        margin: 0;
        color: var(--color-text-secondary);
        transition: color 0.3s ease;
    }
    .services-list li.active h3 {
        color: white;
    }
    .services-list li::before {
        content: '';
        position: absolute;
        left: -1px;
        top: 0;
        width: 2px;
        height: 100%;
        background-color: var(--color-accent);
        transform: scaleY(0);
        transform-origin: center;
        transition: transform 0.4s cubic-bezier(0.22, 1, 0.36, 1);
    }
    .services-list li.active::before {
        transform: scaleY(1);
    }
    .services-visuals {
        display: grid;
        grid-template-rows: auto 1fr;
        gap: 2rem;
        position: relative;
    }
    .service-description-panel {
        position: relative;
        color: var(--color-text-secondary);
        font-size: 1.1rem;
        line-height: 1.7;
        height: 5em; /* Reserve space for 3 lines of text */
    }
    .service-description-panel p {
        margin: 0;
        position: absolute;
        inset: 0;
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
    }
    .service-description-panel p.active {
        opacity: 1;
    }
    .service-image-panel {
        position: relative;
        border-radius: 0.75rem;
        overflow: hidden;
    }
    .service-image {
        position: absolute;
        inset: 0;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        opacity: 0;
        transform: scale(1.05);
        transition: opacity 0.6s ease-in-out, transform 0.6s ease-in-out;
        background-color: var(--color-background-section);
    }
    .service-image.active {
        opacity: 1;
        transform: scale(1);
    }
    /* Fallback for missing service images */
    .service-image:not([style*="background-image"]) {
        background: linear-gradient(135deg, var(--color-background-section) 0%, rgba(var(--color-accent-rgb), 0.1) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--color-text-secondary);
        font-size: 3rem;
    }
    .service-image:not([style*="background-image"])::before {
        content: '🎵';
    }


    /* Software Section */
    .software-section {
        padding: 10rem 2rem;
        position: relative;
        display: flex;
        align-items: center;
        background-image: url(https://images.unsplash.com/photo-1620121692029-d088224ddc74?q=80&w=2832&auto=format&fit=crop);
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
    }
    .software-section::before {
        content: '';
        position: absolute;
        inset: 0;
        background-color: rgba(6, 26, 31, 0.8);
    }
    .software-content {
        position: relative;
        z-index: 1;
        max-width: 1400px;
        margin: 0 auto;
    }
    .software-section .section-heading {
        max-width: 25ch;
    }
    .software-section .section-description {
        margin-bottom: 3rem;
    }

    /* Card Sections (Case Studies) - Overhauled */
    .card-section {
        background-color: var(--color-background);
    }
    .card-section-header {
        margin: 0 auto;
        padding-bottom: 4rem;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        gap: 2rem;
        flex-wrap: wrap;
    }
    .card-section-header .section-heading {
        margin: 0;
    }
    .case-studies-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: auto auto;
        gap: 2rem;
    }
    .case-study-card {
        text-decoration: none;
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        position: relative;
        border-radius: 0.75rem;
        overflow: hidden;
        aspect-ratio: 4 / 3;
        padding: 2rem;
        box-sizing: border-box;
    }
    .case-study-card:first-child {
        grid-column: 1 / -1;
        aspect-ratio: 2 / 1;
    }
    .card-background {
        position: absolute;
        inset: 0;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-color: var(--color-background-section);
        transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    /* Fallback for missing case study images */
    .card-background:not([style*="background-image"]) {
        background: linear-gradient(135deg, var(--color-background-section) 0%, rgba(var(--color-accent-rgb), 0.2) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--color-accent);
        font-size: 4rem;
    }
    .card-background:not([style*="background-image"])::before {
        content: '🎼';
    }
    .case-study-card:hover .card-background {
        transform: scale(1.05);
    }
    .card-background::after {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0) 100%);
        transition: background 0.3s ease;
    }
     .case-study-card:hover .card-background::after {
        background: linear-gradient(to top, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.5) 50%, rgba(0,0,0,0.1) 100%);
    }
    .case-study-card .card-content {
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        width: 100%;
    }
    .card-category {
        font-size: 0.9rem;
        font-weight: 500;
        color: var(--color-text-secondary);
        display: block;
        margin-bottom: 0.5rem;
    }
    .card-title {
        font-size: 1.5rem;
        font-weight: 500;
        line-height: 1.4;
        margin: 0;
        max-width: 30ch;
    }
    .card-arrow {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: rgba(255,255,255,0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(5px);
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.3s ease;
    }
    .case-study-card:hover .card-arrow {
        transform: scale(1);
        opacity: 1;
    }


    /* Final CTA Section */
    .cta-section {
        background: var(--color-background-section);
        text-align: left;
        padding-top: 6rem;
    }
    .cta-section .section-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        max-width: 900px;
    }
    .cta-section .section-heading {
        margin-bottom: 3rem;
        font-size: clamp(2rem, 3vw, 2.5rem);
    }

    /* Enhanced Service Pages */
    .service-hero {
      position: relative;
      min-height: 70vh;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }
    .hero-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
    }
    .hero-image {
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      background-attachment: fixed;
    }
    .hero-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(6, 26, 31, 0.8) 0%, rgba(6, 26, 31, 0.6) 100%);
    }
    .service-hero .page-hero-content {
      position: relative;
      z-index: 2;
      text-align: center;
      max-width: 800px;
      padding: 2rem;
    }
    .service-badge {
      display: inline-block;
      padding: 0.5rem 1.5rem;
      background: rgba(var(--color-accent-rgb), 0.1);
      border: 1px solid rgba(var(--color-accent-rgb), 0.3);
      border-radius: 2rem;
      color: var(--color-accent);
      font-size: 0.875rem;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.1em;
      margin-bottom: 2rem;
      backdrop-filter: blur(10px);
    }
    .hero-cta-button {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 1rem 2rem;
      background: var(--color-accent);
      color: var(--color-background);
      text-decoration: none;
      border-radius: 0.5rem;
      font-weight: 500;
      margin-top: 2rem;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(var(--color-accent-rgb), 0.3);
    }
    .hero-cta-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(var(--color-accent-rgb), 0.4);
    }

    .service-features {
      padding: 8rem 2rem;
      background: var(--color-background-section);
      border-top: 1px solid var(--color-border);
    }
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 3rem;
      margin-top: 4rem;
      max-width: 1400px;
      margin-left: auto;
      margin-right: auto;
    }
    .feature-card {
      background: var(--color-background);
      padding: 3rem 2.5rem;
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      text-align: center;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;
    }
    .feature-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg,
        var(--color-accent) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .feature-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 25px 60px rgba(0,0,0,0.15);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .feature-card:hover::before {
      opacity: 1;
    }
    .feature-icon {
      margin-bottom: 2rem;
      color: var(--color-accent);
    }
    .feature-card h3 {
      font-size: 1.5rem;
      font-weight: 500;
      margin-bottom: 1rem;
      color: var(--color-text);
    }
    .feature-card p {
      color: var(--color-text-secondary);
      line-height: 1.6;
      margin: 0;
    }

    .service-showcase {
      padding: 8rem 2rem;
      background: var(--color-background);
    }
    .showcase-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 6rem;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }
    .showcase-content h2 {
      font-size: clamp(2rem, 4vw, 3rem);
      margin: 1rem 0 3rem 0;
      line-height: 1.2;
      font-weight: 500;
      color: var(--color-text);
    }
    .use-cases-list {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
    .use-case-item {
      display: flex;
      align-items: flex-start;
      gap: 1.5rem;
      padding: 2rem;
      background: var(--color-background-section);
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      transition: all 0.3s ease;
    }
    .use-case-item:hover {
      transform: translateX(8px);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .use-case-icon {
      font-size: 2rem;
      flex-shrink: 0;
    }
    .use-case-item h4 {
      font-size: 1.25rem;
      font-weight: 500;
      margin: 0 0 0.5rem 0;
      color: var(--color-text);
    }
    .use-case-item p {
      margin: 0;
      color: var(--color-text-secondary);
      line-height: 1.6;
    }
    .showcase-visual {
      position: relative;
    }
    .visual-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      border-radius: 1rem;
      overflow: hidden;
    }
    .visual-item {
      height: 200px;
      background-size: cover;
      background-position: center;
      position: relative;
      transition: transform 0.3s ease;
    }
    .visual-item:hover {
      transform: scale(1.05);
    }
    .visual-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(var(--color-accent-rgb), 0.1) 0%, transparent 100%);
    }

    /* Footer */
    .site-footer-bottom {
        background-color: var(--color-background);
        padding: 6rem 2rem 2rem;
        border-top: 1px solid var(--color-border);
    }
    .footer-container {
        max-width: 1400px;
        margin: 0 auto 4rem auto;
        display: grid;
        grid-template-columns: 2fr 1fr 1fr;
        gap: 2rem;
    }
    .footer-logo {
        display: block;
        margin-bottom: 1rem;
    }
    .about-column p {
        color: var(--color-text-secondary);
        max-width: 35ch;
        line-height: 1.6;
    }
    .links-column h4 {
        font-size: 1.1rem;
        font-weight: 500;
        margin: 0 0 1.5rem 0;
        color: white;
    }
    .links-column ul {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    .links-column a {
        color: var(--color-text-secondary);
        text-decoration: none;
        transition: color 0.2s ease;
    }
    .links-column a:hover {
        color: white;
    }
    .footer-bottom-bar {
        max-width: 1400px;
        margin: 0 auto;
        padding-top: 2rem;
        border-top: 1px solid var(--color-border);
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--color-text-secondary);
        font-size: 0.9rem;
    }
    .social-links {
        display: flex;
        gap: 1.5rem;
    }
    .social-links a {
        color: var(--color-text-secondary);
        transition: color 0.2s ease;
    }
    .social-links a:hover {
        color: white;
    }
    .social-links svg {
        width: 20px;
        height: 20px;
    }


    /* Playground Wrapper */
    prompt-dj-midi-wrapper {
      display: block;
      width: 100%;
      height: 100vh;
      position: relative;
    }

    /* When in playground mode, adjust layout but keep header */
    body.playground prompt-dj-midi-wrapper {
      height: calc(100vh - 80px);
      margin-top: 80px;
    }

    body.playground .site-header {
      position: fixed;
      background: rgba(var(--color-background-section), 0.95);
      backdrop-filter: blur(10px);
      z-index: 1000;
    }

    /* Responsive adjustments */
    @media (max-width: 991px) {
        .header-nav .login-button {
            display: none;
        }
        .footer-container {
            grid-template-columns: 1fr 1fr;
            gap: 4rem 2rem;
        }
        .about-column {
            grid-column: 1 / -1;
        }
        .case-studies-grid {
            grid-template-columns: 1fr;
        }
        .case-study-card:first-child {
            grid-column: auto;
            aspect-ratio: 4/3;
        }
        .services-interactive-wrapper {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        .services-list {
            flex-direction: row;
            border-left: none;
            border-bottom: 1px solid var(--color-border);
            overflow-x: auto;
            /* Hide scrollbar */
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
        .services-list::-webkit-scrollbar {
            display: none; /* Chrome, Safari and Opera */
        }
        .services-list li {
            padding: 1rem 1.5rem;
            border-bottom: 2px solid transparent;
            border-top: 2px solid transparent;
            white-space: nowrap;
        }
        .services-list li h3 {
            font-size: 1.2rem;
        }
        .services-list li::before {
            top: auto;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
        }
        .services-visuals {
            min-height: 400px;
        }
    }

    @media (max-width: 768px) {
      .site-header {
        padding: 1.5rem;
      }
      .menu-button {
          width: 44px;
          height: 44px;
      }
      .hero-section h1 {
        font-size: 2.5rem;
      }
      .hero-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 2rem;
        text-align: left;
        padding: 2rem;
      }
      .why-us-description {
        grid-template-columns: 1fr;
      }
      .footer-container {
        grid-template-columns: 1fr;
        gap: 3rem;
      }
      .service-detail-grid,
      .showcase-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
      }
      .service-detail-visual .service-image {
        height: 300px;
      }
      .features-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
      }
      .visual-grid {
        grid-template-columns: 1fr;
      }
      .visual-item {
        height: 250px;
      }
      .hero-image {
        background-attachment: scroll;
      }
      .footer-bottom-bar {
          flex-direction: column-reverse;
          gap: 1.5rem;
          text-align: center;
      }
      .card-title {
        font-size: 1.25rem;
      }
      .content-section {
        padding: 6rem 1rem;
      }
      .why-us-section {
        padding: 8rem 1rem;
      }
      .software-section {
        padding: 8rem 1rem;
        background-attachment: scroll; /* Performance on mobile */
      }
      .mobile-nav-link {
        font-size: 2rem;
      }
    }

    /* Page-specific styles matching home page design */
    .page-hero {
      height: 60vh;
      min-height: 500px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      background: var(--color-background);
      overflow: hidden;
    }
    .page-hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.05) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.02) 100%);
      pointer-events: none;
    }
    .page-hero-content {
      position: relative;
      z-index: 2;
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 2rem;
      width: 100%;
      box-sizing: border-box;
    }
    .page-hero-content h1 {
      font-size: clamp(2.5rem, 5vw, 4rem);
      font-weight: 500;
      line-height: 1.15;
      max-width: 800px;
      margin: 0 0 2rem 0;
      color: var(--color-text);
    }
    .page-hero-content p {
      font-size: 1.25rem;
      color: var(--color-text-secondary);
      max-width: 600px;
      margin: 0;
      line-height: 1.6;
      font-weight: 300;
    }

    /* About page styles matching home page design */
    .about-content {
      padding: 8rem 2rem;
      background-color: var(--color-background-section);
      border-top: 1px solid var(--color-border);
    }
    .about-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 6rem;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }
    .about-text h2 {
      font-size: clamp(2rem, 4vw, 3rem);
      margin: 1rem 0 2rem 0;
      line-height: 1.2;
      font-weight: 500;
      color: var(--color-text);
    }
    .about-text p {
      font-size: 1.125rem;
      line-height: 1.7;
      margin-bottom: 2rem;
      color: var(--color-text-secondary);
      font-weight: 300;
    }
    .about-image {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
    }
    .image-placeholder {
      width: 400px;
      height: 400px;
      background: linear-gradient(135deg, var(--color-background) 0%, var(--color-background-section) 100%);
      border-radius: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid var(--color-border);
      position: relative;
      overflow: hidden;
    }
    .image-placeholder::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg,
        rgba(var(--color-accent-rgb), 0.1) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.05) 100%);
    }
    .image-placeholder svg {
      color: var(--color-accent);
      position: relative;
      z-index: 2;
    }

    .team-section {
      background: var(--color-background);
      padding: 8rem 2rem;
      border-top: 1px solid var(--color-border);
    }
    .team-section .section-container {
      max-width: 1400px;
      margin: 0 auto;
    }
    .team-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 3rem;
      margin-top: 4rem;
    }
    .team-member {
      text-align: center;
      padding: 3rem 2rem;
      background: var(--color-background-section);
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;
    }
    .team-member::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.02) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .team-member:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15);
      border-color: rgba(var(--color-accent-rgb), 0.3);
    }
    .team-member:hover::before {
      opacity: 1;
    }
    .member-avatar {
      width: 100px;
      height: 100px;
      background: linear-gradient(135deg, var(--color-background) 0%, var(--color-accent) 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 2rem;
      border: 3px solid var(--color-accent);
      position: relative;
      z-index: 2;
    }
    .member-avatar svg {
      color: var(--color-background);
    }
    .team-member h3 {
      font-size: 1.5rem;
      margin: 0 0 0.5rem 0;
      font-weight: 500;
      position: relative;
      z-index: 2;
    }
    .member-role {
      color: var(--color-accent);
      font-weight: 600;
      margin: 0 0 1.5rem 0;
      font-size: 0.95rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      position: relative;
      z-index: 2;
    }
    .team-member p:last-child {
      color: var(--color-text-secondary);
      line-height: 1.6;
      font-weight: 300;
      position: relative;
      z-index: 2;
    }

    .values-section {
      padding: 8rem 2rem;
      background: var(--color-background-section);
      border-top: 1px solid var(--color-border);
    }
    .values-section .section-container {
      max-width: 1400px;
      margin: 0 auto;
    }
    .values-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 3rem;
      margin-top: 4rem;
    }
    .value-item {
      text-align: center;
      padding: 3rem 2rem;
      background: var(--color-background);
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;
    }
    .value-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.05) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.02) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .value-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 40px rgba(0,0,0,0.1);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .value-item:hover::before {
      opacity: 1;
    }
    .value-icon {
      font-size: 3.5rem;
      margin-bottom: 1.5rem;
      position: relative;
      z-index: 2;
      color: var(--color-accent);
    }
    .value-icon svg {
      width: 48px;
      height: 48px;
      stroke: var(--color-accent);
    }
    .value-item h3 {
      font-size: 1.5rem;
      margin: 0 0 1.5rem 0;
      font-weight: 500;
      position: relative;
      z-index: 2;
    }
    .value-item p {
      color: var(--color-text-secondary);
      line-height: 1.6;
      font-weight: 300;
      position: relative;
      z-index: 2;
    }

    /* Services page styles matching home page design */
    .services-detail {
      padding: 8rem 2rem;
      background: var(--color-background-section);
      border-top: 1px solid var(--color-border);
    }
    .services-detail .section-container {
      max-width: 1400px;
      margin: 0 auto;
    }
    .services-detail-grid {
      display: grid;
      gap: 6rem;
    }
    .service-detail-card {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 4rem;
      align-items: center;
      padding: 4rem;
      background: var(--color-background);
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;
    }
    .service-detail-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.02) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .service-detail-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 25px 70px rgba(0,0,0,0.15);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .service-detail-card:hover::before {
      opacity: 1;
    }
    .service-detail-card:nth-child(even) {
      direction: rtl;
    }
    .service-detail-card:nth-child(even) > * {
      direction: ltr;
    }
    .service-detail-image {
      width: 100%;
      height: 350px;
      background-size: cover;
      background-position: center;
      border-radius: 1rem;
      background-color: var(--color-background-section);
      border: 1px solid var(--color-border);
      position: relative;
      z-index: 2;
    }
    .service-detail-content {
      position: relative;
      z-index: 2;
    }
    .service-detail-content h3 {
      font-size: clamp(1.75rem, 3vw, 2.5rem);
      margin: 0 0 1.5rem 0;
      color: var(--color-accent);
      font-weight: 500;
    }
    .service-detail-content p {
      font-size: 1.125rem;
      line-height: 1.7;
      color: var(--color-text-secondary);
      margin-bottom: 2.5rem;
      font-weight: 300;
    }
    .service-features h4 {
      margin: 0 0 1.5rem 0;
      color: var(--color-text);
      font-size: 1.25rem;
      font-weight: 500;
    }
    .service-features ul {
      list-style: none;
      padding: 0;
      margin: 0 0 3rem 0;
    }
    .service-features li {
      padding: 0.75rem 0;
      color: var(--color-text-secondary);
      position: relative;
      padding-left: 2rem;
      font-weight: 300;
      border-bottom: 1px solid var(--color-border);
    }
    .service-features li:last-child {
      border-bottom: none;
    }
    .service-features li::before {
      content: '✓';
      position: absolute;
      left: 0;
      color: var(--color-accent);
      font-weight: bold;
      font-size: 1.1rem;
    }
    .service-cta {
      display: inline-flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem 2rem;
      background: var(--color-accent);
      color: var(--color-background);
      text-decoration: none;
      border-radius: 0.75rem;
      font-weight: 600;
      font-size: 1rem;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border: 2px solid var(--color-accent);
    }
    .service-cta:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 35px rgba(var(--color-accent-rgb), 0.4);
      background: transparent;
      color: var(--color-accent);
    }

    /* Coming Soon Section - Matching Home Page Design */
    .pricing-coming-soon {
      background: var(--color-background);
      padding: 8rem 2rem;
      border-top: 1px solid var(--color-border);
      position: relative;
      overflow: hidden;
    }
    .pricing-coming-soon::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.02) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      pointer-events: none;
    }
    .pricing-coming-soon .section-container {
      max-width: 1000px;
      margin: 0 auto;
      position: relative;
      z-index: 2;
    }
    .coming-soon-content {
      text-align: center;
      padding: 4rem 2rem;
      background: var(--color-background-section);
      border-radius: 1.5rem;
      border: 1px solid var(--color-border);
      position: relative;
      overflow: hidden;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    .coming-soon-content::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.03) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .coming-soon-content:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .coming-soon-content:hover::before {
      opacity: 1;
    }
    .coming-soon-icon {
      margin: 0 auto 2rem;
      width: 120px;
      height: 120px;
      background: linear-gradient(135deg, var(--color-accent) 0%, #e6a000 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 2;
      animation: pulse 2s infinite;
    }
    .coming-soon-icon svg {
      color: var(--color-background);
    }
    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }
    .coming-soon-description {
      font-size: 1.25rem;
      line-height: 1.7;
      color: var(--color-text-secondary);
      margin: 2rem auto 3rem;
      max-width: 600px;
      font-weight: 300;
      position: relative;
      z-index: 2;
    }
    .coming-soon-description strong {
      color: var(--color-accent);
      font-weight: 600;
    }
    .coming-soon-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin: 3rem 0;
      position: relative;
      z-index: 2;
    }
    .feature-highlight {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1.5rem;
      background: var(--color-background);
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      transition: all 0.3s ease;
      text-align: left;
    }
    .feature-highlight:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .feature-icon {
      font-size: 2rem;
      flex-shrink: 0;
    }
    .feature-text h4 {
      margin: 0 0 0.5rem 0;
      font-size: 1.1rem;
      font-weight: 500;
      color: var(--color-text);
    }
    .feature-text p {
      margin: 0;
      font-size: 0.95rem;
      color: var(--color-text-secondary);
      line-height: 1.5;
    }
    .coming-soon-cta {
      display: flex;
      gap: 1rem;
      justify-content: center;
      align-items: center;
      margin-top: 3rem;
      position: relative;
      z-index: 2;
    }
    .section-button.primary {
      background: var(--color-accent);
      color: var(--color-background);
      border: 2px solid var(--color-accent);
    }
    .section-button.primary:hover {
      background: transparent;
      color: var(--color-accent);
    }
    .section-button.secondary {
      background: transparent;
      color: var(--color-accent);
      border: 2px solid var(--color-accent);
    }
    .section-button.secondary:hover {
      background: var(--color-accent);
      color: var(--color-background);
    }

    /* Contact page styles matching home page design */
    .contact-content {
      padding: 8rem 2rem;
      background: var(--color-background-section);
      border-top: 1px solid var(--color-border);
    }
    .contact-content .section-container {
      max-width: 1400px;
      margin: 0 auto;
    }
    .contact-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 6rem;
      align-items: start;
    }
    .contact-info h2 {
      font-size: clamp(2rem, 4vw, 3rem);
      margin: 1rem 0 2rem 0;
      line-height: 1.2;
      font-weight: 500;
    }
    .contact-info p {
      font-size: 1.125rem;
      line-height: 1.7;
      color: var(--color-text-secondary);
      margin-bottom: 4rem;
      font-weight: 300;
    }
    .contact-methods {
      display: flex;
      flex-direction: column;
      gap: 3rem;
    }
    .contact-method {
      display: flex;
      align-items: flex-start;
      gap: 1.5rem;
      padding: 2rem;
      background: var(--color-background);
      border-radius: 1rem;
      border: 1px solid var(--color-border);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;
    }
    .contact-method::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.02) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .contact-method:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 40px rgba(0,0,0,0.1);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .contact-method:hover::before {
      opacity: 1;
    }
    .contact-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, var(--color-accent) 0%, #e6a000 100%);
      border-radius: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      border: none;
      position: relative;
      z-index: 2;
    }
    .contact-icon svg {
      color: var(--color-background);
    }
    .contact-method div:last-child {
      position: relative;
      z-index: 2;
    }
    .contact-method h4 {
      margin: 0 0 0.75rem 0;
      font-size: 1.25rem;
      font-weight: 500;
    }
    .contact-method p {
      margin: 0;
      color: var(--color-text-secondary);
      line-height: 1.5;
      font-weight: 300;
    }

    .contact-form {
      background: var(--color-background);
      border-radius: 1rem;
      padding: 3rem;
      border: 1px solid var(--color-border);
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;
    }
    .contact-form::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(var(--color-accent-rgb), 0.02) 0%,
        transparent 50%,
        rgba(var(--color-accent-rgb), 0.01) 100%);
      opacity: 0;
      transition: opacity 0.4s ease;
    }
    .contact-form:hover {
      box-shadow: 0 20px 60px rgba(0,0,0,0.1);
      border-color: rgba(var(--color-accent-rgb), 0.2);
    }
    .contact-form:hover::before {
      opacity: 1;
    }
    .contact-form-container {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      position: relative;
      z-index: 2;
    }
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }
    .form-group label {
      font-weight: 500;
      color: var(--color-text);
      font-size: 1rem;
    }
    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 1rem 1.25rem;
      border: 1px solid var(--color-border);
      border-radius: 0.75rem;
      background: var(--color-background-section);
      color: var(--color-text);
      font-family: inherit;
      font-size: 1rem;
      transition: all 0.3s ease;
      font-weight: 300;
    }
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: var(--color-accent);
      box-shadow: 0 0 0 3px rgba(var(--color-accent-rgb), 0.1);
      background: var(--color-background);
    }
    .form-group textarea {
      resize: vertical;
      min-height: 140px;
      line-height: 1.6;
    }
    .form-submit {
      padding: 1.25rem 2.5rem;
      background: var(--color-accent);
      color: var(--color-background);
      border: 2px solid var(--color-accent);
      border-radius: 0.75rem;
      font-weight: 600;
      font-size: 1rem;
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      margin-top: 1rem;
    }
    .form-submit:hover {
      background: transparent;
      color: var(--color-accent);
      transform: translateY(-3px);
      box-shadow: 0 15px 35px rgba(var(--color-accent-rgb), 0.4);
    }



    /* Responsive styles for new pages matching home page */
    @media (max-width: 991px) {
      .page-hero {
        height: 50vh;
        min-height: 400px;
      }
      .page-hero-content h1 {
        font-size: 2.5rem;
      }
      .about-grid,
      .contact-grid {
        grid-template-columns: 1fr;
        gap: 4rem;
      }
      .service-detail-card {
        grid-template-columns: 1fr;
        gap: 3rem;
        padding: 3rem 2rem;
      }
      .service-detail-card:nth-child(even) {
        direction: ltr;
      }
      .coming-soon-features {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
      .coming-soon-cta {
        flex-direction: column;
        gap: 1rem;
      }
      .team-grid,
      .values-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      }
    }

    @media (max-width: 767px) {
      .page-hero {
        height: 40vh;
        min-height: 350px;
        padding: 0 1rem;
      }
      .page-hero-content h1 {
        font-size: 2rem;
      }
      .about-content,
      .contact-content,
      .services-detail,
      .team-section,
      .values-section,
      .pricing-section {
        padding: 6rem 1rem;
      }
      .about-grid,
      .contact-grid {
        gap: 3rem;
      }
      .service-detail-card {
        padding: 2rem 1.5rem;
        gap: 2rem;
      }
      .team-grid,
      .values-grid,
      .coming-soon-features {
        grid-template-columns: 1fr;
        gap: 2rem;
      }
      .contact-form {
        padding: 2rem 1.5rem;
      }
      .contact-methods {
        gap: 2rem;
      }
      .contact-method {
        padding: 1.5rem;
      }
      .image-placeholder {
        width: 300px;
        height: 300px;
      }
    }

    @media (max-width: 480px) {
      .page-hero-content h1 {
        font-size: 1.75rem;
      }
      .about-content,
      .contact-content,
      .services-detail,
      .team-section,
      .values-section,
      .pricing-coming-soon {
        padding: 4rem 1rem;
      }
      .service-detail-card,
      .team-member,
      .value-item,
      .coming-soon-content {
        padding: 1.5rem;
      }
      .contact-form {
        padding: 1.5rem;
      }
      .image-placeholder {
        width: 250px;
        height: 250px;
      }
    }
  `;
}

function main() {
  document.body.appendChild(new AppShell());
}

main();
