{"version": 3, "file": "reactive-element.js", "sourceRoot": "", "sources": ["../src/reactive-element.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;;GAIG;AAEH,OAAO,EACL,kBAAkB,EAClB,WAAW,GAGZ,MAAM,cAAc,CAAC;AAMtB,6DAA6D;AAC7D,sEAAsE;AAEtE,cAAc,cAAc,CAAC;AAiB7B,8DAA8D;AAC9D,MAAM,EACJ,EAAE,EACF,cAAc,EACd,wBAAwB,EACxB,mBAAmB,EACnB,qBAAqB,EACrB,cAAc,GACf,GAAG,MAAM,CAAC;AAEX,MAAM,SAAS,GAAG,KAAK,CAAC;AAExB,qEAAqE;AACrE,MAAM,MAAM,GAAG,UAAU,CAAC;AAE1B,IAAI,SAAS,EAAE,CAAC;IACd,MAAM,CAAC,cAAc,KAAK,cAAc,CAAC;AAC3C,CAAC;AAED,MAAM,QAAQ,GAAG,IAAI,CAAC;AAEtB,IAAI,YAAqD,CAAC;AAE1D,MAAM,YAAY,GAAI,MAAwD;KAC3E,YAAY,CAAC;AAEhB,oDAAoD;AACpD,oEAAoE;AACpE,8EAA8E;AAC9E,6BAA6B;AAC7B,MAAM,8BAA8B,GAAG,YAAY;IACjD,CAAC,CAAE,YAAY,CAAC,WAA6B;IAC7C,CAAC,CAAC,EAAE,CAAC;AAEP,MAAM,eAAe,GAAG,QAAQ;IAC9B,CAAC,CAAC,MAAM,CAAC,qCAAqC;IAC9C,CAAC,CAAC,MAAM,CAAC,8BAA8B,CAAC;AAE1C,IAAI,QAAQ,EAAE,CAAC;IACb,uEAAuE;IACvE,cAAc;IACd,MAAM,CAAC,iBAAiB,KAAK,IAAI,GAAG,EAAE,CAAC;IAEvC;;;;OAIG;IACH,YAAY,GAAG,CAAC,IAAY,EAAE,OAAe,EAAE,EAAE;QAC/C,OAAO,IAAI,4BAA4B,IAAI,wBAAwB,CAAC;QACpE,IACE,CAAC,MAAM,CAAC,iBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC;YACvC,CAAC,MAAM,CAAC,iBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EACpC,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtB,MAAM,CAAC,iBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;IACH,CAAC,CAAC;IAEF,cAAc,CAAC,GAAG,EAAE;QAClB,YAAY,CACV,UAAU,EACV,qDAAqD,CACtD,CAAC;QAEF,kCAAkC;QAClC,IAAI,MAAM,CAAC,QAAQ,EAAE,KAAK,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;YAC5D,YAAY,CACV,0BAA0B,EAC1B,sDAAsD;gBACpD,sDAAsD,CACzD,CAAC;QACJ,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAmCD;;;;GAIG;AACH,MAAM,aAAa,GAAG,QAAQ;IAC5B,CAAC,CAAC,CAAC,KAAsC,EAAE,EAAE;QACzC,MAAM,UAAU,GAAI,MAAwC;aACzD,qBAAqB,CAAC;QACzB,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO;QACT,CAAC;QACD,MAAM,CAAC,aAAa,CAClB,IAAI,WAAW,CAAkC,WAAW,EAAE;YAC5D,MAAM,EAAE,KAAK;SACd,CAAC,CACH,CAAC;IACJ,CAAC;IACH,CAAC,CAAC,SAAS,CAAC;AAEd;;;;;GAKG;AACH,eAAe;AACf,MAAM,yBAAyB,GAAG,CAChC,IAAO,EACP,IAAa,EACV,EAAE,CAAC,IAAI,CAAC;AA0Kb,MAAM,CAAC,MAAM,gBAAgB,GAA8B;IACzD,WAAW,CAAC,KAAc,EAAE,IAAc;QACxC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,OAAO;gBACV,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,IAAI,CAAC;gBACtD,MAAM;YACR,KAAK,MAAM,CAAC;YACZ,KAAK,KAAK;gBACR,0DAA0D;gBAC1D,wCAAwC;gBACxC,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtD,MAAM;QACV,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,aAAa,CAAC,KAAoB,EAAE,IAAc;QAChD,IAAI,SAAS,GAAY,KAAK,CAAC;QAC/B,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,OAAO;gBACV,SAAS,GAAG,KAAK,KAAK,IAAI,CAAC;gBAC3B,MAAM;YACR,KAAK,MAAM;gBACT,SAAS,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAClD,MAAM;YACR,KAAK,MAAM,CAAC;YACZ,KAAK,KAAK;gBACR,mEAAmE;gBACnE,mDAAmD;gBACnD,sDAAsD;gBACtD,IAAI,CAAC;oBACH,kEAAkE;oBAClE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAM,CAAY,CAAC;gBAC5C,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,SAAS,GAAG,IAAI,CAAC;gBACnB,CAAC;gBACD,MAAM;QACV,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAC;AAMF;;;GAGG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAe,CAAC,KAAc,EAAE,GAAY,EAAW,EAAE,CAC5E,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAElB,MAAM,0BAA0B,GAAwB;IACtD,SAAS,EAAE,IAAI;IACf,IAAI,EAAE,MAAM;IACZ,SAAS,EAAE,gBAAgB;IAC3B,OAAO,EAAE,KAAK;IACd,UAAU,EAAE,KAAK;IACjB,UAAU,EAAE,QAAQ;CACrB,CAAC;AAmBF,2DAA2D;AAC3D,qDAAqD;AACpD,MAA6B,CAAC,QAAQ,KAAK,MAAM,CAAC,UAAU,CAAC,CAAC;AAW/D,yDAAyD;AACzD,8EAA8E;AAC9E,uDAAuD;AACvD,MAAM,CAAC,mBAAmB,KAAK,IAAI,OAAO,EAGvC,CAAC;AAEJ;;;;;GAKG;AACH,MAAM,OAAgB,eAAe;AACnC,oEAAoE;AACpE,6CAA6C;AAC7C,EAAE;AACF,4EAA4E;AAC5E,4EAA4E;AAC5E,2EAA2E;AAC3E,sEAAsE;AACtE,iEAAiE;AACjE,SAAQ,WAAW;IAoDnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,MAAM,CAAC,cAAc,CAAC,WAAwB;QAC5C,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,CAAC,IAAI,CAAC,aAAa,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAChD,CAAC;IAkGD;;;;OAIG;IACH,MAAM,KAAK,kBAAkB;QAC3B,sCAAsC;QACtC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,sEAAsE;QACtE,qEAAqE;QACrE,yEAAyE;QACzE,iCAAiC;QACjC,OAAO,CACL,IAAI,CAAC,wBAAwB,IAAI,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,CAAC,CAC3E,CAAC;IACJ,CAAC;IAID;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,MAAM,CAAC,cAAc,CACnB,IAAiB,EACjB,UAA+B,0BAA0B;QAEzD,6DAA6D;QAC7D,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YACjB,OAAqD,CAAC,SAAS,GAAG,KAAK,CAAC;QAC3E,CAAC;QACD,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,+CAA+C;QAC/C,+DAA+D;QAC/D,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YACxC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACjC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,MAAM,GAAG,GAAG,QAAQ;gBAClB,CAAC,CAAC,iEAAiE;oBACjE,kBAAkB;oBAClB,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC;gBACnD,CAAC,CAAC,MAAM,EAAE,CAAC;YACb,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;YAClE,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;gBAC7B,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACO,MAAM,CAAC,qBAAqB,CACpC,IAAiB,EACjB,GAAoB,EACpB,OAA4B;QAE5B,MAAM,EAAC,GAAG,EAAE,GAAG,EAAC,GAAG,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI;YACnE,GAAG;gBACD,OAAO,IAAI,CAAC,GAAwB,CAAC,CAAC;YACxC,CAAC;YACD,GAAG,CAAwB,CAAU;gBAClC,IAAoD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACjE,CAAC;SACF,CAAC;QACF,IAAI,QAAQ,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YAC5B,IAAI,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;gBACtE,MAAM,IAAI,KAAK,CACb,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM;oBACzC,GAAG,IAAI,CAAC,IAAI,uCAAuC;oBACnD,0DAA0D;oBAC1D,+DAA+D,CAClE,CAAC;YACJ,CAAC;YACD,YAAY,CACV,kCAAkC,EAClC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM;gBACzC,GAAG,IAAI,CAAC,IAAI,uCAAuC;gBACnD,4DAA4D;gBAC5D,wBAAwB,CAC3B,CAAC;QACJ,CAAC;QACD,OAAO;YACL,GAAG;YACH,GAAG,CAAwB,KAAc;gBACvC,MAAM,QAAQ,GAAG,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACvB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC9C,CAAC;YACD,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,IAAI;SACjB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,MAAM,CAAC,kBAAkB,CAAC,IAAiB;QACzC,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC;IACxE,CAAC;IAKD;;;;;;;;;;OAUG;IACK,MAAM,CAAC,SAAS;QACtB,IACE,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,EACzE,CAAC;YACD,mBAAmB;YACnB,OAAO;QACT,CAAC;QACD,4BAA4B;QAC5B,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAA2B,CAAC;QACjE,SAAS,CAAC,QAAQ,EAAE,CAAC;QAErB,oEAAoE;QACpE,qEAAqE;QACrE,uCAAuC;QACvC,IAAI,SAAS,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC;QACpD,CAAC;QACD,mDAAmD;QACnD,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;;OAUG;IACO,MAAM,CAAC,QAAQ;QACvB,IAAI,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;YACtE,OAAO;QACT,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,sDAAsD;QACtD,IAAI,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;YACvE,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;YAC9B,MAAM,QAAQ,GAAG;gBACf,GAAG,mBAAmB,CAAC,KAAK,CAAC;gBAC7B,GAAG,qBAAqB,CAAC,KAAK,CAAC;aACH,CAAC;YAC/B,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE,CAAC;gBACzB,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QAED,sDAAsD;QACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YACtB,MAAM,UAAU,GAAG,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACrD,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;gBAC7B,KAAK,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,UAAU,EAAE,CAAC;oBACtC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC;QACH,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,wBAAwB,GAAG,IAAI,GAAG,EAAE,CAAC;QAC1C,KAAK,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAClD,MAAM,IAAI,GAAG,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YACzD,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;gBACvB,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEtD,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC1C,YAAY,CACV,6BAA6B,EAC7B,6DAA6D;oBAC3D,0DAA0D,CAC7D,CAAC;YACJ,CAAC;YACD,IAAI,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACjD,YAAY,CACV,qCAAqC,EACrC,oEAAoE;oBAClE,0DAA0D,CAC7D,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAcD;;;;;;;;;;;;;OAaG;IACO,MAAM,CAAC,cAAc,CAC7B,MAAuB;QAEvB,MAAM,aAAa,GAAG,EAAE,CAAC;QACzB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,0EAA0E;YAC1E,uDAAuD;YACvD,gEAAgE;YAChE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAE,MAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YACzE,yEAAyE;YACzE,KAAK,MAAM,CAAC,IAAI,GAAG,EAAE,CAAC;gBACpB,aAAa,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAsB,CAAC,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YAChC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IASD;;;OAGG;IACK,MAAM,CAAC,0BAA0B,CACvC,IAAiB,EACjB,OAA4B;QAE5B,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,OAAO,SAAS,KAAK,KAAK;YACxB,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,OAAO,SAAS,KAAK,QAAQ;gBAC7B,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ;oBACxB,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;oBACpB,CAAC,CAAC,SAAS,CAAC;IACpB,CAAC;IAiDD;QACE,KAAK,EAAE,CAAC;QA9WF,yBAAoB,GAAoB,SAAS,CAAC;QAkU1D;;;;WAIG;QACH,oBAAe,GAAG,KAAK,CAAC;QAExB;;;;WAIG;QACH,eAAU,GAAG,KAAK,CAAC;QAqBnB;;WAEG;QACK,yBAAoB,GAAuB,IAAI,CAAC;QAStD,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED;;;OAGG;IACK,YAAY;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,OAAO,CAChC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,CACrC,CAAC;QACF,IAAI,CAAC,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;QACrC,yEAAyE;QACzE,wCAAwC;QACxC,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,4DAA4D;QAC5D,mBAAmB;QACnB,IAAI,CAAC,aAAa,EAAE,CAAC;QACpB,IAAI,CAAC,WAAsC,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CACxE,CAAC,CAAC,IAAI,CAAC,CACR,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,aAAa,CAAC,UAA8B;QAC1C,CAAC,IAAI,CAAC,aAAa,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACnD,iEAAiE;QACjE,oEAAoE;QACpE,mEAAmE;QACnE,yBAAyB;QACzB,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACtD,UAAU,CAAC,aAAa,EAAE,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,gBAAgB,CAAC,UAA8B;QAC7C,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAED;;;;;OAKG;IACK,wBAAwB;QAC9B,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAwB,CAAC;QAC3D,MAAM,iBAAiB,GAAI,IAAI,CAAC,WAAsC;aACnE,iBAAiB,CAAC;QACrB,KAAK,MAAM,CAAC,IAAI,iBAAiB,CAAC,IAAI,EAAkC,EAAE,CAAC;YACzE,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC3B,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnC,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC;QACH,CAAC;QACD,IAAI,kBAAkB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,oBAAoB,GAAG,kBAAkB,CAAC;QACjD,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACO,gBAAgB;QACxB,MAAM,UAAU,GACd,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,YAAY,CACd,IAAI,CAAC,WAAsC,CAAC,iBAAiB,CAC/D,CAAC;QACJ,WAAW,CACT,UAAU,EACT,IAAI,CAAC,WAAsC,CAAC,aAAa,CAC3D,CAAC;QACF,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACH,iBAAiB;QACf,uDAAuD;QACtD,IAA2C,CAAC,UAAU;YACrD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;OAKG;IACO,cAAc,CAAC,gBAAyB,IAAG,CAAC;IAEtD;;;;;OAKG;IACH,oBAAoB;QAClB,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;;;;OAWG;IACH,wBAAwB,CACtB,IAAY,EACZ,IAAmB,EACnB,KAAoB;QAEpB,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAEO,qBAAqB,CAAC,IAAiB,EAAE,KAAc;QAC7D,MAAM,cAAc,GAClB,IAAI,CAAC,WACN,CAAC,iBAAiB,CAAC;QACpB,MAAM,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;QAC1C,MAAM,IAAI,GACR,IAAI,CAAC,WACN,CAAC,0BAA0B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC5C,IAAI,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;YACnD,MAAM,SAAS,GACZ,OAAO,CAAC,SAAuC,EAAE,WAAW;gBAC7D,SAAS;gBACP,CAAC,CAAE,OAAO,CAAC,SAAuC;gBAClD,CAAC,CAAC,gBAAgB,CAAC;YACvB,MAAM,SAAS,GAAG,SAAS,CAAC,WAAY,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YAC9D,IACE,QAAQ;gBACP,IAAI,CAAC,WAAsC,CAAC,eAAgB,CAAC,QAAQ,CACpE,WAAW,CACZ;gBACD,SAAS,KAAK,SAAS,EACvB,CAAC;gBACD,YAAY,CACV,2BAA2B,EAC3B,+BAA+B,IAAc,eAAe;oBAC1D,wBAAwB,IAAI,CAAC,SAAS,0BAA0B;oBAChE,+DAA+D;oBAC/D,uCAAuC,CAC1C,CAAC;YACJ,CAAC;YACD,oDAAoD;YACpD,mEAAmE;YACnE,wEAAwE;YACxE,wEAAwE;YACxE,iEAAiE;YACjE,qEAAqE;YACrE,+BAA+B;YAC/B,wBAAwB;YACxB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACjC,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;gBACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,SAAmB,CAAC,CAAC;YAC/C,CAAC;YACD,4BAA4B;YAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACnC,CAAC;IACH,CAAC;IAED,gBAAgB;IAChB,qBAAqB,CAAC,IAAY,EAAE,KAAoB;QACtD,MAAM,IAAI,GAAG,IAAI,CAAC,WAAqC,CAAC;QACxD,sEAAsE;QACtE,8DAA8D;QAC9D,MAAM,QAAQ,GAAI,IAAI,CAAC,wBAAyC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3E,yEAAyE;QACzE,oDAAoD;QACpD,IAAI,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,oBAAoB,KAAK,QAAQ,EAAE,CAAC;YACrE,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,SAAS,GACb,OAAO,OAAO,CAAC,SAAS,KAAK,UAAU;gBACrC,CAAC,CAAC,EAAC,aAAa,EAAE,OAAO,CAAC,SAAS,EAAC;gBACpC,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,aAAa,KAAK,SAAS;oBAC9C,CAAC,CAAC,OAAO,CAAC,SAAS;oBACnB,CAAC,CAAC,gBAAgB,CAAC;YACzB,wBAAwB;YACxB,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC;YACrC,MAAM,cAAc,GAAG,SAAS,CAAC,aAAc,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YACrE,IAAI,CAAC,QAAsB,CAAC;gBAC1B,cAAc;oBACd,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,QAAQ,CAAC;oBACnC,8DAA8D;oBAC7D,cAAsB,CAAC;YAC1B,4BAA4B;YAC5B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,aAAa,CACX,IAAkB,EAClB,QAAkB,EAClB,OAA6B;QAE7B,4DAA4D;QAC5D,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,IAAI,QAAQ,IAAK,IAAgB,YAAY,KAAK,EAAE,CAAC;gBACnD,YAAY,CACV,EAAE,EACF,yPAAyP,CAC1P,CAAC;YACJ,CAAC;YACD,MAAM,IAAI,GAAG,IAAI,CAAC,WAAqC,CAAC;YACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAkB,CAAC,CAAC;YAC1C,OAAO,KAAK,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,OAAO,GACX,CAAC,OAAO,CAAC,UAAU,IAAI,QAAQ,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC;gBACpD,mEAAmE;gBACnE,qDAAqD;gBACrD,qDAAqD;gBACrD,yDAAyD;gBACzD,iEAAiE;gBACjE,kFAAkF;gBAClF,CAAC,OAAO,CAAC,UAAU;oBACjB,OAAO,CAAC,OAAO;oBACf,QAAQ,KAAK,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC;oBAC5C,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,OAAO,CAAE,CAAC,CAAC,CAAC;YACzE,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,sEAAsE;gBACtE,OAAO;YACT,CAAC;QACH,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE,CAAC;YACnC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAChD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB,CACd,IAAiB,EACjB,QAAiB,EACjB,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAsB,EACnD,eAAyB;QAEzB,kEAAkE;QAClE,oDAAoD;QACpD,IAAI,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAClE,IAAI,CAAC,eAAe,CAAC,GAAG,CACtB,IAAI,EACJ,eAAe,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAkB,CAAC,CACxD,CAAC;YACF,qEAAqE;YACrE,mEAAmE;YACnE,IAAI,OAAO,KAAK,IAAI,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;gBACtD,OAAO;YACT,CAAC;QACH,CAAC;QACD,mEAAmE;QACnE,oBAAoB;QACpB,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACxC,qEAAqE;YACrE,oBAAoB;YACpB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpC,QAAQ,GAAG,SAAS,CAAC;YACvB,CAAC;YACD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC/C,CAAC;QACD,oCAAoC;QACpC,iEAAiE;QACjE,6DAA6D;QAC7D,2CAA2C;QAC3C,IAAI,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE,CAAC;YAC3D,CAAC,IAAI,CAAC,sBAAsB,KAAK,IAAI,GAAG,EAAe,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC;YACH,2DAA2D;YAC3D,+DAA+D;YAC/D,MAAM,IAAI,CAAC,eAAe,CAAC;QAC7B,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,qEAAqE;YACrE,mEAAmE;YACnE,6CAA6C;YAC7C,iCAAiC;YACjC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACrC,sEAAsE;QACtE,oEAAoE;QACpE,uEAAuE;QACvE,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,MAAM,MAAM,CAAC;QACf,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;IAC/B,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACO,cAAc;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACpC,IACE,QAAQ;YACP,IAAI,CAAC,WAAsC,CAAC,eAAgB,CAAC,QAAQ,CACpE,sBAAsB,CACvB;YACD,OAAQ,MAAkD,EAAE,IAAI;gBAC9D,UAAU,EACZ,CAAC;YACD,YAAY,CACV,sBAAsB,EACtB,WAAW,IAAI,CAAC,SAAS,4CAA4C;gBACnE,8DAA8D;gBAC9D,6BAA6B,CAChC,CAAC;QACJ,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG;IACO,aAAa;QACrB,8DAA8D;QAC9D,gEAAgE;QAChE,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,OAAO;QACT,CAAC;QACD,aAAa,EAAE,CAAC,EAAC,IAAI,EAAE,QAAQ,EAAC,CAAC,CAAC;QAClC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,4EAA4E;YAC5E,qFAAqF;YACpF,IAA2C,CAAC,UAAU;gBACrD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,QAAQ,EAAE,CAAC;gBACb,kEAAkE;gBAClE,mEAAmE;gBACnE,sEAAsE;gBACtE,qCAAqC;gBACrC,MAAM,IAAI,GAAG,IAAI,CAAC,WAAqC,CAAC;gBACxD,MAAM,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAClE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,CAC3D,CAAC;gBACF,IAAI,kBAAkB,CAAC,MAAM,EAAE,CAAC;oBAC9B,MAAM,IAAI,KAAK,CACb,uCAAuC,IAAI,CAAC,SAAS,YAAY;wBAC/D,+DAA+D;wBAC/D,WAAW,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;wBAC5C,8DAA8D;wBAC9D,4CAA4C;wBAC5C,4CAA4C;wBAC5C,uBAAuB,CAC1B,CAAC;gBACJ,CAAC;YACH,CAAC;YACD,iDAAiD;YACjD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC9B,0EAA0E;gBAC1E,wDAAwD;gBACxD,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBACnD,IAAI,CAAC,CAAe,CAAC,GAAG,KAAyB,CAAC;gBACpD,CAAC;gBACD,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;YACxC,CAAC;YACD,4DAA4D;YAC5D,uEAAuE;YACvE,iEAAiE;YACjE,gEAAgE;YAChE,oEAAoE;YACpE,wEAAwE;YACxE,yCAAyC;YACzC,mBAAmB;YACnB,+DAA+D;YAC/D,MAAM,iBAAiB,GAAI,IAAI,CAAC,WAAsC;iBACnE,iBAAiB,CAAC;YACrB,IAAI,iBAAiB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC/B,KAAK,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,iBAAiB,EAAE,CAAC;oBAC7C,MAAM,EAAC,OAAO,EAAC,GAAG,OAAO,CAAC;oBAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,CAAe,CAAC,CAAC;oBACpC,IACE,OAAO,KAAK,IAAI;wBAChB,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;wBAChC,KAAK,KAAK,SAAS,EACnB,CAAC;wBACD,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;oBACtD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,YAAY,GAAG,KAAK,CAAC;QACzB,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACnD,IAAI,CAAC;YACH,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;YACpD,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;gBACnC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;gBACrD,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,oEAAoE;YACpE,oBAAoB;YACpB,YAAY,GAAG,KAAK,CAAC;YACrB,mEAAmE;YACnE,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,MAAM,CAAC,CAAC;QACV,CAAC;QACD,kFAAkF;QAClF,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACO,UAAU,CAAC,kBAAkC,IAAS,CAAC;IAEjE,wDAAwD;IACxD,YAAY;IACZ,WAAW,CAAC,iBAAiC;QAC3C,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QACtD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAChC,IACE,QAAQ;YACR,IAAI,CAAC,eAAe;YACnB,IAAI,CAAC,WAAsC,CAAC,eAAgB,CAAC,QAAQ,CACpE,kBAAkB,CACnB,EACD,CAAC;YACD,YAAY,CACV,kBAAkB,EAClB,WAAW,IAAI,CAAC,SAAS,uBAAuB;gBAC9C,yCAAyC;gBACzC,mEAAmE;gBACnE,mEAAmE;gBACnE,gEAAgE,CACnE,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAClC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACO,iBAAiB;QACzB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;;;;;;OAOG;IACO,YAAY,CAAC,kBAAkC;QACvD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;OAQG;IACO,MAAM,CAAC,kBAAkC;QACjD,wEAAwE;QACxE,uEAAuE;QACvE,YAAY;QACZ,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CACxE,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAe,CAAC,CAAC,CACxC,CAAC;QACf,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED;;;;;;;;;OASG;IACO,OAAO,CAAC,kBAAkC,IAAG,CAAC;IAExD;;;;;;;;;;;;;;;OAeG;IACO,YAAY,CAAC,kBAAkC,IAAG,CAAC;;AAziC7D;;;;;GAKG;AACI,6BAAa,GAA6B,EAAE,AAA/B,CAAgC;AAuSpD;;;;;;;;;GASG;AACI,iCAAiB,GAAmB,EAAC,IAAI,EAAE,MAAM,EAAC,AAAjC,CAAkC;AAovB5D,mEAAmE;AACnE,yBAAyB;AACzB,yDAAyD;AACxD,eAAsD,CACrD,yBAAyB,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAChE,GAAG,IAAI,GAAG,EAAE,CAAC;AACb,eAAsD,CACrD,yBAAyB,CAAC,WAAW,EAAE,eAAe,CAAC,CACxD,GAAG,IAAI,GAAG,EAAE,CAAC;AAEd,+BAA+B;AAC/B,eAAe,EAAE,CAAC,EAAC,eAAe,EAAC,CAAC,CAAC;AAErC,uBAAuB;AACvB,IAAI,QAAQ,EAAE,CAAC;IACb,uBAAuB;IACvB,eAAe,CAAC,eAAe,GAAG;QAChC,kBAAkB;QAClB,sBAAsB;KACvB,CAAC;IACF,MAAM,iBAAiB,GAAG,UAAU,IAA4B;QAC9D,IACE,CAAC,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,EACxE,CAAC;YACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAgB,CAAC,KAAK,EAAE,CAAC;QACvD,CAAC;IACH,CAAC,CAAC;IACF,eAAe,CAAC,aAAa,GAAG,UAE9B,OAAoB;QAEpB,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,eAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,eAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;IACH,CAAC,CAAC;IACF,eAAe,CAAC,cAAc,GAAG,UAE/B,OAAoB;QAEpB,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACxB,MAAM,CAAC,GAAG,IAAI,CAAC,eAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,eAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAED,2EAA2E;AAC3E,yEAAyE;AACzE,CAAC,MAAM,CAAC,uBAAuB,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACtD,IAAI,QAAQ,IAAI,MAAM,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;IAC1D,cAAc,CAAC,GAAG,EAAE;QAClB,YAAa,CACX,mBAAmB,EACnB,6DAA6D;YAC3D,qBAAqB,CACxB,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Use this module if you want to create your own base class extending\n * {@link ReactiveElement}.\n * @packageDocumentation\n */\n\nimport {\n  getCompatibleStyle,\n  adoptStyles,\n  CSSResultGroup,\n  CSSResultOrNative,\n} from './css-tag.js';\nimport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from './reactive-controller.js';\n\n// In the Node build, this import will be injected by Rollup:\n// import {HTMLElement, customElements} from '@lit-labs/ssr-dom-shim';\n\nexport * from './css-tag.js';\nexport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from './reactive-controller.js';\n\n/**\n * Removes the `readonly` modifier from properties in the union K.\n *\n * This is a safer way to cast a value to a type with a mutable version of a\n * readonly field, than casting to an interface with the field re-declared\n * because it preserves the type of all the fields and warns on typos.\n */\ntype Mutable<T, K extends keyof T> = Omit<T, K> & {\n  -readonly [P in keyof Pick<T, K>]: P extends K ? T[P] : never;\n};\n\n// TODO (justinfagnani): Add `hasOwn` here when we ship ES2022\nconst {\n  is,\n  defineProperty,\n  getOwnPropertyDescriptor,\n  getOwnPropertyNames,\n  getOwnPropertySymbols,\n  getPrototypeOf,\n} = Object;\n\nconst NODE_MODE = false;\n\n// Lets a minifier replace globalThis references with a minified name\nconst global = globalThis;\n\nif (NODE_MODE) {\n  global.customElements ??= customElements;\n}\n\nconst DEV_MODE = true;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nconst trustedTypes = (global as unknown as {trustedTypes?: {emptyScript: ''}})\n  .trustedTypes;\n\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n  ? (trustedTypes.emptyScript as unknown as '')\n  : '';\n\nconst polyfillSupport = DEV_MODE\n  ? global.reactiveElementPolyfillSupportDevMode\n  : global.reactiveElementPolyfillSupport;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  global.litIssuedWarnings ??= new Set();\n\n  /**\n   * Issue a warning if we haven't already, based either on `code` or `warning`.\n   * Warnings are disabled automatically only by `warning`; disabling via `code`\n   * can be done by users.\n   */\n  issueWarning = (code: string, warning: string) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (\n      !global.litIssuedWarnings!.has(warning) &&\n      !global.litIssuedWarnings!.has(code)\n    ) {\n      console.warn(warning);\n      global.litIssuedWarnings!.add(warning);\n    }\n  };\n\n  queueMicrotask(() => {\n    issueWarning(\n      'dev-mode',\n      `Lit is in dev mode. Not recommended for production!`\n    );\n\n    // Issue polyfill support warning.\n    if (global.ShadyDOM?.inUse && polyfillSupport === undefined) {\n      issueWarning(\n        'polyfill-support-missing',\n        `Shadow DOM is being polyfilled via \\`ShadyDOM\\` but ` +\n          `the \\`polyfill-support\\` module has not been loaded.`\n      );\n    }\n  });\n}\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace ReactiveUnstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry = Update;\n    export interface Update {\n      kind: 'update';\n    }\n  }\n}\n\ninterface DebugLoggingWindow {\n  // Even in dev mode, we generally don't want to emit these events, as that's\n  // another level of cost, so only emit them when DEV_MODE is true _and_ when\n  // window.emitLitDebugEvents is true.\n  emitLitDebugLogEvents?: boolean;\n}\n\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n  ? (event: ReactiveUnstable.DebugLog.Entry) => {\n      const shouldEmit = (global as unknown as DebugLoggingWindow)\n        .emitLitDebugLogEvents;\n      if (!shouldEmit) {\n        return;\n      }\n      global.dispatchEvent(\n        new CustomEvent<ReactiveUnstable.DebugLog.Entry>('lit-debug', {\n          detail: event,\n        })\n      );\n    }\n  : undefined;\n\n/*\n * When using Closure Compiler, JSCompiler_renameProperty(property, object) is\n * replaced at compile time by the munged name for object[property]. We cannot\n * alias this function, so we have to use a small shim that has the same\n * behavior when not compiling.\n */\n/*@__INLINE__*/\nconst JSCompiler_renameProperty = <P extends PropertyKey>(\n  prop: P,\n  _obj: unknown\n): P => prop;\n\n/**\n * Converts property values to and from attribute values.\n */\nexport interface ComplexAttributeConverter<Type = unknown, TypeHint = unknown> {\n  /**\n   * Called to convert an attribute value to a property\n   * value.\n   */\n  fromAttribute?(value: string | null, type?: TypeHint): Type;\n\n  /**\n   * Called to convert a property value to an attribute\n   * value.\n   *\n   * It returns unknown instead of string, to be compatible with\n   * https://github.com/WICG/trusted-types (and similar efforts).\n   */\n  toAttribute?(value: Type, type?: TypeHint): unknown;\n}\n\ntype AttributeConverter<Type = unknown, TypeHint = unknown> =\n  | ComplexAttributeConverter<Type>\n  | ((value: string | null, type?: TypeHint) => Type);\n\n/**\n * Defines options for a property accessor.\n */\nexport interface PropertyDeclaration<Type = unknown, TypeHint = unknown> {\n  /**\n   * When set to `true`, indicates the property is internal private state. The\n   * property should not be set by users. When using TypeScript, this property\n   * should be marked as `private` or `protected`, and it is also a common\n   * practice to use a leading `_` in the name. The property is not added to\n   * `observedAttributes`.\n   */\n  readonly state?: boolean;\n\n  /**\n   * Indicates how and whether the property becomes an observed attribute.\n   * If the value is `false`, the property is not added to `observedAttributes`.\n   * If true or absent, the lowercased property name is observed (e.g. `fooBar`\n   * becomes `foobar`). If a string, the string value is observed (e.g\n   * `attribute: 'foo-bar'`).\n   */\n  readonly attribute?: boolean | string;\n\n  /**\n   * Indicates the type of the property. This is used only as a hint for the\n   * `converter` to determine how to convert the attribute\n   * to/from a property.\n   */\n  readonly type?: TypeHint;\n\n  /**\n   * Indicates how to convert the attribute to/from a property. If this value\n   * is a function, it is used to convert the attribute value a the property\n   * value. If it's an object, it can have keys for `fromAttribute` and\n   * `toAttribute`. If no `toAttribute` function is provided and\n   * `reflect` is set to `true`, the property value is set directly to the\n   * attribute. A default `converter` is used if none is provided; it supports\n   * `Boolean`, `String`, `Number`, `Object`, and `Array`. Note,\n   * when a property changes and the converter is used to update the attribute,\n   * the property is never updated again as a result of the attribute changing,\n   * and vice versa.\n   */\n  readonly converter?: AttributeConverter<Type, TypeHint>;\n\n  /**\n   * Indicates if the property should reflect to an attribute.\n   * If `true`, when the property is set, the attribute is set using the\n   * attribute name determined according to the rules for the `attribute`\n   * property option and the value of the property converted using the rules\n   * from the `converter` property option.\n   */\n  readonly reflect?: boolean;\n\n  /**\n   * A function that indicates if a property should be considered changed when\n   * it is set. The function should take the `newValue` and `oldValue` and\n   * return `true` if an update should be requested.\n   */\n  hasChanged?(value: Type, oldValue: Type): boolean;\n\n  /**\n   * Indicates whether an accessor will be created for this property. By\n   * default, an accessor will be generated for this property that requests an\n   * update when set. If this flag is `true`, no accessor will be created, and\n   * it will be the user's responsibility to call\n   * `this.requestUpdate(propertyName, oldValue)` to request an update when\n   * the property changes.\n   */\n  readonly noAccessor?: boolean;\n\n  /**\n   * Whether this property is wrapping accessors. This is set by `@property`\n   * to control the initial value change and reflection logic.\n   *\n   * @internal\n   */\n  wrapped?: boolean;\n\n  /**\n   * When `true`, uses the initial value of the property as the default value,\n   * which changes how attributes are handled:\n   *  - The initial value does *not* reflect, even if the `reflect` option is `true`.\n   *    Subsequent changes to the property will reflect, even if they are equal to the\n   *     default value.\n   *  - When the attribute is removed, the property is set to the default value\n   *  - The initial value will not trigger an old value in the `changedProperties` map\n   *    argument to update lifecycle methods.\n   *\n   * When set, properties must be initialized, either with a field initializer, or an\n   * assignment in the constructor. Not initializing the property may lead to\n   * improper handling of subsequent property assignments.\n   *\n   * While this behavior is opt-in, most properties that reflect to attributes should\n   * use `useDefault: true` so that their initial values do not reflect.\n   */\n  useDefault?: boolean;\n}\n\n/**\n * Map of properties to PropertyDeclaration options. For each property an\n * accessor is made, and the property is processed according to the\n * PropertyDeclaration options.\n */\nexport interface PropertyDeclarations {\n  readonly [key: string]: PropertyDeclaration;\n}\n\ntype PropertyDeclarationMap = Map<PropertyKey, PropertyDeclaration>;\n\ntype AttributeMap = Map<string, PropertyKey>;\n\n/**\n * A Map of property keys to values.\n *\n * Takes an optional type parameter T, which when specified as a non-any,\n * non-unknown type, will make the Map more strongly-typed, associating the map\n * keys with their corresponding value type on T.\n *\n * Use `PropertyValues<this>` when overriding ReactiveElement.update() and\n * other lifecycle methods in order to get stronger type-checking on keys\n * and values.\n */\n// This type is conditional so that if the parameter T is not specified, or\n// is `any`, the type will include `Map<PropertyKey, unknown>`. Since T is not\n// given in the uses of PropertyValues in this file, all uses here fallback to\n// meaning `Map<PropertyKey, unknown>`, but if a developer uses\n// `PropertyValues<this>` (or any other value for T) they will get a\n// strongly-typed Map type.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type PropertyValues<T = any> = T extends object\n  ? PropertyValueMap<T>\n  : Map<PropertyKey, unknown>;\n\n/**\n * Do not use, instead prefer {@linkcode PropertyValues}.\n */\n// This type must be exported such that JavaScript generated by the Google\n// Closure Compiler can import a type reference.\nexport interface PropertyValueMap<T> extends Map<PropertyKey, unknown> {\n  get<K extends keyof T>(k: K): T[K] | undefined;\n  set<K extends keyof T>(key: K, value: T[K]): this;\n  has<K extends keyof T>(k: K): boolean;\n  delete<K extends keyof T>(k: K): boolean;\n}\n\nexport const defaultConverter: ComplexAttributeConverter = {\n  toAttribute(value: unknown, type?: unknown): unknown {\n    switch (type) {\n      case Boolean:\n        value = value ? emptyStringForBooleanAttribute : null;\n        break;\n      case Object:\n      case Array:\n        // if the value is `null` or `undefined` pass this through\n        // to allow removing/no change behavior.\n        value = value == null ? value : JSON.stringify(value);\n        break;\n    }\n    return value;\n  },\n\n  fromAttribute(value: string | null, type?: unknown) {\n    let fromValue: unknown = value;\n    switch (type) {\n      case Boolean:\n        fromValue = value !== null;\n        break;\n      case Number:\n        fromValue = value === null ? null : Number(value);\n        break;\n      case Object:\n      case Array:\n        // Do *not* generate exception when invalid JSON is set as elements\n        // don't normally complain on being mis-configured.\n        // TODO(sorvell): Do generate exception in *dev mode*.\n        try {\n          // Assert to adhere to Bazel's \"must type assert JSON parse\" rule.\n          fromValue = JSON.parse(value!) as unknown;\n        } catch (e) {\n          fromValue = null;\n        }\n        break;\n    }\n    return fromValue;\n  },\n};\n\nexport interface HasChanged {\n  (value: unknown, old: unknown): boolean;\n}\n\n/**\n * Change function that returns true if `value` is different from `oldValue`.\n * This method is used as the default for a property's `hasChanged` function.\n */\nexport const notEqual: HasChanged = (value: unknown, old: unknown): boolean =>\n  !is(value, old);\n\nconst defaultPropertyDeclaration: PropertyDeclaration = {\n  attribute: true,\n  type: String,\n  converter: defaultConverter,\n  reflect: false,\n  useDefault: false,\n  hasChanged: notEqual,\n};\n\n/**\n * A string representing one of the supported dev mode warning categories.\n */\nexport type WarningKind =\n  | 'change-in-update'\n  | 'migration'\n  | 'async-perform-update';\n\nexport type Initializer = (element: ReactiveElement) => void;\n\n// Temporary, until google3 is on TypeScript 5.2\ndeclare global {\n  interface SymbolConstructor {\n    readonly metadata: unique symbol;\n  }\n}\n\n// Ensure metadata is enabled. TypeScript does not polyfill\n// Symbol.metadata, so we must ensure that it exists.\n(Symbol as {metadata: symbol}).metadata ??= Symbol('metadata');\n\ndeclare global {\n  // This is public global API, do not change!\n  // eslint-disable-next-line no-var\n  var litPropertyMetadata: WeakMap<\n    object,\n    Map<PropertyKey, PropertyDeclaration>\n  >;\n}\n\n// Map from a class's metadata object to property options\n// Note that we must use nullish-coalescing assignment so that we only use one\n// map even if we load multiple version of this module.\nglobal.litPropertyMetadata ??= new WeakMap<\n  object,\n  Map<PropertyKey, PropertyDeclaration>\n>();\n\n/**\n * Base element class which manages element properties and attributes. When\n * properties change, the `update` method is asynchronously called. This method\n * should be supplied by subclasses to render updates as desired.\n * @noInheritDoc\n */\nexport abstract class ReactiveElement\n  // In the Node build, this `extends` clause will be substituted with\n  // `(globalThis.HTMLElement ?? HTMLElement)`.\n  //\n  // This way, we will first prefer any global `HTMLElement` polyfill that the\n  // user has assigned, and then fall back to the `HTMLElement` shim which has\n  // been imported (see note at the top of this file about how this import is\n  // generated by Rollup). Note that the `HTMLElement` variable has been\n  // shadowed by this import, so it no longer refers to the global.\n  extends HTMLElement\n  implements ReactiveControllerHost\n{\n  // Note: these are patched in only in DEV_MODE.\n  /**\n   * Read or set all the enabled warning categories for this class.\n   *\n   * This property is only used in development builds.\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static enabledWarnings?: WarningKind[];\n\n  /**\n   * Enable the given warning category for this class.\n   *\n   * This method only exists in development builds, so it should be accessed\n   * with a guard like:\n   *\n   * ```ts\n   * // Enable for all ReactiveElement subclasses\n   * ReactiveElement.enableWarning?.('migration');\n   *\n   * // Enable for only MyElement and subclasses\n   * MyElement.enableWarning?.('migration');\n   * ```\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static enableWarning?: (warningKind: WarningKind) => void;\n\n  /**\n   * Disable the given warning category for this class.\n   *\n   * This method only exists in development builds, so it should be accessed\n   * with a guard like:\n   *\n   * ```ts\n   * // Disable for all ReactiveElement subclasses\n   * ReactiveElement.disableWarning?.('migration');\n   *\n   * // Disable for only MyElement and subclasses\n   * MyElement.disableWarning?.('migration');\n   * ```\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static disableWarning?: (warningKind: WarningKind) => void;\n\n  /**\n   * Adds an initializer function to the class that is called during instance\n   * construction.\n   *\n   * This is useful for code that runs against a `ReactiveElement`\n   * subclass, such as a decorator, that needs to do work for each\n   * instance, such as setting up a `ReactiveController`.\n   *\n   * ```ts\n   * const myDecorator = (target: typeof ReactiveElement, key: string) => {\n   *   target.addInitializer((instance: ReactiveElement) => {\n   *     // This is run during construction of the element\n   *     new MyController(instance);\n   *   });\n   * }\n   * ```\n   *\n   * Decorating a field will then cause each instance to run an initializer\n   * that adds a controller:\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   @myDecorator foo;\n   * }\n   * ```\n   *\n   * Initializers are stored per-constructor. Adding an initializer to a\n   * subclass does not add it to a superclass. Since initializers are run in\n   * constructors, initializers will run in order of the class hierarchy,\n   * starting with superclasses and progressing to the instance's class.\n   *\n   * @nocollapse\n   */\n  static addInitializer(initializer: Initializer) {\n    this.__prepare();\n    (this._initializers ??= []).push(initializer);\n  }\n\n  static _initializers?: Initializer[];\n\n  /*\n   * Due to closure compiler ES6 compilation bugs, @nocollapse is required on\n   * all static methods and properties with initializers.  Reference:\n   * - https://github.com/google/closure-compiler/issues/1776\n   */\n\n  /**\n   * Maps attribute names to properties; for example `foobar` attribute to\n   * `fooBar` property. Created lazily on user subclasses when finalizing the\n   * class.\n   * @nocollapse\n   */\n  private static __attributeToPropertyMap: AttributeMap;\n\n  /**\n   * Marks class as having been finalized, which includes creating properties\n   * from `static properties`, but does *not* include all properties created\n   * from decorators.\n   * @nocollapse\n   */\n  protected static finalized: true | undefined;\n\n  /**\n   * Memoized list of all element properties, including any superclass\n   * properties. Created lazily on user subclasses when finalizing the class.\n   *\n   * @nocollapse\n   * @category properties\n   */\n  static elementProperties: PropertyDeclarationMap;\n\n  /**\n   * User-supplied object that maps property names to `PropertyDeclaration`\n   * objects containing options for configuring reactive properties. When\n   * a reactive property is set the element will update and render.\n   *\n   * By default properties are public fields, and as such, they should be\n   * considered as primarily settable by element users, either via attribute or\n   * the property itself.\n   *\n   * Generally, properties that are changed by the element should be private or\n   * protected fields and should use the `state: true` option. Properties\n   * marked as `state` do not reflect from the corresponding attribute\n   *\n   * However, sometimes element code does need to set a public property. This\n   * should typically only be done in response to user interaction, and an event\n   * should be fired informing the user; for example, a checkbox sets its\n   * `checked` property when clicked and fires a `changed` event. Mutating\n   * public properties should typically not be done for non-primitive (object or\n   * array) properties. In other cases when an element needs to manage state, a\n   * private property set with the `state: true` option should be used. When\n   * needed, state properties can be initialized via public properties to\n   * facilitate complex interactions.\n   * @nocollapse\n   * @category properties\n   */\n  static properties: PropertyDeclarations;\n\n  /**\n   * Memoized list of all element styles.\n   * Created lazily on user subclasses when finalizing the class.\n   * @nocollapse\n   * @category styles\n   */\n  static elementStyles: Array<CSSResultOrNative> = [];\n\n  /**\n   * Array of styles to apply to the element. The styles should be defined\n   * using the {@linkcode css} tag function, via constructible stylesheets, or\n   * imported from native CSS module scripts.\n   *\n   * Note on Content Security Policy:\n   *\n   * Element styles are implemented with `<style>` tags when the browser doesn't\n   * support adopted StyleSheets. To use such `<style>` tags with the style-src\n   * CSP directive, the style-src value must either include 'unsafe-inline' or\n   * `nonce-<base64-value>` with `<base64-value>` replaced be a server-generated\n   * nonce.\n   *\n   * To provide a nonce to use on generated `<style>` elements, set\n   * `window.litNonce` to a server-generated nonce in your page's HTML, before\n   * loading application code:\n   *\n   * ```html\n   * <script>\n   *   // Generated and unique per request:\n   *   window.litNonce = 'a1b2c3d4';\n   * </script>\n   * ```\n   * @nocollapse\n   * @category styles\n   */\n  static styles?: CSSResultGroup;\n\n  /**\n   * Returns a list of attributes corresponding to the registered properties.\n   * @nocollapse\n   * @category attributes\n   */\n  static get observedAttributes() {\n    // Ensure we've created all properties\n    this.finalize();\n    // this.__attributeToPropertyMap is only undefined after finalize() in\n    // ReactiveElement itself. ReactiveElement.observedAttributes is only\n    // accessed with ReactiveElement as the receiver when a subclass or mixin\n    // calls super.observedAttributes\n    return (\n      this.__attributeToPropertyMap && [...this.__attributeToPropertyMap.keys()]\n    );\n  }\n\n  private __instanceProperties?: PropertyValues = undefined;\n\n  /**\n   * Creates a property accessor on the element prototype if one does not exist\n   * and stores a {@linkcode PropertyDeclaration} for the property with the\n   * given options. The property setter calls the property's `hasChanged`\n   * property option or uses a strict identity check to determine whether or not\n   * to request an update.\n   *\n   * This method may be overridden to customize properties; however,\n   * when doing so, it's important to call `super.createProperty` to ensure\n   * the property is setup correctly. This method calls\n   * `getPropertyDescriptor` internally to get a descriptor to install.\n   * To customize what properties do when they are get or set, override\n   * `getPropertyDescriptor`. To customize the options for a property,\n   * implement `createProperty` like this:\n   *\n   * ```ts\n   * static createProperty(name, options) {\n   *   options = Object.assign(options, {myOption: true});\n   *   super.createProperty(name, options);\n   * }\n   * ```\n   *\n   * @nocollapse\n   * @category properties\n   */\n  static createProperty(\n    name: PropertyKey,\n    options: PropertyDeclaration = defaultPropertyDeclaration\n  ) {\n    // If this is a state property, force the attribute to false.\n    if (options.state) {\n      (options as Mutable<PropertyDeclaration, 'attribute'>).attribute = false;\n    }\n    this.__prepare();\n    // Whether this property is wrapping accessors.\n    // Helps control the initial value change and reflection logic.\n    if (this.prototype.hasOwnProperty(name)) {\n      options = Object.create(options);\n      options.wrapped = true;\n    }\n    this.elementProperties.set(name, options);\n    if (!options.noAccessor) {\n      const key = DEV_MODE\n        ? // Use Symbol.for in dev mode to make it easier to maintain state\n          // when doing HMR.\n          Symbol.for(`${String(name)} (@property() cache)`)\n        : Symbol();\n      const descriptor = this.getPropertyDescriptor(name, key, options);\n      if (descriptor !== undefined) {\n        defineProperty(this.prototype, name, descriptor);\n      }\n    }\n  }\n\n  /**\n   * Returns a property descriptor to be defined on the given named property.\n   * If no descriptor is returned, the property will not become an accessor.\n   * For example,\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   static getPropertyDescriptor(name, key, options) {\n   *     const defaultDescriptor =\n   *         super.getPropertyDescriptor(name, key, options);\n   *     const setter = defaultDescriptor.set;\n   *     return {\n   *       get: defaultDescriptor.get,\n   *       set(value) {\n   *         setter.call(this, value);\n   *         // custom action.\n   *       },\n   *       configurable: true,\n   *       enumerable: true\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * @nocollapse\n   * @category properties\n   */\n  protected static getPropertyDescriptor(\n    name: PropertyKey,\n    key: string | symbol,\n    options: PropertyDeclaration\n  ): PropertyDescriptor | undefined {\n    const {get, set} = getOwnPropertyDescriptor(this.prototype, name) ?? {\n      get(this: ReactiveElement) {\n        return this[key as keyof typeof this];\n      },\n      set(this: ReactiveElement, v: unknown) {\n        (this as unknown as Record<string | symbol, unknown>)[key] = v;\n      },\n    };\n    if (DEV_MODE && get == null) {\n      if ('value' in (getOwnPropertyDescriptor(this.prototype, name) ?? {})) {\n        throw new Error(\n          `Field ${JSON.stringify(String(name))} on ` +\n            `${this.name} was declared as a reactive property ` +\n            `but it's actually declared as a value on the prototype. ` +\n            `Usually this is due to using @property or @state on a method.`\n        );\n      }\n      issueWarning(\n        'reactive-property-without-getter',\n        `Field ${JSON.stringify(String(name))} on ` +\n          `${this.name} was declared as a reactive property ` +\n          `but it does not have a getter. This will be an error in a ` +\n          `future version of Lit.`\n      );\n    }\n    return {\n      get,\n      set(this: ReactiveElement, value: unknown) {\n        const oldValue = get?.call(this);\n        set?.call(this, value);\n        this.requestUpdate(name, oldValue, options);\n      },\n      configurable: true,\n      enumerable: true,\n    };\n  }\n\n  /**\n   * Returns the property options associated with the given property.\n   * These options are defined with a `PropertyDeclaration` via the `properties`\n   * object or the `@property` decorator and are registered in\n   * `createProperty(...)`.\n   *\n   * Note, this method should be considered \"final\" and not overridden. To\n   * customize the options for a given property, override\n   * {@linkcode createProperty}.\n   *\n   * @nocollapse\n   * @final\n   * @category properties\n   */\n  static getPropertyOptions(name: PropertyKey) {\n    return this.elementProperties.get(name) ?? defaultPropertyDeclaration;\n  }\n\n  // Temporary, until google3 is on TypeScript 5.2\n  declare static [Symbol.metadata]: object & Record<PropertyKey, unknown>;\n\n  /**\n   * Initializes static own properties of the class used in bookkeeping\n   * for element properties, initializers, etc.\n   *\n   * Can be called multiple times by code that needs to ensure these\n   * properties exist before using them.\n   *\n   * This method ensures the superclass is finalized so that inherited\n   * property metadata can be copied down.\n   * @nocollapse\n   */\n  private static __prepare() {\n    if (\n      this.hasOwnProperty(JSCompiler_renameProperty('elementProperties', this))\n    ) {\n      // Already prepared\n      return;\n    }\n    // Finalize any superclasses\n    const superCtor = getPrototypeOf(this) as typeof ReactiveElement;\n    superCtor.finalize();\n\n    // Create own set of initializers for this class if any exist on the\n    // superclass and copy them down. Note, for a small perf boost, avoid\n    // creating initializers unless needed.\n    if (superCtor._initializers !== undefined) {\n      this._initializers = [...superCtor._initializers];\n    }\n    // Initialize elementProperties from the superclass\n    this.elementProperties = new Map(superCtor.elementProperties);\n  }\n\n  /**\n   * Finishes setting up the class so that it's ready to be registered\n   * as a custom element and instantiated.\n   *\n   * This method is called by the ReactiveElement.observedAttributes getter.\n   * If you override the observedAttributes getter, you must either call\n   * super.observedAttributes to trigger finalization, or call finalize()\n   * yourself.\n   *\n   * @nocollapse\n   */\n  protected static finalize() {\n    if (this.hasOwnProperty(JSCompiler_renameProperty('finalized', this))) {\n      return;\n    }\n    this.finalized = true;\n    this.__prepare();\n\n    // Create properties from the static properties block:\n    if (this.hasOwnProperty(JSCompiler_renameProperty('properties', this))) {\n      const props = this.properties;\n      const propKeys = [\n        ...getOwnPropertyNames(props),\n        ...getOwnPropertySymbols(props),\n      ] as Array<keyof typeof props>;\n      for (const p of propKeys) {\n        this.createProperty(p, props[p]);\n      }\n    }\n\n    // Create properties from standard decorator metadata:\n    const metadata = this[Symbol.metadata];\n    if (metadata !== null) {\n      const properties = litPropertyMetadata.get(metadata);\n      if (properties !== undefined) {\n        for (const [p, options] of properties) {\n          this.elementProperties.set(p, options);\n        }\n      }\n    }\n\n    // Create the attribute-to-property map\n    this.__attributeToPropertyMap = new Map();\n    for (const [p, options] of this.elementProperties) {\n      const attr = this.__attributeNameForProperty(p, options);\n      if (attr !== undefined) {\n        this.__attributeToPropertyMap.set(attr, p);\n      }\n    }\n\n    this.elementStyles = this.finalizeStyles(this.styles);\n\n    if (DEV_MODE) {\n      if (this.hasOwnProperty('createProperty')) {\n        issueWarning(\n          'no-override-create-property',\n          'Overriding ReactiveElement.createProperty() is deprecated. ' +\n            'The override will not be called with standard decorators'\n        );\n      }\n      if (this.hasOwnProperty('getPropertyDescriptor')) {\n        issueWarning(\n          'no-override-get-property-descriptor',\n          'Overriding ReactiveElement.getPropertyDescriptor() is deprecated. ' +\n            'The override will not be called with standard decorators'\n        );\n      }\n    }\n  }\n\n  /**\n   * Options used when calling `attachShadow`. Set this property to customize\n   * the options for the shadowRoot; for example, to create a closed\n   * shadowRoot: `{mode: 'closed'}`.\n   *\n   * Note, these options are used in `createRenderRoot`. If this method\n   * is customized, options should be respected if possible.\n   * @nocollapse\n   * @category rendering\n   */\n  static shadowRootOptions: ShadowRootInit = {mode: 'open'};\n\n  /**\n   * Takes the styles the user supplied via the `static styles` property and\n   * returns the array of styles to apply to the element.\n   * Override this method to integrate into a style management system.\n   *\n   * Styles are deduplicated preserving the _last_ instance in the list. This\n   * is a performance optimization to avoid duplicated styles that can occur\n   * especially when composing via subclassing. The last item is kept to try\n   * to preserve the cascade order with the assumption that it's most important\n   * that last added styles override previous styles.\n   *\n   * @nocollapse\n   * @category styles\n   */\n  protected static finalizeStyles(\n    styles?: CSSResultGroup\n  ): Array<CSSResultOrNative> {\n    const elementStyles = [];\n    if (Array.isArray(styles)) {\n      // Dedupe the flattened array in reverse order to preserve the last items.\n      // Casting to Array<unknown> works around TS error that\n      // appears to come from trying to flatten a type CSSResultArray.\n      const set = new Set((styles as Array<unknown>).flat(Infinity).reverse());\n      // Then preserve original order by adding the set items in reverse order.\n      for (const s of set) {\n        elementStyles.unshift(getCompatibleStyle(s as CSSResultOrNative));\n      }\n    } else if (styles !== undefined) {\n      elementStyles.push(getCompatibleStyle(styles));\n    }\n    return elementStyles;\n  }\n\n  /**\n   * Node or ShadowRoot into which element DOM should be rendered. Defaults\n   * to an open shadowRoot.\n   * @category rendering\n   */\n  readonly renderRoot!: HTMLElement | DocumentFragment;\n\n  /**\n   * Returns the property name for the given attribute `name`.\n   * @nocollapse\n   */\n  private static __attributeNameForProperty(\n    name: PropertyKey,\n    options: PropertyDeclaration\n  ) {\n    const attribute = options.attribute;\n    return attribute === false\n      ? undefined\n      : typeof attribute === 'string'\n        ? attribute\n        : typeof name === 'string'\n          ? name.toLowerCase()\n          : undefined;\n  }\n\n  // Initialize to an unresolved Promise so we can make sure the element has\n  // connected before first update.\n  private __updatePromise!: Promise<boolean>;\n\n  /**\n   * True if there is a pending update as a result of calling `requestUpdate()`.\n   * Should only be read.\n   * @category updates\n   */\n  isUpdatePending = false;\n\n  /**\n   * Is set to `true` after the first update. The element code cannot assume\n   * that `renderRoot` exists before the element `hasUpdated`.\n   * @category updates\n   */\n  hasUpdated = false;\n\n  /**\n   * Map with keys for any properties that have changed since the last\n   * update cycle with previous values.\n   *\n   * @internal\n   */\n  _$changedProperties!: PropertyValues;\n\n  /**\n   * Records property default values when the\n   * `useDefault` option is used.\n   */\n  private __defaultValues?: Map<PropertyKey, unknown>;\n\n  /**\n   * Properties that should be reflected when updated.\n   */\n  private __reflectingProperties?: Set<PropertyKey>;\n\n  /**\n   * Name of currently reflecting property\n   */\n  private __reflectingProperty: PropertyKey | null = null;\n\n  /**\n   * Set of controllers.\n   */\n  private __controllers?: Set<ReactiveController>;\n\n  constructor() {\n    super();\n    this.__initialize();\n  }\n\n  /**\n   * Internal only override point for customizing work done when elements\n   * are constructed.\n   */\n  private __initialize() {\n    this.__updatePromise = new Promise<boolean>(\n      (res) => (this.enableUpdating = res)\n    );\n    this._$changedProperties = new Map();\n    // This enqueues a microtask that must run before the first update, so it\n    // must be called before requestUpdate()\n    this.__saveInstanceProperties();\n    // ensures first update will be caught by an early access of\n    // `updateComplete`\n    this.requestUpdate();\n    (this.constructor as typeof ReactiveElement)._initializers?.forEach((i) =>\n      i(this)\n    );\n  }\n\n  /**\n   * Registers a `ReactiveController` to participate in the element's reactive\n   * update cycle. The element automatically calls into any registered\n   * controllers during its lifecycle callbacks.\n   *\n   * If the element is connected when `addController()` is called, the\n   * controller's `hostConnected()` callback will be immediately called.\n   * @category controllers\n   */\n  addController(controller: ReactiveController) {\n    (this.__controllers ??= new Set()).add(controller);\n    // If a controller is added after the element has been connected,\n    // call hostConnected. Note, re-using existence of `renderRoot` here\n    // (which is set in connectedCallback) to avoid the need to track a\n    // first connected state.\n    if (this.renderRoot !== undefined && this.isConnected) {\n      controller.hostConnected?.();\n    }\n  }\n\n  /**\n   * Removes a `ReactiveController` from the element.\n   * @category controllers\n   */\n  removeController(controller: ReactiveController) {\n    this.__controllers?.delete(controller);\n  }\n\n  /**\n   * Fixes any properties set on the instance before upgrade time.\n   * Otherwise these would shadow the accessor and break these properties.\n   * The properties are stored in a Map which is played back after the\n   * constructor runs.\n   */\n  private __saveInstanceProperties() {\n    const instanceProperties = new Map<PropertyKey, unknown>();\n    const elementProperties = (this.constructor as typeof ReactiveElement)\n      .elementProperties;\n    for (const p of elementProperties.keys() as IterableIterator<keyof this>) {\n      if (this.hasOwnProperty(p)) {\n        instanceProperties.set(p, this[p]);\n        delete this[p];\n      }\n    }\n    if (instanceProperties.size > 0) {\n      this.__instanceProperties = instanceProperties;\n    }\n  }\n\n  /**\n   * Returns the node into which the element should render and by default\n   * creates and returns an open shadowRoot. Implement to customize where the\n   * element's DOM is rendered. For example, to render into the element's\n   * childNodes, return `this`.\n   *\n   * @return Returns a node into which to render.\n   * @category rendering\n   */\n  protected createRenderRoot(): HTMLElement | DocumentFragment {\n    const renderRoot =\n      this.shadowRoot ??\n      this.attachShadow(\n        (this.constructor as typeof ReactiveElement).shadowRootOptions\n      );\n    adoptStyles(\n      renderRoot,\n      (this.constructor as typeof ReactiveElement).elementStyles\n    );\n    return renderRoot;\n  }\n\n  /**\n   * On first connection, creates the element's renderRoot, sets up\n   * element styling, and enables updating.\n   * @category lifecycle\n   */\n  connectedCallback() {\n    // Create renderRoot before controllers `hostConnected`\n    (this as Mutable<typeof this, 'renderRoot'>).renderRoot ??=\n      this.createRenderRoot();\n    this.enableUpdating(true);\n    this.__controllers?.forEach((c) => c.hostConnected?.());\n  }\n\n  /**\n   * Note, this method should be considered final and not overridden. It is\n   * overridden on the element instance with a function that triggers the first\n   * update.\n   * @category updates\n   */\n  protected enableUpdating(_requestedUpdate: boolean) {}\n\n  /**\n   * Allows for `super.disconnectedCallback()` in extensions while\n   * reserving the possibility of making non-breaking feature additions\n   * when disconnecting at some point in the future.\n   * @category lifecycle\n   */\n  disconnectedCallback() {\n    this.__controllers?.forEach((c) => c.hostDisconnected?.());\n  }\n\n  /**\n   * Synchronizes property values when attributes change.\n   *\n   * Specifically, when an attribute is set, the corresponding property is set.\n   * You should rarely need to implement this callback. If this method is\n   * overridden, `super.attributeChangedCallback(name, _old, value)` must be\n   * called.\n   *\n   * See [responding to attribute changes](https://developer.mozilla.org/en-US/docs/Web/API/Web_components/Using_custom_elements#responding_to_attribute_changes)\n   * on MDN for more information about the `attributeChangedCallback`.\n   * @category attributes\n   */\n  attributeChangedCallback(\n    name: string,\n    _old: string | null,\n    value: string | null\n  ) {\n    this._$attributeToProperty(name, value);\n  }\n\n  private __propertyToAttribute(name: PropertyKey, value: unknown) {\n    const elemProperties: PropertyDeclarationMap = (\n      this.constructor as typeof ReactiveElement\n    ).elementProperties;\n    const options = elemProperties.get(name)!;\n    const attr = (\n      this.constructor as typeof ReactiveElement\n    ).__attributeNameForProperty(name, options);\n    if (attr !== undefined && options.reflect === true) {\n      const converter =\n        (options.converter as ComplexAttributeConverter)?.toAttribute !==\n        undefined\n          ? (options.converter as ComplexAttributeConverter)\n          : defaultConverter;\n      const attrValue = converter.toAttribute!(value, options.type);\n      if (\n        DEV_MODE &&\n        (this.constructor as typeof ReactiveElement).enabledWarnings!.includes(\n          'migration'\n        ) &&\n        attrValue === undefined\n      ) {\n        issueWarning(\n          'undefined-attribute-value',\n          `The attribute value for the ${name as string} property is ` +\n            `undefined on element ${this.localName}. The attribute will be ` +\n            `removed, but in the previous version of \\`ReactiveElement\\`, ` +\n            `the attribute would not have changed.`\n        );\n      }\n      // Track if the property is being reflected to avoid\n      // setting the property again via `attributeChangedCallback`. Note:\n      // 1. this takes advantage of the fact that the callback is synchronous.\n      // 2. will behave incorrectly if multiple attributes are in the reaction\n      // stack at time of calling. However, since we process attributes\n      // in `update` this should not be possible (or an extreme corner case\n      // that we'd like to discover).\n      // mark state reflecting\n      this.__reflectingProperty = name;\n      if (attrValue == null) {\n        this.removeAttribute(attr);\n      } else {\n        this.setAttribute(attr, attrValue as string);\n      }\n      // mark state not reflecting\n      this.__reflectingProperty = null;\n    }\n  }\n\n  /** @internal */\n  _$attributeToProperty(name: string, value: string | null) {\n    const ctor = this.constructor as typeof ReactiveElement;\n    // Note, hint this as an `AttributeMap` so closure clearly understands\n    // the type; it has issues with tracking types through statics\n    const propName = (ctor.__attributeToPropertyMap as AttributeMap).get(name);\n    // Use tracking info to avoid reflecting a property value to an attribute\n    // if it was just set because the attribute changed.\n    if (propName !== undefined && this.__reflectingProperty !== propName) {\n      const options = ctor.getPropertyOptions(propName);\n      const converter =\n        typeof options.converter === 'function'\n          ? {fromAttribute: options.converter}\n          : options.converter?.fromAttribute !== undefined\n            ? options.converter\n            : defaultConverter;\n      // mark state reflecting\n      this.__reflectingProperty = propName;\n      const convertedValue = converter.fromAttribute!(value, options.type);\n      this[propName as keyof this] =\n        convertedValue ??\n        this.__defaultValues?.get(propName) ??\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (convertedValue as any);\n      // mark state not reflecting\n      this.__reflectingProperty = null;\n    }\n  }\n\n  /**\n   * Requests an update which is processed asynchronously. This should be called\n   * when an element should update based on some state not triggered by setting\n   * a reactive property. In this case, pass no arguments. It should also be\n   * called when manually implementing a property setter. In this case, pass the\n   * property `name` and `oldValue` to ensure that any configured property\n   * options are honored.\n   *\n   * @param name name of requesting property\n   * @param oldValue old value of requesting property\n   * @param options property options to use instead of the previously\n   *     configured options\n   * @category updates\n   */\n  requestUpdate(\n    name?: PropertyKey,\n    oldValue?: unknown,\n    options?: PropertyDeclaration\n  ): void {\n    // If we have a property key, perform property update steps.\n    if (name !== undefined) {\n      if (DEV_MODE && (name as unknown) instanceof Event) {\n        issueWarning(\n          ``,\n          `The requestUpdate() method was called with an Event as the property name. This is probably a mistake caused by binding this.requestUpdate as an event listener. Instead bind a function that will call it with no arguments: () => this.requestUpdate()`\n        );\n      }\n      const ctor = this.constructor as typeof ReactiveElement;\n      const newValue = this[name as keyof this];\n      options ??= ctor.getPropertyOptions(name);\n      const changed =\n        (options.hasChanged ?? notEqual)(newValue, oldValue) ||\n        // When there is no change, check a corner case that can occur when\n        // 1. there's a initial value which was not reflected\n        // 2. the property is subsequently set to this value.\n        // For example, `prop: {useDefault: true, reflect: true}`\n        // and el.prop = 'foo'. This should be considered a change if the\n        // attribute is not set because we will now reflect the property to the attribute.\n        (options.useDefault &&\n          options.reflect &&\n          newValue === this.__defaultValues?.get(name) &&\n          !this.hasAttribute(ctor.__attributeNameForProperty(name, options)!));\n      if (changed) {\n        this._$changeProperty(name, oldValue, options);\n      } else {\n        // Abort the request if the property should not be considered changed.\n        return;\n      }\n    }\n    if (this.isUpdatePending === false) {\n      this.__updatePromise = this.__enqueueUpdate();\n    }\n  }\n\n  /**\n   * @internal\n   */\n  _$changeProperty(\n    name: PropertyKey,\n    oldValue: unknown,\n    {useDefault, reflect, wrapped}: PropertyDeclaration,\n    initializeValue?: unknown\n  ) {\n    // Record default value when useDefault is used. This allows us to\n    // restore this value when the attribute is removed.\n    if (useDefault && !(this.__defaultValues ??= new Map()).has(name)) {\n      this.__defaultValues.set(\n        name,\n        initializeValue ?? oldValue ?? this[name as keyof this]\n      );\n      // if this is not wrapping an accessor, it must be an initial setting\n      // and in this case we do not want to record the change or reflect.\n      if (wrapped !== true || initializeValue !== undefined) {\n        return;\n      }\n    }\n    // TODO (justinfagnani): Create a benchmark of Map.has() + Map.set(\n    // vs just Map.set()\n    if (!this._$changedProperties.has(name)) {\n      // On the initial change, the old value should be `undefined`, except\n      // with `useDefault`\n      if (!this.hasUpdated && !useDefault) {\n        oldValue = undefined;\n      }\n      this._$changedProperties.set(name, oldValue);\n    }\n    // Add to reflecting properties set.\n    // Note, it's important that every change has a chance to add the\n    // property to `__reflectingProperties`. This ensures setting\n    // attribute + property reflects correctly.\n    if (reflect === true && this.__reflectingProperty !== name) {\n      (this.__reflectingProperties ??= new Set<PropertyKey>()).add(name);\n    }\n  }\n\n  /**\n   * Sets up the element to asynchronously update.\n   */\n  private async __enqueueUpdate() {\n    this.isUpdatePending = true;\n    try {\n      // Ensure any previous update has resolved before updating.\n      // This `await` also ensures that property changes are batched.\n      await this.__updatePromise;\n    } catch (e) {\n      // Refire any previous errors async so they do not disrupt the update\n      // cycle. Errors are refired so developers have a chance to observe\n      // them, and this can be done by implementing\n      // `window.onunhandledrejection`.\n      Promise.reject(e);\n    }\n    const result = this.scheduleUpdate();\n    // If `scheduleUpdate` returns a Promise, we await it. This is done to\n    // enable coordinating updates with a scheduler. Note, the result is\n    // checked to avoid delaying an additional microtask unless we need to.\n    if (result != null) {\n      await result;\n    }\n    return !this.isUpdatePending;\n  }\n\n  /**\n   * Schedules an element update. You can override this method to change the\n   * timing of updates by returning a Promise. The update will await the\n   * returned Promise, and you should resolve the Promise to allow the update\n   * to proceed. If this method is overridden, `super.scheduleUpdate()`\n   * must be called.\n   *\n   * For instance, to schedule updates to occur just before the next frame:\n   *\n   * ```ts\n   * override protected async scheduleUpdate(): Promise<unknown> {\n   *   await new Promise((resolve) => requestAnimationFrame(() => resolve()));\n   *   super.scheduleUpdate();\n   * }\n   * ```\n   * @category updates\n   */\n  protected scheduleUpdate(): void | Promise<unknown> {\n    const result = this.performUpdate();\n    if (\n      DEV_MODE &&\n      (this.constructor as typeof ReactiveElement).enabledWarnings!.includes(\n        'async-perform-update'\n      ) &&\n      typeof (result as unknown as Promise<unknown> | undefined)?.then ===\n        'function'\n    ) {\n      issueWarning(\n        'async-perform-update',\n        `Element ${this.localName} returned a Promise from performUpdate(). ` +\n          `This behavior is deprecated and will be removed in a future ` +\n          `version of ReactiveElement.`\n      );\n    }\n    return result;\n  }\n\n  /**\n   * Performs an element update. Note, if an exception is thrown during the\n   * update, `firstUpdated` and `updated` will not be called.\n   *\n   * Call `performUpdate()` to immediately process a pending update. This should\n   * generally not be needed, but it can be done in rare cases when you need to\n   * update synchronously.\n   *\n   * @category updates\n   */\n  protected performUpdate(): void {\n    // Abort any update if one is not pending when this is called.\n    // This can happen if `performUpdate` is called early to \"flush\"\n    // the update.\n    if (!this.isUpdatePending) {\n      return;\n    }\n    debugLogEvent?.({kind: 'update'});\n    if (!this.hasUpdated) {\n      // Create renderRoot before first update. This occurs in `connectedCallback`\n      // but is done here to support out of tree calls to `enableUpdating`/`performUpdate`.\n      (this as Mutable<typeof this, 'renderRoot'>).renderRoot ??=\n        this.createRenderRoot();\n      if (DEV_MODE) {\n        // Produce warning if any reactive properties on the prototype are\n        // shadowed by class fields. Instance fields set before upgrade are\n        // deleted by this point, so any own property is caused by class field\n        // initialization in the constructor.\n        const ctor = this.constructor as typeof ReactiveElement;\n        const shadowedProperties = [...ctor.elementProperties.keys()].filter(\n          (p) => this.hasOwnProperty(p) && p in getPrototypeOf(this)\n        );\n        if (shadowedProperties.length) {\n          throw new Error(\n            `The following properties on element ${this.localName} will not ` +\n              `trigger updates as expected because they are set using class ` +\n              `fields: ${shadowedProperties.join(', ')}. ` +\n              `Native class fields and some compiled output will overwrite ` +\n              `accessors used for detecting changes. See ` +\n              `https://lit.dev/msg/class-field-shadowing ` +\n              `for more information.`\n          );\n        }\n      }\n      // Mixin instance properties once, if they exist.\n      if (this.__instanceProperties) {\n        // TODO (justinfagnani): should we use the stored value? Could a new value\n        // have been set since we stored the own property value?\n        for (const [p, value] of this.__instanceProperties) {\n          this[p as keyof this] = value as this[keyof this];\n        }\n        this.__instanceProperties = undefined;\n      }\n      // Trigger initial value reflection and populate the initial\n      // `changedProperties` map, but only for the case of properties created\n      // via `createProperty` on accessors, which will not have already\n      // populated the `changedProperties` map since they are not set.\n      // We can't know if these accessors had initializers, so we just set\n      // them anyway - a difference from experimental decorators on fields and\n      // standard decorators on auto-accessors.\n      // For context see:\n      // https://github.com/lit/lit/pull/4183#issuecomment-1711959635\n      const elementProperties = (this.constructor as typeof ReactiveElement)\n        .elementProperties;\n      if (elementProperties.size > 0) {\n        for (const [p, options] of elementProperties) {\n          const {wrapped} = options;\n          const value = this[p as keyof this];\n          if (\n            wrapped === true &&\n            !this._$changedProperties.has(p) &&\n            value !== undefined\n          ) {\n            this._$changeProperty(p, undefined, options, value);\n          }\n        }\n      }\n    }\n    let shouldUpdate = false;\n    const changedProperties = this._$changedProperties;\n    try {\n      shouldUpdate = this.shouldUpdate(changedProperties);\n      if (shouldUpdate) {\n        this.willUpdate(changedProperties);\n        this.__controllers?.forEach((c) => c.hostUpdate?.());\n        this.update(changedProperties);\n      } else {\n        this.__markUpdated();\n      }\n    } catch (e) {\n      // Prevent `firstUpdated` and `updated` from running when there's an\n      // update exception.\n      shouldUpdate = false;\n      // Ensure element can accept additional updates after an exception.\n      this.__markUpdated();\n      throw e;\n    }\n    // The update is no longer considered pending and further updates are now allowed.\n    if (shouldUpdate) {\n      this._$didUpdate(changedProperties);\n    }\n  }\n\n  /**\n   * Invoked before `update()` to compute values needed during the update.\n   *\n   * Implement `willUpdate` to compute property values that depend on other\n   * properties and are used in the rest of the update process.\n   *\n   * ```ts\n   * willUpdate(changedProperties) {\n   *   // only need to check changed properties for an expensive computation.\n   *   if (changedProperties.has('firstName') || changedProperties.has('lastName')) {\n   *     this.sha = computeSHA(`${this.firstName} ${this.lastName}`);\n   *   }\n   * }\n   *\n   * render() {\n   *   return html`SHA: ${this.sha}`;\n   * }\n   * ```\n   *\n   * @category updates\n   */\n  protected willUpdate(_changedProperties: PropertyValues): void {}\n\n  // Note, this is an override point for polyfill-support.\n  // @internal\n  _$didUpdate(changedProperties: PropertyValues) {\n    this.__controllers?.forEach((c) => c.hostUpdated?.());\n    if (!this.hasUpdated) {\n      this.hasUpdated = true;\n      this.firstUpdated(changedProperties);\n    }\n    this.updated(changedProperties);\n    if (\n      DEV_MODE &&\n      this.isUpdatePending &&\n      (this.constructor as typeof ReactiveElement).enabledWarnings!.includes(\n        'change-in-update'\n      )\n    ) {\n      issueWarning(\n        'change-in-update',\n        `Element ${this.localName} scheduled an update ` +\n          `(generally because a property was set) ` +\n          `after an update completed, causing a new update to be scheduled. ` +\n          `This is inefficient and should be avoided unless the next update ` +\n          `can only be scheduled as a side effect of the previous update.`\n      );\n    }\n  }\n\n  private __markUpdated() {\n    this._$changedProperties = new Map();\n    this.isUpdatePending = false;\n  }\n\n  /**\n   * Returns a Promise that resolves when the element has completed updating.\n   * The Promise value is a boolean that is `true` if the element completed the\n   * update without triggering another update. The Promise result is `false` if\n   * a property was set inside `updated()`. If the Promise is rejected, an\n   * exception was thrown during the update.\n   *\n   * To await additional asynchronous work, override the `getUpdateComplete`\n   * method. For example, it is sometimes useful to await a rendered element\n   * before fulfilling this Promise. To do this, first await\n   * `super.getUpdateComplete()`, then any subsequent state.\n   *\n   * @return A promise of a boolean that resolves to true if the update completed\n   *     without triggering another update.\n   * @category updates\n   */\n  get updateComplete(): Promise<boolean> {\n    return this.getUpdateComplete();\n  }\n\n  /**\n   * Override point for the `updateComplete` promise.\n   *\n   * It is not safe to override the `updateComplete` getter directly due to a\n   * limitation in TypeScript which means it is not possible to call a\n   * superclass getter (e.g. `super.updateComplete.then(...)`) when the target\n   * language is ES5 (https://github.com/microsoft/TypeScript/issues/338).\n   * This method should be overridden instead. For example:\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   override async getUpdateComplete() {\n   *     const result = await super.getUpdateComplete();\n   *     await this._myChild.updateComplete;\n   *     return result;\n   *   }\n   * }\n   * ```\n   *\n   * @return A promise of a boolean that resolves to true if the update completed\n   *     without triggering another update.\n   * @category updates\n   */\n  protected getUpdateComplete(): Promise<boolean> {\n    return this.__updatePromise;\n  }\n\n  /**\n   * Controls whether or not `update()` should be called when the element requests\n   * an update. By default, this method always returns `true`, but this can be\n   * customized to control when to update.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected shouldUpdate(_changedProperties: PropertyValues): boolean {\n    return true;\n  }\n\n  /**\n   * Updates the element. This method reflects property values to attributes.\n   * It can be overridden to render and keep updated element DOM.\n   * Setting properties inside this method will *not* trigger\n   * another update.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected update(_changedProperties: PropertyValues) {\n    // The forEach() expression will only run when __reflectingProperties is\n    // defined, and it returns undefined, setting __reflectingProperties to\n    // undefined\n    this.__reflectingProperties &&= this.__reflectingProperties.forEach((p) =>\n      this.__propertyToAttribute(p, this[p as keyof this])\n    ) as undefined;\n    this.__markUpdated();\n  }\n\n  /**\n   * Invoked whenever the element is updated. Implement to perform\n   * post-updating tasks via DOM APIs, for example, focusing an element.\n   *\n   * Setting properties inside this method will trigger the element to update\n   * again after this update cycle completes.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected updated(_changedProperties: PropertyValues) {}\n\n  /**\n   * Invoked when the element is first updated. Implement to perform one time\n   * work on the element after update.\n   *\n   * ```ts\n   * firstUpdated() {\n   *   this.renderRoot.getElementById('my-text-area').focus();\n   * }\n   * ```\n   *\n   * Setting properties inside this method will trigger the element to update\n   * again after this update cycle completes.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected firstUpdated(_changedProperties: PropertyValues) {}\n}\n// Assigned here to work around a jscompiler bug with static fields\n// when compiling to ES5.\n// https://github.com/google/closure-compiler/issues/3177\n(ReactiveElement as unknown as Record<string, unknown>)[\n  JSCompiler_renameProperty('elementProperties', ReactiveElement)\n] = new Map();\n(ReactiveElement as unknown as Record<string, unknown>)[\n  JSCompiler_renameProperty('finalized', ReactiveElement)\n] = new Map();\n\n// Apply polyfills if available\npolyfillSupport?.({ReactiveElement});\n\n// Dev mode warnings...\nif (DEV_MODE) {\n  // Default warning set.\n  ReactiveElement.enabledWarnings = [\n    'change-in-update',\n    'async-perform-update',\n  ];\n  const ensureOwnWarnings = function (ctor: typeof ReactiveElement) {\n    if (\n      !ctor.hasOwnProperty(JSCompiler_renameProperty('enabledWarnings', ctor))\n    ) {\n      ctor.enabledWarnings = ctor.enabledWarnings!.slice();\n    }\n  };\n  ReactiveElement.enableWarning = function (\n    this: typeof ReactiveElement,\n    warning: WarningKind\n  ) {\n    ensureOwnWarnings(this);\n    if (!this.enabledWarnings!.includes(warning)) {\n      this.enabledWarnings!.push(warning);\n    }\n  };\n  ReactiveElement.disableWarning = function (\n    this: typeof ReactiveElement,\n    warning: WarningKind\n  ) {\n    ensureOwnWarnings(this);\n    const i = this.enabledWarnings!.indexOf(warning);\n    if (i >= 0) {\n      this.enabledWarnings!.splice(i, 1);\n    }\n  };\n}\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for ReactiveElement usage.\n(global.reactiveElementVersions ??= []).push('2.1.1');\nif (DEV_MODE && global.reactiveElementVersions.length > 1) {\n  queueMicrotask(() => {\n    issueWarning!(\n      'multiple-versions',\n      `Multiple versions of Lit loaded. Loading multiple versions ` +\n        `is not recommended.`\n    );\n  });\n}\n"]}