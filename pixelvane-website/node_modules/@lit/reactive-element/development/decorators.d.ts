/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
export * from './decorators/custom-element.js';
export * from './decorators/property.js';
export * from './decorators/state.js';
export * from './decorators/event-options.js';
export * from './decorators/query.js';
export * from './decorators/query-all.js';
export * from './decorators/query-async.js';
export * from './decorators/query-assigned-elements.js';
export * from './decorators/query-assigned-nodes.js';
//# sourceMappingURL=decorators.d.ts.map