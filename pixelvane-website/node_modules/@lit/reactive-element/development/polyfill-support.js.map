{"version": 3, "file": "polyfill-support.js", "sourceRoot": "", "sources": ["../src/polyfill-support.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAqBH,IAAM,MAAM,GAAG,UAAU,CAAC;AAsB1B,sEAAsE;AACtE,WAAW;AACX,kCAAkC;AAClC,IAAI,QAAQ,GAAG,IAAI,CAAC;AAEpB,IAAM,eAAe,GAAG,UAAC,EAIxB;QAHC,eAAe,qBAAA;IAIf,yEAAyE;IACzE,gEAAgE;IAChE,uDAAuD;IACvD,IACE,MAAM,CAAC,QAAQ,KAAK,SAAS;QAC7B,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAC5D,CAAC;QACD,OAAO;IACT,CAAC;IAED,eAAe;IACf,+DAA+D;IAC/D,4CAA4C;IAC5C,KAAK;IAEL,IAAM,YAAY,GAAG,eAAe,CAAC,SAAS,CAAC;IAE/C,kEAAkE;IAClE,oCAAoC;IACpC,IACE,MAAM,CAAC,QAAQ;QACf,MAAM,CAAC,QAAQ,CAAC,KAAK;QACrB,MAAM,CAAC,QAAQ,CAAC,OAAO,KAAK,IAAI,EAChC,CAAC;QACD,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,IAAM,gBAAgB,GAAG,YAAY,CAAC,gBAAgB,CAAC;IACvD,YAAY,CAAC,gBAAgB,GAAG;;QAC9B,0EAA0E;QAC1E,wBAAwB;QACxB,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;QAC5B,wDAAwD;QACxD,wBAAwB;QACxB,IAAI,MAAM,CAAC,QAAS,CAAC,YAAY,EAAE,CAAC;YAClC,OAAO,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC5C,IAAI,CAAC,WAAmD,CAAC,MAAM,CAAC;oBAC/D,IAAI,CAAC;gBACP,qEAAqE;gBACrE,IAAM,GAAG,GACP,IAAI,CAAC,WACN,CAAC,aAAa,CAAC,GAAG,CAAC,UAAC,CAAC;oBACpB,OAAA,CAAC,YAAY,aAAa;wBACxB,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAC3B,UAAC,CAAS,EAAE,CAAU,IAAK,OAAA,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAhB,CAAgB,EAC3C,EAAE,CACH;wBACH,CAAC,CAAC,CAAC,CAAC,OAAO;gBALb,CAKa,CACd,CAAC;gBACF,MAAA,MAAA,MAAM,CAAC,QAAQ,0CAAE,WAAW,0CAAE,qBAAqB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBAC/D,IAAI,IAAI,CAAC,WAAW,CAAC,sBAAsB,KAAK,SAAS,EAAE,CAAC;oBAC1D,MAAM,CAAC,QAAS,CAAC,qBAAqB,CACpC,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,EAClC,IAAI,CACL,CAAC;gBACJ,CAAC;YACH,CAAC;YACD,OAAO,CACL,MAAA,IAAI,CAAC,UAAU,mCACf,IAAI,CAAC,YAAY,CACd,IAAI,CAAC,WAAmD;iBACtD,iBAAiB,CACrB,CACF,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;IAEF;;OAEG;IACH,IAAM,iBAAiB,GAAG,YAAY,CAAC,iBAAiB,CAAC;IACzD,YAAY,CAAC,iBAAiB,GAAG;QAC/B,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,8DAA8D;QAC9D,oDAAoD;QACpD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,CAAC,QAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC;IACH,CAAC,CAAC;IAEF;;;OAGG;IACH,IAAM,SAAS,GAAG,YAAY,CAAC,WAAW,CAAC;IAC3C,YAAY,CAAC,WAAW,GAAG,UAEzB,iBAA0B;QAE1B,oEAAoE;QACpE,+DAA+D;QAC/D,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,CAAC,QAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC;QACD,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;IAC1C,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,IAAI,QAAQ,EAAE,CAAC;IACb,MAAA,UAAU,CAAC,qCAAqC,oCAAhD,UAAU,CAAC,qCAAqC,GAAK,eAAe,EAAC;AACvE,CAAC;KAAM,CAAC;IACN,MAAA,UAAU,CAAC,8BAA8B,oCAAzC,UAAU,CAAC,8BAA8B,GAAK,eAAe,EAAC;AAChE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * ReactiveElement patch to support browsers without native web components.\n *\n * This module should be used in addition to loading the web components\n * polyfills via @webcomponents/webcomponentjs. When using those polyfills\n * support for polyfilled Shadow DOM is automatic via the ShadyDOM polyfill, but\n * support for Shadow DOM like css scoping is opt-in. This module uses ShadyCSS\n * to scope styles defined via the `static styles` property.\n *\n * @packageDocumentation\n */\n\nexport {};\n\ninterface RenderOptions {\n  readonly renderBefore?: ChildNode | null;\n  scope?: string;\n}\n\nconst SCOPED = '__scoped';\n\ntype CSSResults = Array<{cssText: string} | CSSStyleSheet>;\n\ninterface PatchableReactiveElementConstructor {\n  [SCOPED]: boolean;\n  elementStyles: CSSResults;\n  shadowRootOptions: ShadowRootInit;\n  _$handlesPrepareStyles?: boolean;\n}\n\ninterface PatchableReactiveElement extends HTMLElement {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableReactiveElement;\n  constructor: PatchableReactiveElementConstructor;\n  connectedCallback(): void;\n  hasUpdated: boolean;\n  _$didUpdate(changedProperties: unknown): void;\n  createRenderRoot(): Element | ShadowRoot;\n  renderOptions: RenderOptions;\n}\n\n// Note, explicitly use `var` here so that this can be re-defined when\n// bundled.\n// eslint-disable-next-line no-var\nvar DEV_MODE = true;\n\nconst polyfillSupport = ({\n  ReactiveElement,\n}: {\n  ReactiveElement: PatchableReactiveElement;\n}) => {\n  // polyfill-support is only needed if ShadyCSS or the ApplyShim is in use\n  // We test at the point of patching, which makes it safe to load\n  // webcomponentsjs and polyfill-support in either order\n  if (\n    window.ShadyCSS === undefined ||\n    (window.ShadyCSS.nativeShadow && !window.ShadyCSS.ApplyShim)\n  ) {\n    return;\n  }\n\n  // console.log(\n  //   '%c Making ReactiveElement compatible with ShadyDOM/CSS.',\n  //   'color: lightgreen; font-style: italic'\n  // );\n\n  const elementProto = ReactiveElement.prototype;\n\n  // In noPatch mode, patch the ReactiveElement prototype so that no\n  // ReactiveElements must be wrapped.\n  if (\n    window.ShadyDOM &&\n    window.ShadyDOM.inUse &&\n    window.ShadyDOM.noPatch === true\n  ) {\n    window.ShadyDOM.patchElementProto(elementProto);\n  }\n\n  /**\n   * Patch to apply adoptedStyleSheets via ShadyCSS\n   */\n  const createRenderRoot = elementProto.createRenderRoot;\n  elementProto.createRenderRoot = function (this: PatchableReactiveElement) {\n    // Pass the scope to render options so that it gets to lit-html for proper\n    // scoping via ShadyCSS.\n    const name = this.localName;\n    // If using native Shadow DOM must adoptStyles normally,\n    // otherwise do nothing.\n    if (window.ShadyCSS!.nativeShadow) {\n      return createRenderRoot.call(this);\n    } else {\n      if (!this.constructor.hasOwnProperty(SCOPED)) {\n        (this.constructor as PatchableReactiveElementConstructor)[SCOPED] =\n          true;\n        // Use ShadyCSS's `prepareAdoptedCssText` to shim adoptedStyleSheets.\n        const css = (\n          this.constructor as PatchableReactiveElementConstructor\n        ).elementStyles.map((v) =>\n          v instanceof CSSStyleSheet\n            ? Array.from(v.cssRules).reduce(\n                (a: string, r: CSSRule) => (a += r.cssText),\n                ''\n              )\n            : v.cssText\n        );\n        window.ShadyCSS?.ScopingShim?.prepareAdoptedCssText(css, name);\n        if (this.constructor._$handlesPrepareStyles === undefined) {\n          window.ShadyCSS!.prepareTemplateStyles(\n            document.createElement('template'),\n            name\n          );\n        }\n      }\n      return (\n        this.shadowRoot ??\n        this.attachShadow(\n          (this.constructor as PatchableReactiveElementConstructor)\n            .shadowRootOptions\n        )\n      );\n    }\n  };\n\n  /**\n   * Patch connectedCallback to apply ShadyCSS custom properties shimming.\n   */\n  const connectedCallback = elementProto.connectedCallback;\n  elementProto.connectedCallback = function (this: PatchableReactiveElement) {\n    connectedCallback.call(this);\n    // Note, must do first update separately so that we're ensured\n    // that rendering has completed before calling this.\n    if (this.hasUpdated) {\n      window.ShadyCSS!.styleElement(this);\n    }\n  };\n\n  /**\n   * Patch update to apply ShadyCSS custom properties shimming for first\n   * update.\n   */\n  const didUpdate = elementProto._$didUpdate;\n  elementProto._$didUpdate = function (\n    this: PatchableReactiveElement,\n    changedProperties: unknown\n  ) {\n    // Note, must do first update here so rendering has completed before\n    // calling this and styles are correct by updated/firstUpdated.\n    if (!this.hasUpdated) {\n      window.ShadyCSS!.styleElement(this);\n    }\n    didUpdate.call(this, changedProperties);\n  };\n};\n\nif (DEV_MODE) {\n  globalThis.reactiveElementPolyfillSupportDevMode ??= polyfillSupport;\n} else {\n  globalThis.reactiveElementPolyfillSupport ??= polyfillSupport;\n}\n"]}