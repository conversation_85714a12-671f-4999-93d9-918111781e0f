{"version": 3, "file": "query-assigned-nodes.js", "sources": ["../../src/decorators/query-assigned-nodes.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport type {ReactiveElement} from '../reactive-element.js';\nimport {desc, type Interface} from './base.js';\n\n/**\n * Options for the {@linkcode queryAssignedNodes} decorator. Extends the options\n * that can be passed into [HTMLSlotElement.assignedNodes](https://developer.mozilla.org/en-US/docs/Web/API/HTMLSlotElement/assignedNodes).\n */\nexport interface QueryAssignedNodesOptions extends AssignedNodesOptions {\n  /**\n   * Name of the slot to query. Leave empty for the default slot.\n   */\n  slot?: string;\n}\n\nexport type QueryAssignedNodesDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: <PERSON><PERSON><PERSON>,\n    descriptor?: PropertyDescriptor\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  <C extends Interface<ReactiveElement>, V extends Array<Node>>(\n    value: ClassAccessorDecoratorTarget<C, V>,\n    context: ClassAccessorDecoratorContext<C, V>\n  ): ClassAccessorDecoratorResult<C, V>;\n};\n\n/**\n * A property decorator that converts a class property into a getter that\n * returns the `assignedNodes` of the given `slot`.\n *\n * Can be passed an optional {@linkcode QueryAssignedNodesOptions} object.\n *\n * Example usage:\n * ```ts\n * class MyElement {\n *   @queryAssignedNodes({slot: 'list', flatten: true})\n *   listItems!: Array<Node>;\n *\n *   render() {\n *     return html`\n *       <slot name=\"list\"></slot>\n *     `;\n *   }\n * }\n * ```\n *\n * Note the type of this property should be annotated as `Array<Node>`. Use the\n * queryAssignedElements decorator to list only elements, and optionally filter\n * the element list using a CSS selector.\n *\n * @category Decorator\n */\nexport function queryAssignedNodes(\n  options?: QueryAssignedNodesOptions\n): QueryAssignedNodesDecorator {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return (<V extends Array<Node>>(\n    obj: object,\n    name: PropertyKey | ClassAccessorDecoratorContext<unknown, unknown>\n  ) => {\n    const {slot} = options ?? {};\n    const slotSelector = `slot${slot ? `[name=${slot}]` : ':not([name])'}`;\n    return desc(obj, name, {\n      get(this: ReactiveElement): V {\n        const slotEl =\n          this.renderRoot?.querySelector<HTMLSlotElement>(slotSelector);\n        return (slotEl?.assignedNodes(options) ?? []) as unknown as V;\n      },\n    });\n  }) as QueryAssignedNodesDecorator;\n}\n"], "names": ["queryAssignedNodes", "options", "obj", "name", "slot", "slotSelector", "desc", "get", "slotEl", "this", "renderRoot", "querySelector", "assignedNodes"], "mappings": ";;;;;GAqEM,SAAUA,EACdC,GAGA,MAAQ,CACNC,EACAC,KAEA,MAAMC,KAACA,GAAQH,GAAW,GACpBI,EAAe,QAAOD,EAAO,SAASA,KAAU,gBACtD,OAAOE,EAAKJ,EAAKC,EAAM,CACrB,GAAAI,GACE,MAAMC,EACJC,KAAKC,YAAYC,cAA+BN,GAClD,OAAQG,GAAQI,cAAcX,IAAY,EAC3C,GAEJ,CACH"}