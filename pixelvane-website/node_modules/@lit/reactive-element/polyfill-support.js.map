{"version": 3, "file": "polyfill-support.js", "sources": ["src/polyfill-support.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * ReactiveElement patch to support browsers without native web components.\n *\n * This module should be used in addition to loading the web components\n * polyfills via @webcomponents/webcomponentjs. When using those polyfills\n * support for polyfilled Shadow DOM is automatic via the ShadyDOM polyfill, but\n * support for Shadow DOM like css scoping is opt-in. This module uses ShadyCSS\n * to scope styles defined via the `static styles` property.\n *\n * @packageDocumentation\n */\n\nexport {};\n\ninterface RenderOptions {\n  readonly renderBefore?: ChildNode | null;\n  scope?: string;\n}\n\nconst SCOPED = '__scoped';\n\ntype CSSResults = Array<{cssText: string} | CSSStyleSheet>;\n\ninterface PatchableReactiveElementConstructor {\n  [SCOPED]: boolean;\n  elementStyles: CSSResults;\n  shadowRootOptions: ShadowRootInit;\n  _$handlesPrepareStyles?: boolean;\n}\n\ninterface PatchableReactiveElement extends HTMLElement {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableReactiveElement;\n  constructor: PatchableReactiveElementConstructor;\n  connectedCallback(): void;\n  hasUpdated: boolean;\n  _$didUpdate(changedProperties: unknown): void;\n  createRenderRoot(): Element | ShadowRoot;\n  renderOptions: RenderOptions;\n}\n\n// Note, explicitly use `var` here so that this can be re-defined when\n// bundled.\n// eslint-disable-next-line no-var\nvar DEV_MODE = true;\n\nconst polyfillSupport = ({\n  ReactiveElement,\n}: {\n  ReactiveElement: PatchableReactiveElement;\n}) => {\n  // polyfill-support is only needed if ShadyCSS or the ApplyShim is in use\n  // We test at the point of patching, which makes it safe to load\n  // webcomponentsjs and polyfill-support in either order\n  if (\n    window.ShadyCSS === undefined ||\n    (window.ShadyCSS.nativeShadow && !window.ShadyCSS.ApplyShim)\n  ) {\n    return;\n  }\n\n  // console.log(\n  //   '%c Making ReactiveElement compatible with ShadyDOM/CSS.',\n  //   'color: lightgreen; font-style: italic'\n  // );\n\n  const elementProto = ReactiveElement.prototype;\n\n  // In noPatch mode, patch the ReactiveElement prototype so that no\n  // ReactiveElements must be wrapped.\n  if (\n    window.ShadyDOM &&\n    window.ShadyDOM.inUse &&\n    window.ShadyDOM.noPatch === true\n  ) {\n    window.ShadyDOM.patchElementProto(elementProto);\n  }\n\n  /**\n   * Patch to apply adoptedStyleSheets via ShadyCSS\n   */\n  const createRenderRoot = elementProto.createRenderRoot;\n  elementProto.createRenderRoot = function (this: PatchableReactiveElement) {\n    // Pass the scope to render options so that it gets to lit-html for proper\n    // scoping via ShadyCSS.\n    const name = this.localName;\n    // If using native Shadow DOM must adoptStyles normally,\n    // otherwise do nothing.\n    if (window.ShadyCSS!.nativeShadow) {\n      return createRenderRoot.call(this);\n    } else {\n      if (!this.constructor.hasOwnProperty(SCOPED)) {\n        (this.constructor as PatchableReactiveElementConstructor)[SCOPED] =\n          true;\n        // Use ShadyCSS's `prepareAdoptedCssText` to shim adoptedStyleSheets.\n        const css = (\n          this.constructor as PatchableReactiveElementConstructor\n        ).elementStyles.map((v) =>\n          v instanceof CSSStyleSheet\n            ? Array.from(v.cssRules).reduce(\n                (a: string, r: CSSRule) => (a += r.cssText),\n                ''\n              )\n            : v.cssText\n        );\n        window.ShadyCSS?.ScopingShim?.prepareAdoptedCssText(css, name);\n        if (this.constructor._$handlesPrepareStyles === undefined) {\n          window.ShadyCSS!.prepareTemplateStyles(\n            document.createElement('template'),\n            name\n          );\n        }\n      }\n      return (\n        this.shadowRoot ??\n        this.attachShadow(\n          (this.constructor as PatchableReactiveElementConstructor)\n            .shadowRootOptions\n        )\n      );\n    }\n  };\n\n  /**\n   * Patch connectedCallback to apply ShadyCSS custom properties shimming.\n   */\n  const connectedCallback = elementProto.connectedCallback;\n  elementProto.connectedCallback = function (this: PatchableReactiveElement) {\n    connectedCallback.call(this);\n    // Note, must do first update separately so that we're ensured\n    // that rendering has completed before calling this.\n    if (this.hasUpdated) {\n      window.ShadyCSS!.styleElement(this);\n    }\n  };\n\n  /**\n   * Patch update to apply ShadyCSS custom properties shimming for first\n   * update.\n   */\n  const didUpdate = elementProto._$didUpdate;\n  elementProto._$didUpdate = function (\n    this: PatchableReactiveElement,\n    changedProperties: unknown\n  ) {\n    // Note, must do first update here so rendering has completed before\n    // calling this and styles are correct by updated/firstUpdated.\n    if (!this.hasUpdated) {\n      window.ShadyCSS!.styleElement(this);\n    }\n    didUpdate.call(this, changedProperties);\n  };\n};\n\nif (DEV_MODE) {\n  globalThis.reactiveElementPolyfillSupportDevMode ??= polyfillSupport;\n} else {\n  globalThis.reactiveElementPolyfillSupport ??= polyfillSupport;\n}\n"], "names": ["SCOPED", "_b", "globalThis", "reactiveElementPolyfillSupport", "_a", "ReactiveElement", "undefined", "window", "ShadyCSS", "nativeShadow", "App<PERSON><PERSON><PERSON>", "elementProto", "prototype", "ShadyDOM", "inUse", "noPatch", "patchElementProto", "createRenderRoot", "name", "this", "localName", "call", "constructor", "hasOwnProperty", "css", "elementStyles", "map", "v", "CSSStyleSheet", "Array", "from", "cssRules", "reduce", "a", "r", "cssText", "<PERSON><PERSON><PERSON><PERSON>", "prepareAdoptedCssText", "_$handlesPrepareStyles", "prepareTemplateStyles", "document", "createElement", "_c", "shadowRoot", "attachShadow", "shadowRootOptions", "connectedCallback", "hasUpdated", "styleElement", "didUpdate", "_$didUpdate", "changedProperties"], "mappings": ";;;;;aAyBMA,EAAS,WA0I4B,QAAzCC,EAAAC,WAAWC,sCAA8B,IAAAF,IAAzCC,WAAWC,+BA/GW,SAACC,GACvB,IAAAC,EAAeD,EAAAC,gBAOf,QACsBC,IAApBC,OAAOC,YACND,OAAOC,SAASC,cAAiBF,OAAOC,SAASE,WAFpD,CAYA,IAAMC,EAAeN,EAAgBO,UAKnCL,OAAOM,UACPN,OAAOM,SAASC,QACY,IAA5BP,OAAOM,SAASE,SAEhBR,OAAOM,SAASG,kBAAkBL,GAMpC,IAAMM,EAAmBN,EAAaM,iBACtCN,EAAaM,iBAAmB,qBAGxBC,EAAOC,KAAKC,UAGlB,GAAIb,OAAOC,SAAUC,aACnB,OAAOQ,EAAiBI,KAAKF,MAE7B,IAAKA,KAAKG,YAAYC,eAAevB,GAAS,CAC3CmB,KAAKG,YAAoDtB,IACxD,EAEF,IAAMwB,EACJL,KAAKG,YACLG,cAAcC,KAAI,SAACC,GACnB,OAAAA,aAAaC,cACTC,MAAMC,KAAKH,EAAEI,UAAUC,QACrB,SAACC,EAAWC,GAAe,OAACD,EAAKC,EAAEC,OAAQ,GAC3C,IAEFR,EAAEQ,OALN,IAO4B,QAA9BlC,EAAiB,QAAjBG,EAAAG,OAAOC,gBAAU,IAAAJ,OAAA,EAAAA,EAAAgC,mBAAa,IAAAnC,GAAAA,EAAAoC,sBAAsBb,EAAKN,QACTZ,IAA5Ca,KAAKG,YAAYgB,MACnB/B,OAAOC,SAAU+B,sBACfC,SAASC,cAAc,YACvBvB,EAGL,CACD,OACiB,QAAfwB,EAAAvB,KAAKwB,kBAAU,IAAAD,EAAAA,EACfvB,KAAKyB,aACFzB,KAAKG,YACHuB,kBAIX,EAKA,IAAMC,EAAoBnC,EAAamC,kBACvCnC,EAAamC,kBAAoB,WAC/BA,EAAkBzB,KAAKF,MAGnBA,KAAK4B,YACPxC,OAAOC,SAAUwC,aAAa7B,KAElC,EAMA,IAAM8B,EAAYtC,EAAauC,KAC/BvC,EAAauC,KAAc,SAEzBC,GAIKhC,KAAK4B,YACRxC,OAAOC,SAAUwC,aAAa7B,MAEhC8B,EAAU5B,KAAKF,KAAMgC,EACvB,CA5FC,CA6FH"}