import {
  CSSResult,
  ReactiveElement,
  adoptStyles,
  css,
  defaultConverter,
  getCompatibleStyle,
  notEqual,
  supportsAdoptingStyleSheets,
  unsafeCSS
} from "./chunk-SBXOPBOA.js";
import {
  _$LH,
  html,
  mathml,
  noChange,
  nothing,
  render,
  svg
} from "./chunk-32RPEPIA.js";

// node_modules/lit-element/development/lit-element.js
var JSCompiler_renameProperty = (prop, _obj) => prop;
var DEV_MODE = true;
var global = globalThis;
var issueWarning;
if (DEV_MODE) {
  global.litIssuedWarnings ?? (global.litIssuedWarnings = /* @__PURE__ */ new Set());
  issueWarning = (code, warning) => {
    warning += ` See https://lit.dev/msg/${code} for more information.`;
    if (!global.litIssuedWarnings.has(warning) && !global.litIssuedWarnings.has(code)) {
      console.warn(warning);
      global.litIssuedWarnings.add(warning);
    }
  };
}
var LitElement = class extends ReactiveElement {
  constructor() {
    super(...arguments);
    this.renderOptions = { host: this };
    this.__childPart = void 0;
  }
  /**
   * @category rendering
   */
  createRenderRoot() {
    var _a2;
    const renderRoot = super.createRenderRoot();
    (_a2 = this.renderOptions).renderBefore ?? (_a2.renderBefore = renderRoot.firstChild);
    return renderRoot;
  }
  /**
   * Updates the element. This method reflects property values to attributes
   * and calls `render` to render DOM via lit-html. Setting properties inside
   * this method will *not* trigger another update.
   * @param changedProperties Map of changed properties with old values
   * @category updates
   */
  update(changedProperties) {
    const value = this.render();
    if (!this.hasUpdated) {
      this.renderOptions.isConnected = this.isConnected;
    }
    super.update(changedProperties);
    this.__childPart = render(value, this.renderRoot, this.renderOptions);
  }
  /**
   * Invoked when the component is added to the document's DOM.
   *
   * In `connectedCallback()` you should setup tasks that should only occur when
   * the element is connected to the document. The most common of these is
   * adding event listeners to nodes external to the element, like a keydown
   * event handler added to the window.
   *
   * ```ts
   * connectedCallback() {
   *   super.connectedCallback();
   *   addEventListener('keydown', this._handleKeydown);
   * }
   * ```
   *
   * Typically, anything done in `connectedCallback()` should be undone when the
   * element is disconnected, in `disconnectedCallback()`.
   *
   * @category lifecycle
   */
  connectedCallback() {
    var _a2;
    super.connectedCallback();
    (_a2 = this.__childPart) == null ? void 0 : _a2.setConnected(true);
  }
  /**
   * Invoked when the component is removed from the document's DOM.
   *
   * This callback is the main signal to the element that it may no longer be
   * used. `disconnectedCallback()` should ensure that nothing is holding a
   * reference to the element (such as event listeners added to nodes external
   * to the element), so that it is free to be garbage collected.
   *
   * ```ts
   * disconnectedCallback() {
   *   super.disconnectedCallback();
   *   window.removeEventListener('keydown', this._handleKeydown);
   * }
   * ```
   *
   * An element may be re-connected after being disconnected.
   *
   * @category lifecycle
   */
  disconnectedCallback() {
    var _a2;
    super.disconnectedCallback();
    (_a2 = this.__childPart) == null ? void 0 : _a2.setConnected(false);
  }
  /**
   * Invoked on each update to perform rendering tasks. This method may return
   * any value renderable by lit-html's `ChildPart` - typically a
   * `TemplateResult`. Setting properties inside this method will *not* trigger
   * the element to update.
   * @category rendering
   */
  render() {
    return noChange;
  }
};
LitElement["_$litElement$"] = true;
LitElement[JSCompiler_renameProperty("finalized", LitElement)] = true;
var _a;
(_a = global.litElementHydrateSupport) == null ? void 0 : _a.call(global, { LitElement });
var polyfillSupport = DEV_MODE ? global.litElementPolyfillSupportDevMode : global.litElementPolyfillSupport;
polyfillSupport == null ? void 0 : polyfillSupport({ LitElement });
var _$LE = {
  _$attributeToProperty: (el, name, value) => {
    el._$attributeToProperty(name, value);
  },
  // eslint-disable-next-line
  _$changedProperties: (el) => el._$changedProperties
};
(global.litElementVersions ?? (global.litElementVersions = [])).push("4.2.1");
if (DEV_MODE && global.litElementVersions.length > 1) {
  queueMicrotask(() => {
    issueWarning("multiple-versions", `Multiple versions of Lit loaded. Loading multiple versions is not recommended.`);
  });
}

// node_modules/lit-html/development/is-server.js
var NODE_MODE = false;
var isServer = NODE_MODE;
export {
  CSSResult,
  LitElement,
  ReactiveElement,
  _$LE,
  _$LH,
  adoptStyles,
  css,
  defaultConverter,
  getCompatibleStyle,
  html,
  isServer,
  mathml,
  noChange,
  notEqual,
  nothing,
  render,
  supportsAdoptingStyleSheets,
  svg,
  unsafeCSS
};
/*! Bundled license information:

lit-element/development/lit-element.js:
  (**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)

lit-html/development/is-server.js:
  (**
   * @license
   * Copyright 2022 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)
*/
//# sourceMappingURL=lit.js.map
