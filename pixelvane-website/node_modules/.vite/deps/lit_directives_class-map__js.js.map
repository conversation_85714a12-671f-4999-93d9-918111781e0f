{"version": 3, "sources": ["../../lit-html/src/directives/class-map.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\n\n/**\n * A key-value set of class names to truthy values.\n */\nexport interface ClassInfo {\n  readonly [name: string]: string | boolean | number;\n}\n\nclass ClassMapDirective extends Directive {\n  /**\n   * Stores the ClassInfo object applied to a given AttributePart.\n   * Used to unset existing values when a new ClassInfo object is applied.\n   */\n  private _previousClasses?: Set<string>;\n  private _staticClasses?: Set<string>;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      partInfo.type !== PartType.ATTRIBUTE ||\n      partInfo.name !== 'class' ||\n      (partInfo.strings?.length as number) > 2\n    ) {\n      throw new Error(\n        '`classMap()` can only be used in the `class` attribute ' +\n          'and must be the only part in the attribute.'\n      );\n    }\n  }\n\n  render(classInfo: ClassInfo) {\n    // Add spaces to ensure separation from static classes\n    return (\n      ' ' +\n      Object.keys(classInfo)\n        .filter((key) => classInfo[key])\n        .join(' ') +\n      ' '\n    );\n  }\n\n  override update(part: AttributePart, [classInfo]: DirectiveParameters<this>) {\n    // Remember dynamic classes on the first render\n    if (this._previousClasses === undefined) {\n      this._previousClasses = new Set();\n      if (part.strings !== undefined) {\n        this._staticClasses = new Set(\n          part.strings\n            .join(' ')\n            .split(/\\s/)\n            .filter((s) => s !== '')\n        );\n      }\n      for (const name in classInfo) {\n        if (classInfo[name] && !this._staticClasses?.has(name)) {\n          this._previousClasses.add(name);\n        }\n      }\n      return this.render(classInfo);\n    }\n\n    const classList = part.element.classList;\n\n    // Remove old classes that no longer apply\n    for (const name of this._previousClasses) {\n      if (!(name in classInfo)) {\n        classList.remove(name);\n        this._previousClasses!.delete(name);\n      }\n    }\n\n    // Add or remove classes based on their classMap value\n    for (const name in classInfo) {\n      // We explicitly want a loose truthy check of `value` because it seems\n      // more convenient that '' and 0 are skipped.\n      const value = !!classInfo[name];\n      if (\n        value !== this._previousClasses.has(name) &&\n        !this._staticClasses?.has(name)\n      ) {\n        if (value) {\n          classList.add(name);\n          this._previousClasses.add(name);\n        } else {\n          classList.remove(name);\n          this._previousClasses.delete(name);\n        }\n      }\n    }\n    return noChange;\n  }\n}\n\n/**\n * A directive that applies dynamic CSS classes.\n *\n * This must be used in the `class` attribute and must be the only part used in\n * the attribute. It takes each property in the `classInfo` argument and adds\n * the property name to the element's `classList` if the property value is\n * truthy; if the property value is falsy, the property name is removed from\n * the element's `class`.\n *\n * For example `{foo: bar}` applies the class `foo` if the value of `bar` is\n * truthy.\n *\n * @param classInfo\n */\nexport const classMap = directive(ClassMapDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {ClassMapDirective};\n"], "mappings": ";;;;;;;;;;AAsBA,IAAM,oBAAN,cAAgC,UAAS;EAQvC,YAAY,UAAkB;AA9BhC;AA+BI,UAAM,QAAQ;AACd,QACE,SAAS,SAAS,SAAS,aAC3B,SAAS,SAAS,aACjB,cAAS,YAAT,mBAAkB,UAAoB,GACvC;AACA,YAAM,IAAI,MACR,oGAC+C;IAEnD;EACF;EAEA,OAAO,WAAoB;AAEzB,WACE,MACA,OAAO,KAAK,SAAS,EAClB,OAAO,CAAC,QAAQ,UAAU,GAAG,CAAC,EAC9B,KAAK,GAAG,IACX;EAEJ;EAES,OAAO,MAAqB,CAAC,SAAS,GAA4B;AAvD7E;AAyDI,QAAI,KAAK,qBAAqB,QAAW;AACvC,WAAK,mBAAmB,oBAAI,IAAG;AAC/B,UAAI,KAAK,YAAY,QAAW;AAC9B,aAAK,iBAAiB,IAAI,IACxB,KAAK,QACF,KAAK,GAAG,EACR,MAAM,IAAI,EACV,OAAO,CAAC,MAAM,MAAM,EAAE,CAAC;MAE9B;AACA,iBAAW,QAAQ,WAAW;AAC5B,YAAI,UAAU,IAAI,KAAK,GAAC,UAAK,mBAAL,mBAAqB,IAAI,QAAO;AACtD,eAAK,iBAAiB,IAAI,IAAI;QAChC;MACF;AACA,aAAO,KAAK,OAAO,SAAS;IAC9B;AAEA,UAAM,YAAY,KAAK,QAAQ;AAG/B,eAAW,QAAQ,KAAK,kBAAkB;AACxC,UAAI,EAAE,QAAQ,YAAY;AACxB,kBAAU,OAAO,IAAI;AACrB,aAAK,iBAAkB,OAAO,IAAI;MACpC;IACF;AAGA,eAAW,QAAQ,WAAW;AAG5B,YAAM,QAAQ,CAAC,CAAC,UAAU,IAAI;AAC9B,UACE,UAAU,KAAK,iBAAiB,IAAI,IAAI,KACxC,GAAC,UAAK,mBAAL,mBAAqB,IAAI,QAC1B;AACA,YAAI,OAAO;AACT,oBAAU,IAAI,IAAI;AAClB,eAAK,iBAAiB,IAAI,IAAI;QAChC,OAAO;AACL,oBAAU,OAAO,IAAI;AACrB,eAAK,iBAAiB,OAAO,IAAI;QACnC;MACF;IACF;AACA,WAAO;EACT;;AAiBK,IAAM,WAAW,UAAU,iBAAiB;", "names": []}