{"version": 3, "sources": ["../../lit-html/src/directives/style-map.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\n\n/**\n * A key-value set of CSS properties and values.\n *\n * The key should be either a valid CSS property name string, like\n * `'background-color'`, or a valid JavaScript camel case property name\n * for CSSStyleDeclaration like `backgroundColor`.\n */\nexport interface StyleInfo {\n  [name: string]: string | number | undefined | null;\n}\n\nconst important = 'important';\n// The leading space is important\nconst importantFlag = ' !' + important;\n// How many characters to remove from a value, as a negative number\nconst flagTrim = 0 - importantFlag.length;\n\nclass StyleMapDirective extends Directive {\n  private _previousStyleProperties?: Set<string>;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      partInfo.type !== PartType.ATTRIBUTE ||\n      partInfo.name !== 'style' ||\n      (partInfo.strings?.length as number) > 2\n    ) {\n      throw new Error(\n        'The `styleMap` directive must be used in the `style` attribute ' +\n          'and must be the only part in the attribute.'\n      );\n    }\n  }\n\n  render(styleInfo: Readonly<StyleInfo>) {\n    return Object.keys(styleInfo).reduce((style, prop) => {\n      const value = styleInfo[prop];\n      if (value == null) {\n        return style;\n      }\n      // Convert property names from camel-case to dash-case, i.e.:\n      //  `backgroundColor` -> `background-color`\n      // Vendor-prefixed names need an extra `-` appended to front:\n      //  `webkitAppearance` -> `-webkit-appearance`\n      // Exception is any property name containing a dash, including\n      // custom properties; we assume these are already dash-cased i.e.:\n      //  `--my-button-color` --> `--my-button-color`\n      prop = prop.includes('-')\n        ? prop\n        : prop\n            .replace(/(?:^(webkit|moz|ms|o)|)(?=[A-Z])/g, '-$&')\n            .toLowerCase();\n      return style + `${prop}:${value};`;\n    }, '');\n  }\n\n  override update(part: AttributePart, [styleInfo]: DirectiveParameters<this>) {\n    const {style} = part.element as HTMLElement;\n\n    if (this._previousStyleProperties === undefined) {\n      this._previousStyleProperties = new Set(Object.keys(styleInfo));\n      return this.render(styleInfo);\n    }\n\n    // Remove old properties that no longer exist in styleInfo\n    for (const name of this._previousStyleProperties) {\n      // If the name isn't in styleInfo or it's null/undefined\n      if (styleInfo[name] == null) {\n        this._previousStyleProperties!.delete(name);\n        if (name.includes('-')) {\n          style.removeProperty(name);\n        } else {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          (style as any)[name] = null;\n        }\n      }\n    }\n\n    // Add or update properties\n    for (const name in styleInfo) {\n      const value = styleInfo[name];\n      if (value != null) {\n        this._previousStyleProperties.add(name);\n        const isImportant =\n          typeof value === 'string' && value.endsWith(importantFlag);\n        if (name.includes('-') || isImportant) {\n          style.setProperty(\n            name,\n            isImportant\n              ? (value as string).slice(0, flagTrim)\n              : (value as string),\n            isImportant ? important : ''\n          );\n        } else {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          (style as any)[name] = value;\n        }\n      }\n    }\n    return noChange;\n  }\n}\n\n/**\n * A directive that applies CSS properties to an element.\n *\n * `styleMap` can only be used in the `style` attribute and must be the only\n * expression in the attribute. It takes the property names in the\n * {@link StyleInfo styleInfo} object and adds the properties to the inline\n * style of the element.\n *\n * Property names with dashes (`-`) are assumed to be valid CSS\n * property names and set on the element's style object using `setProperty()`.\n * Names without dashes are assumed to be camelCased JavaScript property names\n * and set on the element's style object using property assignment, allowing the\n * style object to translate JavaScript-style names to CSS property names.\n *\n * For example `styleMap({backgroundColor: 'red', 'border-top': '5px', '--size':\n * '0'})` sets the `background-color`, `border-top` and `--size` properties.\n *\n * @param styleInfo\n * @see {@link https://lit.dev/docs/templates/directives/#stylemap styleMap code samples on Lit.dev}\n */\nexport const styleMap = directive(StyleMapDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {StyleMapDirective};\n"], "mappings": ";;;;;;;;;;AA0BA,IAAM,YAAY;AAElB,IAAM,gBAAgB,OAAO;AAE7B,IAAM,WAAW,IAAI,cAAc;AAEnC,IAAM,oBAAN,cAAgC,UAAS;EAGvC,YAAY,UAAkB;AAnChC;AAoCI,UAAM,QAAQ;AACd,QACE,SAAS,SAAS,SAAS,aAC3B,SAAS,SAAS,aACjB,cAAS,YAAT,mBAAkB,UAAoB,GACvC;AACA,YAAM,IAAI,MACR,4GAC+C;IAEnD;EACF;EAEA,OAAO,WAA8B;AACnC,WAAO,OAAO,KAAK,SAAS,EAAE,OAAO,CAAC,OAAO,SAAQ;AACnD,YAAM,QAAQ,UAAU,IAAI;AAC5B,UAAI,SAAS,MAAM;AACjB,eAAO;MACT;AAQA,aAAO,KAAK,SAAS,GAAG,IACpB,OACA,KACG,QAAQ,qCAAqC,KAAK,EAClD,YAAW;AAClB,aAAO,QAAQ,GAAG,IAAI,IAAI,KAAK;IACjC,GAAG,EAAE;EACP;EAES,OAAO,MAAqB,CAAC,SAAS,GAA4B;AACzE,UAAM,EAAC,MAAK,IAAI,KAAK;AAErB,QAAI,KAAK,6BAA6B,QAAW;AAC/C,WAAK,2BAA2B,IAAI,IAAI,OAAO,KAAK,SAAS,CAAC;AAC9D,aAAO,KAAK,OAAO,SAAS;IAC9B;AAGA,eAAW,QAAQ,KAAK,0BAA0B;AAEhD,UAAI,UAAU,IAAI,KAAK,MAAM;AAC3B,aAAK,yBAA0B,OAAO,IAAI;AAC1C,YAAI,KAAK,SAAS,GAAG,GAAG;AACtB,gBAAM,eAAe,IAAI;QAC3B,OAAO;AAEJ,gBAAc,IAAI,IAAI;QACzB;MACF;IACF;AAGA,eAAW,QAAQ,WAAW;AAC5B,YAAM,QAAQ,UAAU,IAAI;AAC5B,UAAI,SAAS,MAAM;AACjB,aAAK,yBAAyB,IAAI,IAAI;AACtC,cAAM,cACJ,OAAO,UAAU,YAAY,MAAM,SAAS,aAAa;AAC3D,YAAI,KAAK,SAAS,GAAG,KAAK,aAAa;AACrC,gBAAM,YACJ,MACA,cACK,MAAiB,MAAM,GAAG,QAAQ,IAClC,OACL,cAAc,YAAY,EAAE;QAEhC,OAAO;AAEJ,gBAAc,IAAI,IAAI;QACzB;MACF;IACF;AACA,WAAO;EACT;;AAuBK,IAAM,WAAW,UAAU,iBAAiB;", "names": []}