{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["src/index.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,EAAC,oBAAoB,EAAC,MAAM,4BAA4B,CAAC;AAChE,OAAO,EACL,eAAe,EACf,SAAS,EACT,eAAe,GAEhB,MAAM,iBAAiB,CAAC;AAEzB,OAAO,EACL,mBAAmB,EACnB,gBAAgB,EAChB,6BAA6B,GAC9B,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAC,WAAW,EAAE,KAAK,EAAE,WAAW,EAAC,MAAM,iBAAiB,CAAC;AAEhE,+DAA+D;AAC/D,mEAAmE;AACnE,8CAA8C;AAC9C,UAAU,CAAC,KAAK,KAAK,SAAS,CAAC;AAC/B,UAAU,CAAC,WAAW,KAAK,eAAe,CAAC;AAK3C,MAAM,UAAU,GAAG,IAAI,OAAO,EAG3B,CAAC;AACJ,MAAM,oBAAoB,GAAG,CAC3B,OAA6C,EAC7C,EAAE;IACF,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACpC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;QACxB,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,2DAA2D;AAC3D,EAAE;AACF,+EAA+E;AAC/E,iCAAiC;AACjC,+EAA+E;AAC/E,sEAAsE;AACtE,0CAA0C;AAC1C,sEAAsE;AACtE,iFAAiF;AACjF,mEAAmE;AACnE,oDAAoD;AACpD,MAAM,WAAW,GAAG,MAAM,OAAQ,SAAQ,eAAe;IAArC;;QAOV,qBAAgB,GAA0B,IAAI,CAAC;QAC7C,iBAAY,GAAsB,IAAI,CAAC;QACvC,gBAAW,GAA4B,IAAI,CAAC;IAsExD,CAAC;IA9EC,IAAI,UAAU;QACZ,OAAO,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;YACpE,IAAI;YACJ,KAAK;SACN,CAAC,CAAC,CAAC;IACN,CAAC;IAKD,IAAI,UAAU;QACZ,IAAI,IAAI,CAAC,gBAAgB,KAAK,QAAQ,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IACD,IAAI,SAAS;QACX,OAAQ,IAAI,CAAC,WAAiD,CAAC,WAAW,CAAC;IAC7E,CAAC;IACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC;IACvC,CAAC;IACD,YAAY,CAAC,IAAY,EAAE,KAAc;QACvC,0EAA0E;QAC1E,6DAA6D;QAC7D,oBAAoB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACtD,CAAC;IACD,eAAe,CAAC,IAAY;QAC1B,oBAAoB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IACD,eAAe,CAAC,IAAY,EAAE,KAAe;QAC3C,2EAA2E;QAC3E,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,SAAS;YACT,IAAI,KAAK,KAAK,SAAS,IAAI,CAAC,KAAK,EAAE,CAAC;gBAClC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC3B,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;aAAM,CAAC;YACN,SAAS;YACT,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,EAAE,CAAC;gBACjC,WAAW;gBACX,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBAC5B,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,WAAW;gBACX,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QACD,SAAS;QACT,OAAO,IAAI,CAAC;IACd,CAAC;IACD,YAAY,CAAC,IAAY;QACvB,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IACD,YAAY,CAAC,IAAoB;QAC/B,MAAM,UAAU,GAAG,EAAC,IAAI,EAAE,IAAI,EAAyB,CAAC;QACxD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC;QAClC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACjC,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC;QACjC,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IACD,eAAe;QACb,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CACb,wDAAwD;gBACtD,kEAAkE,CACrE,CAAC;QACJ,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,oBAAoB,CAAC,IAA8B,CAAC,CAAC;QAC3E,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;QAC7B,OAAO,SAA6B,CAAC;IACvC,CAAC;IACD,YAAY,CAAC,IAAY;QACvB,MAAM,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACnD,OAAO,KAAK,IAAI,IAAI,CAAC;IACvB,CAAC;CACF,CAAC;AACF,MAAM,uBAAuB,GAAG,WAAuC,CAAC;AACxE,OAAO,EAAC,uBAAuB,IAAI,OAAO,EAAC,CAAC;AAE5C,MAAM,eAAe,GAAG,MAAM,WAAY,SAAQ,WAAW;CAAG,CAAC;AACjE,MAAM,2BAA2B,GAC/B,eAA+C,CAAC;AAClD,OAAO,EAAC,2BAA2B,IAAI,WAAW,EAAC,CAAC;AAEpD,6EAA6E;AAC7E,6DAA6D;AAC7D,2CAA2C;AAC3C,6CAA6C;AAC7C,8EAA8E;AAC9E,uEAAuE;AACvE,UAAU,CAAC,aAAa,KAAK,MAAM,CAAC,cAAc,CAChD,IAAI,2BAA2B,EAAE,EACjC,WAAW,EACX;IACE,yDAAyD;IACzD,GAAG;QACD,OAAO,iBAAiB,CAAC;IAC3B,CAAC;CACF,CACF,CAAC;AA2BF,SAAS,oBAAoB;IAC3B,IAAI,OAA2B,CAAC;IAChC,IAAI,MAAkC,CAAC;IACvC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAI,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QAC1C,OAAO,GAAG,GAAG,CAAC;QACd,MAAM,GAAG,GAAG,CAAC;IACf,CAAC,CAAC,CAAC;IACH,OAAO,EAAC,OAAO,EAAE,OAAO,EAAE,OAAQ,EAAE,MAAM,EAAE,MAAO,EAAC,CAAC;AACvD,CAAC;AAED,MAAM,qBAAqB;IAA3B;QACU,kBAAa,GAAG,IAAI,GAAG,EAAqC,CAAC;QAC7D,yBAAoB,GAAG,IAAI,GAAG,EAGnC,CAAC;QACI,0BAAqB,GAAG,IAAI,GAAG,EAGpC,CAAC;IA2EN,CAAC;IAzEC,MAAM,CAAC,IAAY,EAAE,IAAkC;QACrD,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;gBAC3C,OAAO,CAAC,IAAI,CACV,wCAAwC,IAAI,aAAa;oBACvD,yDAAyD;oBACzD,uDAAuD;oBACvD,gEAAgE;oBAChE,gDAAgD,CACnD,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,yDAAyD;oBACvD,aAAa,IAAI,4CAA4C,CAChE,CAAC;YACJ,CAAC;QACH,CAAC;QACD,IAAI,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CACb,yDAAyD;gBACvD,mEAAmE;gBACnE,YAAY,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CACpD,CAAC;QACJ,CAAC;QACD,mDAAmD;QAClD,IAA0C,CAAC,WAAW,GAAG,IAAI,CAAC;QAC/D,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE;YAC3B,IAAI;YACJ,0EAA0E;YAC1E,oEAAoE;YACpE,gBAAgB;YAChB,EAAE;YACF,iEAAiE;YACjE,yEAAyE;YACzE,0EAA0E;YAC1E,wEAAwE;YACxE,4BAA4B;YAC5B,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,IAAI,EAAE;SAClD,CAAC,CAAC;QACH,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,GAAG,CAAC,IAAY;QACd,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAChD,OAAO,UAAU,EAAE,IAAI,CAAC;IAC1B,CAAC;IAED,OAAO,CAAC,IAAkC;QACxC,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IACrD,CAAC;IAED,OAAO,CAAC,QAAqB;QAC3B,6DAA6D;QAC7D,MAAM,IAAI,KAAK,CACb,4DAA4D;YAC1D,mCAAmC,CACtC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,IAAY;QAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,UAAU,CAAC,IAAI,CAAC;QACzB,CAAC;QACD,IAAI,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,aAAa,GAAG,oBAAoB,EAA4B,CAAC;YACjE,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QACtD,CAAC;QACD,OAAO,aAAa,CAAC,OAAO,CAAC;IAC/B,CAAC;CACF;AAED,MAAM,qCAAqC,GACzC,qBAAiE,CAAC;AACpE,OAAO,EAAC,qCAAqC,IAAI,qBAAqB,EAAC,CAAC;AAExE,MAAM,CAAC,MAAM,cAAc,GAAG,IAAI,qCAAqC,EAAE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nimport {ElementInternalsShim} from './lib/element-internals.js';\nimport {\n  EventTargetShim,\n  EventShim,\n  CustomEventShim,\n  EventTargetShimMeta,\n} from './lib/events.js';\n\nexport {\n  ariaMixinAttributes,\n  ElementInternals,\n  HYDRATE_INTERNALS_ATTR_PREFIX,\n} from './lib/element-internals.js';\nexport {CustomEvent, Event, EventTarget} from './lib/events.js';\n\n// In an empty Node.js vm, we need to patch the global context.\n// TODO: Remove these globalThis assignments when we remove support\n// for vm modules (--experimental-vm-modules).\nglobalThis.Event ??= EventShim;\nglobalThis.CustomEvent ??= CustomEventShim;\n\n// Internal type to be used for the event polyfill functionality.\nexport type HTMLElementWithEventMeta = HTMLElement & EventTargetShimMeta;\n\nconst attributes = new WeakMap<\n  InstanceType<typeof HTMLElementShim>,\n  Map<string, string>\n>();\nconst attributesForElement = (\n  element: InstanceType<typeof HTMLElementShim>\n) => {\n  let attrs = attributes.get(element);\n  if (attrs === undefined) {\n    attributes.set(element, (attrs = new Map()));\n  }\n  return attrs;\n};\n\n// The typings around the exports below are a little funky:\n//\n// 1. We want the `name` of the shim classes to match the real ones at runtime,\n//    hence e.g. `class Element`.\n// 2. We can't shadow the global types with a simple class declaration, because\n//    then we can't reference the global types for casting, hence e.g.\n//    `const ElementShim = class Element`.\n// 3. We want to export the classes typed as the real ones, hence e.g.\n//    `const ElementShimWithRealType = ElementShim as object as typeof Element;`.\n// 4. We want the exported names to match the real ones, hence e.g.\n//    `export {ElementShimWithRealType as Element}`.\nconst ElementShim = class Element extends EventTargetShim {\n  get attributes() {\n    return Array.from(attributesForElement(this)).map(([name, value]) => ({\n      name,\n      value,\n    }));\n  }\n  private __shadowRootMode: null | ShadowRootMode = null;\n  protected __shadowRoot: null | ShadowRoot = null;\n  protected __internals: null | ElementInternals = null;\n\n  get shadowRoot() {\n    if (this.__shadowRootMode === 'closed') {\n      return null;\n    }\n    return this.__shadowRoot;\n  }\n  get localName() {\n    return (this.constructor as NamedCustomHTMLElementConstructor).__localName;\n  }\n  get tagName() {\n    return this.localName?.toUpperCase();\n  }\n  setAttribute(name: string, value: unknown): void {\n    // Emulate browser behavior that silently casts all values to string. E.g.\n    // `42` becomes `\"42\"` and `{}` becomes `\"[object Object]\"\"`.\n    attributesForElement(this).set(name, String(value));\n  }\n  removeAttribute(name: string) {\n    attributesForElement(this).delete(name);\n  }\n  toggleAttribute(name: string, force?: boolean): boolean {\n    // Steps reference https://dom.spec.whatwg.org/#dom-element-toggleattribute\n    if (this.hasAttribute(name)) {\n      // Step 5\n      if (force === undefined || !force) {\n        this.removeAttribute(name);\n        return false;\n      }\n    } else {\n      // Step 4\n      if (force === undefined || force) {\n        // Step 4.1\n        this.setAttribute(name, '');\n        return true;\n      } else {\n        // Step 4.2\n        return false;\n      }\n    }\n    // Step 6\n    return true;\n  }\n  hasAttribute(name: string) {\n    return attributesForElement(this).has(name);\n  }\n  attachShadow(init: ShadowRootInit): ShadowRoot {\n    const shadowRoot = {host: this} as object as ShadowRoot;\n    this.__shadowRootMode = init.mode;\n    if (init && init.mode === 'open') {\n      this.__shadowRoot = shadowRoot;\n    }\n    return shadowRoot;\n  }\n  attachInternals(): ElementInternals {\n    if (this.__internals !== null) {\n      throw new Error(\n        `Failed to execute 'attachInternals' on 'HTMLElement': ` +\n          `ElementInternals for the specified element was already attached.`\n      );\n    }\n    const internals = new ElementInternalsShim(this as unknown as HTMLElement);\n    this.__internals = internals;\n    return internals as ElementInternals;\n  }\n  getAttribute(name: string) {\n    const value = attributesForElement(this).get(name);\n    return value ?? null;\n  }\n};\nconst ElementShimWithRealType = ElementShim as object as typeof Element;\nexport {ElementShimWithRealType as Element};\n\nconst HTMLElementShim = class HTMLElement extends ElementShim {};\nconst HTMLElementShimWithRealType =\n  HTMLElementShim as object as typeof HTMLElement;\nexport {HTMLElementShimWithRealType as HTMLElement};\n\n// For convenience, we provide a global instance of a HTMLElement as an event\n// target. This facilitates registering global event handlers\n// (e.g. for @lit/context ContextProvider).\n// We use this in in the SSR render function.\n// Note, this is a bespoke element and not simply `document` or `window` since\n// user code relies on these being undefined in the server environment.\nglobalThis.litServerRoot ??= Object.defineProperty(\n  new HTMLElementShimWithRealType(),\n  'localName',\n  {\n    // Patch localName (and tagName) to return a unique name.\n    get() {\n      return 'lit-server-root';\n    },\n  }\n);\n\ninterface CustomHTMLElementConstructor {\n  new (): HTMLElement;\n  observedAttributes?: string[];\n}\n\ninterface NamedCustomHTMLElementConstructor\n  extends CustomHTMLElementConstructor {\n  __localName: string;\n}\n\ntype CustomElementRegistration = {\n  ctor: {new (): HTMLElement};\n  observedAttributes: string[];\n};\n\ntype RealCustomElementRegistry = (typeof globalThis)['customElements'];\ntype RealCustomElementRegistryClass =\n  (typeof globalThis)['CustomElementRegistry'];\n\n// Ponyfill for PromiseWithResolvers, remove once we can assume its presence.\ntype PromiseWithResolvers<T> = {\n  promise: Promise<T>;\n  resolve: (value: T) => void;\n  reject: (reason?: unknown) => void;\n};\nfunction promiseWithResolvers<T>(): PromiseWithResolvers<T> {\n  let resolve: (value: T) => void;\n  let reject: (reason?: unknown) => void;\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return {promise, resolve: resolve!, reject: reject!};\n}\n\nclass CustomElementRegistry implements RealCustomElementRegistry {\n  private __definitions = new Map<string, CustomElementRegistration>();\n  private __reverseDefinitions = new Map<\n    CustomHTMLElementConstructor,\n    string\n  >();\n  private __pendingWhenDefineds = new Map<\n    string,\n    PromiseWithResolvers<CustomElementConstructor>\n  >();\n\n  define(name: string, ctor: CustomHTMLElementConstructor) {\n    if (this.__definitions.has(name)) {\n      if (process.env.NODE_ENV === 'development') {\n        console.warn(\n          `'CustomElementRegistry' already has \"${name}\" defined. ` +\n            `This may have been caused by live reload or hot module ` +\n            `replacement in which case it can be safely ignored.\\n` +\n            `Make sure to test your application with a production build as ` +\n            `repeat registrations will throw in production.`\n        );\n      } else {\n        throw new Error(\n          `Failed to execute 'define' on 'CustomElementRegistry': ` +\n            `the name \"${name}\" has already been used with this registry`\n        );\n      }\n    }\n    if (this.__reverseDefinitions.has(ctor)) {\n      throw new Error(\n        `Failed to execute 'define' on 'CustomElementRegistry': ` +\n          `the constructor has already been used with this registry for the ` +\n          `tag name ${this.__reverseDefinitions.get(ctor)}`\n      );\n    }\n    // Provide tagName and localName for the component.\n    (ctor as NamedCustomHTMLElementConstructor).__localName = name;\n    this.__definitions.set(name, {\n      ctor,\n      // Note it's important we read `observedAttributes` in case it is a getter\n      // with side-effects, as is the case in Lit, where it triggers class\n      // finalization.\n      //\n      // TODO(aomarks) To be spec compliant, we should also capture the\n      // registration-time lifecycle methods like `connectedCallback`. For them\n      // to be actually accessible to e.g. the Lit SSR element renderer, though,\n      // we'd need to introduce a new API for accessing them (since `get` only\n      // returns the constructor).\n      observedAttributes: ctor.observedAttributes ?? [],\n    });\n    this.__reverseDefinitions.set(ctor, name);\n    this.__pendingWhenDefineds.get(name)?.resolve(ctor);\n    this.__pendingWhenDefineds.delete(name);\n  }\n\n  get(name: string) {\n    const definition = this.__definitions.get(name);\n    return definition?.ctor;\n  }\n\n  getName(ctor: CustomHTMLElementConstructor) {\n    return this.__reverseDefinitions.get(ctor) ?? null;\n  }\n\n  upgrade(_element: HTMLElement) {\n    // In SSR this doesn't make a lot of sense, so we do nothing.\n    throw new Error(\n      `customElements.upgrade is not currently supported in SSR. ` +\n        `Please file a bug if you need it.`\n    );\n  }\n\n  async whenDefined(name: string): Promise<CustomElementConstructor> {\n    const definition = this.__definitions.get(name);\n    if (definition) {\n      return definition.ctor;\n    }\n    let withResolvers = this.__pendingWhenDefineds.get(name);\n    if (!withResolvers) {\n      withResolvers = promiseWithResolvers<CustomElementConstructor>();\n      this.__pendingWhenDefineds.set(name, withResolvers);\n    }\n    return withResolvers.promise;\n  }\n}\n\nconst CustomElementRegistryShimWithRealType =\n  CustomElementRegistry as object as RealCustomElementRegistryClass;\nexport {CustomElementRegistryShimWithRealType as CustomElementRegistry};\n\nexport const customElements = new CustomElementRegistryShimWithRealType();\n"]}