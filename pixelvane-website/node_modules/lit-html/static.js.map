{"version": 3, "file": "static.js", "sources": ["src/static.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n// Any new exports need to be added to the export statement in\n// `packages/lit/src/index.all.ts`.\n\nimport {\n  html as coreHtml,\n  svg as coreSvg,\n  mathml as coreMathml,\n  TemplateResult,\n} from './lit-html.js';\n\nexport interface StaticValue {\n  /** The value to interpolate as-is into the template. */\n  _$litStatic$: string;\n\n  /**\n   * A value that can't be decoded from ordinary JSON, make it harder for\n   * an attacker-controlled data that goes through JSON.parse to produce a valid\n   * StaticValue.\n   */\n  r: typeof brand;\n}\n\n/**\n * Prevents JSON injection attacks.\n *\n * The goals of this brand:\n *   1) fast to check\n *   2) code is small on the wire\n *   3) multiple versions of Lit in a single page will all produce mutually\n *      interoperable StaticValues\n *   4) normal JSON.parse (without an unusual reviver) can not produce a\n *      StaticValue\n *\n * Symbols satisfy (1), (2), and (4). We use Symbol.for to satisfy (3), but\n * we don't care about the key, so we break ties via (2) and use the empty\n * string.\n */\nconst brand = Symbol.for('');\n\n/** Safely extracts the string part of a StaticValue. */\nconst unwrapStaticValue = (value: unknown): string | undefined => {\n  if ((value as Partial<StaticValue>)?.r !== brand) {\n    return undefined;\n  }\n  return (value as Partial<StaticValue>)?.['_$litStatic$'];\n};\n\n/**\n * Wraps a string so that it behaves like part of the static template\n * strings instead of a dynamic value.\n *\n * Users must take care to ensure that adding the static string to the template\n * results in well-formed HTML, or else templates may break unexpectedly.\n *\n * Note that this function is unsafe to use on untrusted content, as it will be\n * directly parsed into HTML. Do not pass user input to this function\n * without sanitizing it.\n *\n * Static values can be changed, but they will cause a complete re-render\n * since they effectively create a new template.\n */\nexport const unsafeStatic = (value: string): StaticValue => ({\n  ['_$litStatic$']: value,\n  r: brand,\n});\n\nconst textFromStatic = (value: StaticValue) => {\n  if (value['_$litStatic$'] !== undefined) {\n    return value['_$litStatic$'];\n  } else {\n    throw new Error(\n      `Value passed to 'literal' function must be a 'literal' result: ${value}. Use 'unsafeStatic' to pass non-literal values, but\n            take care to ensure page security.`\n    );\n  }\n};\n\n/**\n * Tags a string literal so that it behaves like part of the static template\n * strings instead of a dynamic value.\n *\n * The only values that may be used in template expressions are other tagged\n * `literal` results or `unsafeStatic` values (note that untrusted content\n * should never be passed to `unsafeStatic`).\n *\n * Users must take care to ensure that adding the static string to the template\n * results in well-formed HTML, or else templates may break unexpectedly.\n *\n * Static values can be changed, but they will cause a complete re-render since\n * they effectively create a new template.\n */\nexport const literal = (\n  strings: TemplateStringsArray,\n  ...values: unknown[]\n): StaticValue => ({\n  ['_$litStatic$']: values.reduce(\n    (acc, v, idx) => acc + textFromStatic(v as StaticValue) + strings[idx + 1],\n    strings[0]\n  ) as string,\n  r: brand,\n});\n\nconst stringsCache = new Map<string, TemplateStringsArray>();\n\n/**\n * Wraps a lit-html template tag (`html` or `svg`) to add static value support.\n */\nexport const withStatic =\n  (coreTag: typeof coreHtml | typeof coreSvg | typeof coreMathml) =>\n  (strings: TemplateStringsArray, ...values: unknown[]): TemplateResult => {\n    const l = values.length;\n    let staticValue: string | undefined;\n    let dynamicValue: unknown;\n    const staticStrings: Array<string> = [];\n    const dynamicValues: Array<unknown> = [];\n    let i = 0;\n    let hasStatics = false;\n    let s: string;\n\n    while (i < l) {\n      s = strings[i];\n      // Collect any unsafeStatic values, and their following template strings\n      // so that we treat a run of template strings and unsafe static values as\n      // a single template string.\n      while (\n        i < l &&\n        ((dynamicValue = values[i]),\n        (staticValue = unwrapStaticValue(dynamicValue))) !== undefined\n      ) {\n        s += staticValue + strings[++i];\n        hasStatics = true;\n      }\n      // If the last value is static, we don't need to push it.\n      if (i !== l) {\n        dynamicValues.push(dynamicValue);\n      }\n      staticStrings.push(s);\n      i++;\n    }\n    // If the last value isn't static (which would have consumed the last\n    // string), then we need to add the last string.\n    if (i === l) {\n      staticStrings.push(strings[l]);\n    }\n\n    if (hasStatics) {\n      const key = staticStrings.join('$$lit$$');\n      strings = stringsCache.get(key)!;\n      if (strings === undefined) {\n        // Beware: in general this pattern is unsafe, and doing so may bypass\n        // lit's security checks and allow an attacker to execute arbitrary\n        // code and inject arbitrary content.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (staticStrings as any).raw = staticStrings;\n        stringsCache.set(\n          key,\n          (strings = staticStrings as unknown as TemplateStringsArray)\n        );\n      }\n      values = dynamicValues;\n    }\n    return coreTag(strings, ...values);\n  };\n\n/**\n * Interprets a template literal as an HTML template that can efficiently\n * render to and update a container.\n *\n * Includes static value support from `lit-html/static.js`.\n */\nexport const html = withStatic(coreHtml);\n\n/**\n * Interprets a template literal as an SVG template that can efficiently\n * render to and update a container.\n *\n * Includes static value support from `lit-html/static.js`.\n */\nexport const svg = withStatic(coreSvg);\n\n/**\n * Interprets a template literal as MathML fragment that can efficiently render\n * to and update a container.\n *\n * Includes static value support from `lit-html/static.js`.\n */\nexport const mathml = withStatic(coreMathml);\n"], "names": ["brand", "Symbol", "for", "unwrapStaticValue", "value", "r", "unsafeStatic", "_$litStatic$", "literal", "strings", "values", "reduce", "acc", "v", "idx", "undefined", "Error", "textFromStatic", "stringsCache", "Map", "with<PERSON>tat<PERSON>", "coreTag", "l", "length", "staticValue", "dynamicValue", "staticStrings", "dynamicValues", "s", "i", "hasStatics", "push", "key", "join", "get", "raw", "set", "html", "coreHtml", "svg", "coreSvg", "mathml", "coreMathml"], "mappings": ";;;;;;AA2CA,MAAMA,EAAQC,OAAOC,IAAI,IAGnBC,EAAqBC,IACzB,GAAKA,GAAgCC,IAAML,EAG3C,OAAQI,GAA+C,YAAC,EAiB7CE,EAAgBF,IAAgC,CAC3DG,aAAkBH,EAClBC,EAAGL,IA4BQQ,EAAU,CACrBC,KACGC,KACc,CACjBH,aAAkBG,EAAOC,QACvB,CAACC,EAAKC,EAAGC,IAAQF,EA9BE,CAACR,IACtB,QAA8BW,IAA1BX,EAAoB,aACtB,OAAOA,EAAoB,aAE3B,MAAUY,MACR,kEAAkEZ,wGAGrE,EAsBwBa,CAAeJ,GAAoBJ,EAAQK,EAAM,IACxEL,EAAQ,IAEVJ,EAAGL,IAGCkB,EAAe,IAAIC,IAKZC,EACVC,GACD,CAACZ,KAAkCC,KACjC,MAAMY,EAAIZ,EAAOa,OACjB,IAAIC,EACAC,EACJ,MAAMC,EAA+B,GAC/BC,EAAgC,GACtC,IAEIC,EAFAC,EAAI,EACJC,GAAa,EAGjB,KAAOD,EAAIP,GAAG,CAKZ,IAJAM,EAAInB,EAAQoB,GAKVA,EAAIP,QAEiDP,KADnDU,EAAef,EAAOmB,GACvBL,EAAcrB,EAAkBsB,KAEjCG,GAAKJ,EAAcf,IAAUoB,GAC7BC,GAAa,EAGXD,IAAMP,GACRK,EAAcI,KAAKN,GAErBC,EAAcK,KAAKH,GACnBC,GACD,CAOD,GAJIA,IAAMP,GACRI,EAAcK,KAAKtB,EAAQa,IAGzBQ,EAAY,CACd,MAAME,EAAMN,EAAcO,KAAK,gBAEflB,KADhBN,EAAUS,EAAagB,IAAIF,MAMxBN,EAAsBS,IAAMT,EAC7BR,EAAakB,IACXJ,EACCvB,EAAUiB,IAGfhB,EAASiB,CACV,CACD,OAAON,EAAQZ,KAAYC,EAAO,EASzB2B,EAAOjB,EAAWkB,GAQlBC,EAAMnB,EAAWoB,GAQjBC,EAASrB,EAAWsB"}