<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#061a1f;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0a2329;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#061a1f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f9b200;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="accent1" cx="25%" cy="25%" r="50%">
      <stop offset="0%" style="stop-color:#f9b200;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#f9b200;stop-opacity:0" />
    </radialGradient>
    <radialGradient id="accent2" cx="75%" cy="75%" r="50%">
      <stop offset="0%" style="stop-color:#f9b200;stop-opacity:0.05" />
      <stop offset="100%" style="stop-color:#f9b200;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Background patterns -->
  <rect width="1200" height="630" fill="url(#accent1)"/>
  <rect width="1200" height="630" fill="url(#accent2)"/>
  
  <!-- Logo -->
  <g transform="translate(560, 120)">
    <circle cx="40" cy="40" r="36" fill="#F9B200"/>
    <rect x="24" y="32" width="32" height="4" fill="#000"/>
    <rect x="24" y="40" width="32" height="4" fill="#000"/>
    <rect x="24" y="48" width="32" height="4" fill="#000"/>
  </g>
  
  <!-- Main Title -->
  <text x="600" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="72" font-weight="bold" fill="url(#textGradient)">PixelVane</text>
  
  <!-- Accent Line -->
  <rect x="550" y="300" width="100" height="4" fill="#f9b200" opacity="0.8"/>
  
  <!-- Subtitle -->
  <text x="600" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="300" fill="rgba(255,255,255,0.9)">AI Music &amp; Audio Generation</text>
  
  <!-- Feature Tags -->
  <g transform="translate(300, 420)">
    <!-- AI Composition -->
    <rect x="0" y="0" width="160" height="50" rx="12" fill="rgba(249,178,0,0.1)" stroke="rgba(249,178,0,0.3)" stroke-width="1"/>
    <text x="80" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="500" fill="#f9b200">AI Composition</text>
    
    <!-- Audio Synthesis -->
    <rect x="200" y="0" width="160" height="50" rx="12" fill="rgba(249,178,0,0.1)" stroke="rgba(249,178,0,0.3)" stroke-width="1"/>
    <text x="280" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="500" fill="#f9b200">Audio Synthesis</text>
    
    <!-- Analysis -->
    <rect x="400" y="0" width="160" height="50" rx="12" fill="rgba(249,178,0,0.1)" stroke="rgba(249,178,0,0.3)" stroke-width="1"/>
    <text x="480" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="500" fill="#f9b200">Analysis</text>
  </g>
  
  <!-- Bottom tagline -->
  <text x="600" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="300" fill="rgba(255,255,255,0.7)">Unparalleled precision and forensically accurate reports</text>
</svg>
