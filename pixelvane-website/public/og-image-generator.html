<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PixelVane OG Image Generator</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .og-image {
            width: 1200px;
            height: 630px;
            background: linear-gradient(135deg, #061a1f 0%, #0a2329 50%, #061a1f 100%);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .bg-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(249, 178, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(249, 178, 0, 0.05) 0%, transparent 50%);
        }
        
        .content {
            text-align: center;
            z-index: 2;
            max-width: 800px;
            padding: 0 60px;
        }
        
        .logo {
            margin-bottom: 40px;
        }
        
        .logo svg {
            width: 80px;
            height: 80px;
        }
        
        .title {
            font-size: 72px;
            font-weight: 700;
            margin: 0 0 20px 0;
            background: linear-gradient(135deg, #ffffff 0%, #f9b200 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.1;
        }
        
        .subtitle {
            font-size: 32px;
            font-weight: 300;
            margin: 0 0 30px 0;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.3;
        }
        
        .features {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 40px;
        }
        
        .feature {
            background: rgba(249, 178, 0, 0.1);
            border: 1px solid rgba(249, 178, 0, 0.3);
            border-radius: 12px;
            padding: 16px 24px;
            font-size: 18px;
            font-weight: 500;
            color: #f9b200;
        }
        
        .accent-line {
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #f9b200 0%, transparent 100%);
            margin: 30px auto;
        }
    </style>
</head>
<body>
    <div class="og-image">
        <div class="bg-pattern"></div>
        <div class="content">
            <div class="logo">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="50" r="45" fill="#F9B200"/>
                    <path d="M30 40h40v5H30zm0 10h40v5H30zm0 10h40v5H30z" fill="#000"/>
                </svg>
            </div>
            <h1 class="title">PixelVane</h1>
            <div class="accent-line"></div>
            <p class="subtitle">AI Music & Audio Generation</p>
            <div class="features">
                <div class="feature">AI Composition</div>
                <div class="feature">Audio Synthesis</div>
                <div class="feature">Analysis</div>
            </div>
        </div>
    </div>
</body>
</html>
