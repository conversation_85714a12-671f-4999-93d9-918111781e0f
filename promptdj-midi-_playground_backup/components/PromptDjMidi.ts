/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import { css, html, LitElement } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { styleMap } from 'lit/directives/style-map.js';
import { classMap } from 'lit/directives/class-map.js';
import { GoogleGenAI, Type } from '@google/genai';

import { throttle } from '../utils/throttle';

import './PromptController';
import './PlayPauseButton';
import type { PlaybackState, Prompt } from '../types';
import { MidiDispatcher } from '../utils/MidiDispatcher';
import { LiveMusicHelper } from '../utils/LiveMusicHelper';

/** The main UI component, inspired by DAW layouts. */
@customElement('prompt-dj-midi')
export class PromptDjMidi extends LitElement {
  static override styles = css`
    :host {
      height: 100%;
      width: 100%;
      display: flex;
      box-sizing: border-box;
      background: #0A0A0A;
      color: #f0f0f0;
      font-family: 'Inter', sans-serif;
    }
    #sidebar {
      width: 320px;
      min-width: 320px;
      height: 100%;
      background: #111111;
      display: flex;
      flex-direction: column;
      overflow-y: hidden;
      border-right: 1px solid rgba(255, 255, 255, 0.1);
    }
    #sidebar-header {
      padding: 20px 20px 15px 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      flex-shrink: 0;
    }
    #sidebar-header h2 {
      color: white;
      margin: 0 0 15px 0;
      font-size: 20px;
      font-weight: 500;
    }
    #search-input {
      width: 100%;
      padding: 8px 12px;
      border-radius: 6px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      background: #1C1C1C;
      color: white;
      font-family: inherit;
      font-size: 14px;
      box-sizing: border-box;
      transition: border-color 0.2s ease;
    }
    #search-input:focus {
      outline: none;
      border-color: #F9B200;
    }
    #asset-list {
      flex-grow: 1;
      padding: 10px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      overflow-y: auto;
    }
    .asset-item {
      display: flex;
      align-items: center;
      gap: 5px;
      background: #1C1C1C;
      border-radius: 6px;
      border: 1px solid transparent;
      padding: 0 8px 0 15px;
      transition: all 0.2s ease;
    }
    .asset-item:hover {
      border-color: rgba(255, 255, 255, 0.2);
      background: #282828;
    }
    .asset-info {
      flex-grow: 1;
      display: flex;
      align-items: center;
      gap: 12px;
      font-family: inherit;
      font-size: 14px;
      color: #f0f0f0;
      user-select: none;
      overflow: hidden;
      white-space: nowrap;
    }
    .asset-info span {
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .asset-actions {
        display: flex;
        flex-shrink: 0;
    }
    .preview-btn, .add-btn {
      width: 36px;
      height: 36px;
      flex-shrink: 0;
      background: transparent;
      border: none;
      color: #999;
      cursor: pointer;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }
    .preview-btn:hover, .add-btn:hover {
      background: #3c3c3c;
      color: white;
    }
    .preview-btn.previewing {
      color: #F9B200;
    }
    .preview-btn svg {
      width: 20px;
      height: 20px;
    }
    .add-btn {
        font-size: 24px;
        font-weight: 300;
    }
    .asset-color-swatch {
      width: 14px;
      height: 14px;
      border-radius: 4px;
      flex-shrink: 0;
    }
    #main-content {
      flex-grow: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
      overflow: hidden;
    }
    #main-content::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><filter id="n"><feTurbulence type="fractalNoise" baseFrequency="0.65" numOctaves="3" stitchTiles="stitch"/></filter><rect width="100%" height="100%" filter="url(%23n)"/></svg>');
      opacity: 0.03;
      pointer-events: none;
      z-index: 0;
    }
    #background {
      /* No longer used for gradients, kept for structure */
      display: none;
    }
    #toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 20px;
      background: #111111;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      position: relative;
      z-index: 2;
      gap: 15px;
      flex-shrink: 0;
      height: 80px;
      box-sizing: border-box;
    }
    #transport-controls {
      display: flex;
      align-items: center;
      gap: 20px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    #midi-controls, #record-controls {
      display: flex;
      gap: 10px;
      align-items: center;
    }
    #track-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;
      padding: 20px;
      overflow-y: auto;
      position: relative;
      z-index: 1;
    }
    .empty-state {
      color: #888;
      text-align: center;
      margin: auto;
      font-size: 1.1em;
      line-height: 1.6;
    }
    play-pause-button {
      width: 60px;
      height: 60px;
    }
    button {
      font: inherit;
      font-weight: 500;
      cursor: pointer;
      color: #f0f0f0;
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 6px;
      user-select: none;
      padding: 6px 12px;
      transition: all 0.2s ease;
    }
    button:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.5);
    }
    button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: transparent;
    }

    button.active {
      background-color: #F9B200;
      color: #0A0A0A;
      border-color: #F9B200;
    }
    button.active:hover {
      background-color: #fbc13a;
    }

    button.record-active {
      background-color: #e74c3c;
      color: white;
      border-color: #e74c3c;
      animation: pulse 1.5s infinite;
    }
    #download-btn {
      background: #F9B200;
      color: #0A0A0A;
      border-color: #F9B200;
      font-weight: bold;
      text-decoration: none;
      display: inline-block;
    }
    #download-btn:hover {
      background: #fbc13a;
    }
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
      100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
    }
    select {
      font: inherit;
      padding: 5px 8px;
      background: #1C1C1C;
      color: #f0f0f0;
      border-radius: 6px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      outline: none;
      cursor: pointer;
    }
    #complexity-control {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      color: #ccc;
    }
    #complexity-control label {
      font-size: 10px;
      font-weight: bold;
      letter-spacing: 1px;
      user-select: none;
    }
    #complexity-slider {
      -webkit-appearance: none;
      appearance: none;
      width: 150px;
      height: 5px;
      background: #0A0A0A;
      outline: none;
      border-radius: 3px;
      cursor: pointer;
    }
    #complexity-slider::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 15px;
      height: 15px;
      background: #f0f0f0;
      cursor: pointer;
      border-radius: 50%;
      border: none;
    }
    #complexity-slider::-moz-range-thumb {
      width: 15px;
      height: 15px;
      background: #f0f0f0;
      cursor: pointer;
      border-radius: 50%;
      border: none;
    }

    /* AI Prompt Window Styles */
    #ai-window {
      position: absolute;
      bottom: 20px;
      right: 20px;
      width: 350px;
      background: #1C1C1C;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.5);
      z-index: 10;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    }
    #ai-window.minimized {
      height: 40px;
      width: 200px;
    }
    #ai-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      height: 40px;
      background: #282828;
      cursor: pointer;
      flex-shrink: 0;
      user-select: none;
    }
    #ai-header h3 {
      color: white;
      font-size: 14px;
      font-weight: 500;
      margin: 0;
    }
    #ai-header button {
      background: none;
      border: none;
      color: #ccc;
      font-size: 20px;
      padding: 0;
      width: 24px;
      height: 24px;
      line-height: 24px;
    }
    #ai-header button:hover {
      color: white;
    }
    #ai-body {
      padding: 15px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      flex-grow: 1;
    }
    #ai-prompt-input {
      width: 100%;
      height: 80px;
      box-sizing: border-box;
      background: #0A0A0A;
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      border-radius: 4px;
      padding: 10px;
      resize: none;
      font-family: inherit;
    }
    #ai-generate-btn {
      background: #F9B200;
      border: none;
      color: #0A0A0A;
      padding: 10px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 600;
    }
    #ai-generate-btn:hover {
        background: #fbc13a;
    }
    #ai-generate-btn:disabled {
      background: #444;
      cursor: wait;
      color: #888;
    }
  `;

  private allPrompts: Map<string, Prompt>;
  private midiDispatcher: MidiDispatcher;
  private ai: GoogleGenAI;
  private availablePromptsForAI: { text: string, category: string }[];
  private liveMusicHelper: LiveMusicHelper;


  @state() private activePromptIds = new Set<string>();
  @property({ type: Boolean }) private showMidi = false;
  @property({ type: String }) public playbackState: PlaybackState = 'stopped';
  @state() public audioLevel = 0;
  @state() private midiInputIds: string[] = [];
  @state() private activeMidiInputId: string | null = null;
  @state() private complexity = 0.5;
  @state() private searchQuery = '';
  @state() public isRecording = false;
  @state() private downloadUrl: string | null = null;
  @state() private downloadFilename = 'prompt-dj-beat.webm';
  @state() private isAiWindowMinimized = false;
  @state() private isAiGenerating = false;
  @state() private aiPromptText = '';
  @state() private previewingPromptId: string | null = null;
  @state() private wasPlayingBeforePreview = false;

  @property({ type: Object })
  private filteredPrompts = new Set<string>();

  constructor(
    initialPrompts: Map<string, Prompt>,
    ai: GoogleGenAI,
    availablePromptsForAI: { text: string, category: string }[],
    liveMusicHelper: LiveMusicHelper
  ) {
    super();
    this.allPrompts = initialPrompts;
    this.midiDispatcher = new MidiDispatcher();
    this.ai = ai;
    this.availablePromptsForAI = availablePromptsForAI;
    this.liveMusicHelper = liveMusicHelper;
  }

  private dispatchPromptsChanged() {
    const promptsToSend = new Map<string, Prompt>();
    
    // If in preview mode, send only the preview prompt
    if (this.previewingPromptId) {
      const prompt = this.allPrompts.get(this.previewingPromptId);
      if (prompt) {
        const previewPromptClone = { ...prompt, weight: 1.0 };
        promptsToSend.set(this.previewingPromptId, previewPromptClone);
      }
    } else {
       // Add only active prompts
      for (const promptId of this.activePromptIds) {
        const prompt = this.allPrompts.get(promptId);
        if (prompt && prompt.weight > 0) {
          promptsToSend.set(promptId, prompt);
        }
      }
      // Add a special prompt for complexity, with its weight based on the slider.
      if (this.complexity > 0.05) { // Using a small deadzone
        const complexityPrompt: Prompt = {
          promptId: 'internal-complexity-prompt',
          text: 'intricate and layered instrumentation, complex harmonies, detailed patterns',
          weight: this.complexity * 0.75, // Scale it a bit, max weight will be 0.75
          cc: -1, // Not associated with a MIDI CC
          color: 'transparent', // Not visible in the UI
          category: 'Complexity',
        };
        promptsToSend.set(complexityPrompt.promptId, complexityPrompt);
      }
    }
    
    this.dispatchEvent(
      new CustomEvent('prompts-changed', { detail: promptsToSend }),
    );
  }

  private handleComplexityChange(e: Event) {
    this.complexity = (e.target as HTMLInputElement).valueAsNumber;
    this.dispatchPromptsChanged();
  }

  private handlePromptChanged(e: CustomEvent<Prompt>) {
    const { promptId, text, weight, cc, color, category } = e.detail;
    const prompt = this.allPrompts.get(promptId);
    if (!prompt) return;

    prompt.text = text;
    prompt.weight = weight;
    prompt.cc = cc;
    prompt.color = color;
    prompt.category = category;
    
    this.allPrompts.set(promptId, prompt);
    this.dispatchPromptsChanged();
    this.requestUpdate('allPrompts');
  }

  private addPrompt(promptId: string) {
    // Cannot add prompts while one is being previewed.
    if (this.previewingPromptId) return;
    const prompt = this.allPrompts.get(promptId);
    if(prompt) {
      prompt.weight = 0.5; // Set a default weight
      this.allPrompts.set(promptId, prompt);
    }
    this.activePromptIds.add(promptId);
    this.dispatchPromptsChanged();
    this.requestUpdate('activePromptIds');
  }

  private handlePromptRemoved(e: CustomEvent<{promptId: string}>) {
    const {promptId} = e.detail;
    const prompt = this.allPrompts.get(promptId);
    if (prompt) {
      prompt.weight = 0; // Reset weight
      this.allPrompts.set(promptId, prompt);
    }
    this.activePromptIds.delete(promptId);
    this.dispatchPromptsChanged();
    this.requestUpdate('activePromptIds');
  }

  private readonly makeBackground = throttle(
    () => {
      // This function is no longer needed for the background, but we keep the throttler
      // in case we want to reintroduce dynamic elements later.
      return '';
    },
    30,
  );

  private toggleShowMidi() {
    return this.setShowMidi(!this.showMidi);
  }

  public async setShowMidi(show: boolean) {
    this.showMidi = show;
    if (!this.showMidi) return;
    try {
      const inputIds = await this.midiDispatcher.getMidiAccess();
      this.midiInputIds = inputIds;
      this.activeMidiInputId = this.midiDispatcher.activeMidiInputId;
    } catch (e: any) {
      this.showMidi = false;
      this.dispatchEvent(new CustomEvent('error', {detail: e.message}));
    }
  }

  private handleMidiInputChange(event: Event) {
    const selectElement = event.target as HTMLSelectElement;
    const newMidiId = selectElement.value;
    this.activeMidiInputId = newMidiId;
    this.midiDispatcher.activeMidiInputId = newMidiId;
  }
  
  private handleSearchInput(e: Event) {
    this.searchQuery = (e.target as HTMLInputElement).value;
  }

  private toggleRecording() {
    this.dispatchEvent(new CustomEvent('toggle-recording'));
    // If a download is available, this new action clears it
    if (this.downloadUrl) {
      this.clearDownload();
    }
  }

  public setDownload(blob: Blob, prompts: string[]) {
    if (this.downloadUrl) {
      URL.revokeObjectURL(this.downloadUrl);
    }
    this.downloadUrl = URL.createObjectURL(blob);
    const usedPrompts = prompts.join('-').replace(/[^a-z0-9-_]/gi, '_');
    this.downloadFilename = `${usedPrompts || 'prompt-dj-beat'}.webm`;
  }
  
  public clearDownload() {
    if (this.downloadUrl) {
      URL.revokeObjectURL(this.downloadUrl);
      this.downloadUrl = null;
    }
  }

  private async playPause() {
    if (this.previewingPromptId) {
      this.previewingPromptId = null;
      this.wasPlayingBeforePreview = false;
      await this.liveMusicHelper.stop();
    } else {
      await this.liveMusicHelper.playPause();
    }
    this.clearDownload();
  }

  public addFilteredPrompt(prompt: string) {
    this.filteredPrompts = new Set([...this.filteredPrompts, prompt]);
  }

  private async handlePreviewToggle(promptIdToPreview: string) {
    const isCurrentlyPreviewingThis = this.previewingPromptId === promptIdToPreview;
    
    // If stopping the current preview
    if (isCurrentlyPreviewingThis) {
      this.previewingPromptId = null;
      await this.liveMusicHelper.pause();
      if (this.wasPlayingBeforePreview) {
        this.dispatchPromptsChanged(); // Restore original prompts
        await this.liveMusicHelper.play();
      }
      this.wasPlayingBeforePreview = false;
      return;
    }
    
    // If starting a new preview
    if (this.previewingPromptId === null) {
      this.wasPlayingBeforePreview = this.playbackState === 'playing' || this.playbackState === 'loading';
      if (this.wasPlayingBeforePreview) {
        await this.liveMusicHelper.pause();
      }
    }
    
    this.previewingPromptId = promptIdToPreview;
    this.dispatchPromptsChanged();
    await this.liveMusicHelper.play();
  }

  private buildCategorizedPromptList(): string {
    const categories = new Map<string, string[]>();
    for (const prompt of this.availablePromptsForAI) {
      if (!categories.has(prompt.category)) {
        categories.set(prompt.category, []);
      }
      categories.get(prompt.category)!.push(prompt.text);
    }

    let formattedList = "Here are the available prompts, grouped by category:\n\n";
    for (const [category, prompts] of categories.entries()) {
      formattedList += `**${category}**\n`;
      formattedList += prompts.map(p => `- ${p}`).join('\n');
      formattedList += '\n\n';
    }
    return formattedList;
  }

  private async generateBeatFromPrompt() {
    if (!this.aiPromptText || this.isAiGenerating) return;
    this.isAiGenerating = true;

    const schema = {
      type: Type.OBJECT,
      properties: {
        prompts: {
          type: Type.ARRAY,
          description: 'An array of objects, each representing a selected prompt and its weight.',
          items: {
            type: Type.OBJECT,
            properties: {
              promptName: {
                type: Type.STRING,
                description: 'The exact name of the prompt string selected from the provided list.'
              },
              weight: {
                type: Type.NUMBER,
                description: 'A value between 0.1 and 2.0 indicating the prominence of this track. 0.5 is a good default.'
              }
            },
            required: ['promptName', 'weight']
          }
        },
        complexity: {
            type: Type.NUMBER,
            description: 'A value between 0.0 and 1.0 representing the overall musical complexity. 0.5 is average.'
        }
      },
      required: ['prompts', 'complexity']
    };

    const categorizedList = this.buildCategorizedPromptList();

    const systemInstruction = `You are an expert AI music producer. Your goal is to interpret the user's request and select a small, coherent set of musical building blocks from the provided list to create a beat. You will also determine the relative volume (weight) of each track and the overall complexity of the composition.

Follow these steps:
1.  **Analyze the Request:** Identify the core genre, mood, instrumentation, and any specific elements the user mentioned.
2.  **Build a Foundation:** ALWAYS include a core rhythmic element (like a drum beat or a specific kick/snare) and a bass element (like a bassline or an 808) unless the user's request makes this impossible. These should generally have a higher weight (e.g., 0.6-1.0).
3.  **Add Character:** Select 3-5 additional prompts for melody, harmony, texture, and FX. Assign weights based on their intended role. A main melody might be 0.7, while a background texture could be 0.3.
4.  **Determine Complexity:** Based on the user's request, choose a complexity value from 0.0 (very simple) to 1.0 (very intricate). For example, 'lo-fi chill beat' might be low complexity (0.2-0.4), while 'complex prog metal' would be high (0.8-1.0).
5.  **Be Coherent:** Ensure all selected prompts work together musically. For example, don't mix a 'Salsa' rhythm with a 'Death Metal' guitar riff unless specifically asked.
6.  **Be Concise:** Select a total of 5 to 7 prompts. Do not exceed 7. This ensures a clean, focused musical idea.
7.  **Use Exact Prompts:** You MUST only choose prompts that appear verbatim in the provided list.

Based on this, analyze the user's request and respond with a JSON object containing a 'prompts' array (with promptName and weight for each) and a 'complexity' value.`;

    try {
      const response = await this.ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: `${categorizedList}User request: "${this.aiPromptText}"`,
        config: {
          responseMimeType: 'application/json',
          responseSchema: schema,
          systemInstruction: systemInstruction
        }
      });
      
      const jsonText = response.text.trim();
      const result = JSON.parse(jsonText);
      const selectedPrompts: { promptName: string, weight: number }[] = result.prompts || [];
      const newComplexity: number | undefined = result.complexity;

      const newActiveIds = new Set<string>();
      
      // Map text back to promptId
      const textToIdMap = new Map<string, string>();
      for (const prompt of this.allPrompts.values()) {
        textToIdMap.set(prompt.text, prompt.promptId);
      }
      
      // Reset weights of old tracks
      for (const oldId of this.activePromptIds) {
          const prompt = this.allPrompts.get(oldId);
          if (prompt) prompt.weight = 0;
      }
      
      this.activePromptIds.clear();

      // Process new tracks from AI
      for (const selectedPrompt of selectedPrompts) {
        const promptId = textToIdMap.get(selectedPrompt.promptName);
        if (promptId) {
            newActiveIds.add(promptId);
            const prompt = this.allPrompts.get(promptId);
            if (prompt) {
                // clamp weight between 0 and 2
                prompt.weight = Math.max(0, Math.min(2, selectedPrompt.weight || 0.5));
            }
        }
      }
      
      this.activePromptIds = newActiveIds;
      
      // Set complexity
      if (newComplexity !== undefined) {
          // clamp complexity between 0 and 1
          this.complexity = Math.max(0, Math.min(1, newComplexity));
      }

      this.dispatchPromptsChanged();
      this.requestUpdate();

    } catch (e) {
      console.error(e);
      this.dispatchEvent(new CustomEvent('error', {detail: 'AI beat generation failed. Please try again.'}));
    } finally {
      this.isAiGenerating = false;
    }
  }

  override render() {
    const bg = styleMap({
      backgroundImage: this.makeBackground(),
    });
    return html`
      <div id="sidebar">
        <div id="sidebar-header">
          <h2>Prompts</h2>
          <input 
            type="search" 
            id="search-input" 
            placeholder="Search assets..."
            .value=${this.searchQuery}
            @input=${this.handleSearchInput}
          />
        </div>
        <div id="asset-list">
          ${this.renderSidebar()}
        </div>
      </div>
      <div id="main-content">
        <div id="background" style=${bg}></div>
        <div id="toolbar">
          <div id="midi-controls">
            <button
              @click=${this.toggleShowMidi}
              class=${this.showMidi ? 'active' : ''}
              >MIDI</button
            >
            <select
              @change=${this.handleMidiInputChange}
              .value=${this.activeMidiInputId || ''}
              style=${this.showMidi ? '' : 'visibility: hidden'}>
              ${this.midiInputIds.length > 0
            ? this.midiInputIds.map(
              (id) =>
                html`<option value=${id}>
                        ${this.midiDispatcher.getDeviceName(id)}
                      </option>`,
            )
            : html`<option value="">No devices found</option>`}
            </select>
          </div>
          <div id="transport-controls">
            <play-pause-button .playbackState=${this.playbackState} @click=${this.playPause}></play-pause-button>
            <div id="complexity-control">
              <label for="complexity-slider">COMPLEXITY</label>
              <input
                id="complexity-slider"
                type="range"
                min="0"
                max="1"
                step="0.01"
                .value=${this.complexity}
                @input=${this.handleComplexityChange}
              />
            </div>
          </div>
          <div id="record-controls">
            <button 
              @click=${this.toggleRecording}
              class=${this.isRecording ? 'record-active' : ''}
              title=${this.isRecording ? 'Stop Recording' : 'Record Session'}
              >${this.isRecording ? 'STOP' : 'REC'}
            </button>
            ${this.downloadUrl ? html`
              <a id="download-btn" class="button" .href=${this.downloadUrl} .download=${this.downloadFilename}>DOWNLOAD</a>
            ` : ''}
          </div>
        </div>
        <div id="track-area">
          ${this.renderTrackArea()}
        </div>
        ${this.renderAiWindow()}
      </div>
    `;
  }
  
  private renderSidebar() {
    const lowerCaseQuery = this.searchQuery.toLowerCase();
    const availablePrompts = [...this.allPrompts.values()]
      .filter(p => !this.activePromptIds.has(p.promptId) && (p.text.toLowerCase().includes(lowerCaseQuery) || p.category.toLowerCase().includes(lowerCaseQuery)))
      .sort((a, b) => a.text.localeCompare(b.text));
      
    if (availablePrompts.length === 0 && this.searchQuery) {
      return html`<div class="empty-state">No prompts found.</div>`;
    }

    const playIcon = html`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"></path></svg>`;
    const stopIcon = html`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6 6h12v12H6z"></path></svg>`;

    return availablePrompts.map(prompt => html`
      <div class="asset-item">
        <div class="asset-info" title=${prompt.text}>
            <div class="asset-color-swatch" style="background-color: ${prompt.color}"></div>
            <span>${prompt.text}</span>
        </div>
        <div class="asset-actions">
            <button 
                class="preview-btn ${this.previewingPromptId === prompt.promptId ? 'previewing' : ''}" 
                @click=${() => this.handlePreviewToggle(prompt.promptId)} 
                title=${this.previewingPromptId === prompt.promptId ? 'Stop preview' : `Preview ${prompt.text}`}>
                ${this.previewingPromptId === prompt.promptId ? stopIcon : playIcon}
            </button>
            <button class="add-btn" @click=${() => this.addPrompt(prompt.promptId)} title="Add ${prompt.text} to tracks">+</button>
        </div>
      </div>
    `);
  }
  
  private renderTrackArea() {
    const activePrompts = [...this.allPrompts.values()].filter(p => this.activePromptIds.has(p.promptId));
    
    if (activePrompts.length === 0) {
      return html`<div class="empty-state">Add prompts from the sidebar or use the<br>AI Beat Generator to start.</div>`;
    }

    return activePrompts.map((prompt) => {
      return html`<prompt-controller
        .promptId=${prompt.promptId}
        ?filtered=${this.filteredPrompts.has(prompt.text)}
        .cc=${prompt.cc}
        .text=${prompt.text}
        .weight=${prompt.weight}
        .color=${prompt.color}
        .category=${prompt.category}
        .midiDispatcher=${this.midiDispatcher}
        ?showCC=${this.showMidi}
        .audioLevel=${this.audioLevel}
        @prompt-changed=${this.handlePromptChanged}
        @prompt-removed=${this.handlePromptRemoved}>
      </prompt-controller>`;
    });
  }

  private renderAiWindow() {
    const windowClasses = classMap({ minimized: this.isAiWindowMinimized });
    return html`
      <div id="ai-window" class=${windowClasses}>
        <div id="ai-header" @click=${() => this.isAiWindowMinimized = !this.isAiWindowMinimized}>
          <h3>AI Beat Generator</h3>
          <button title="Toggle Window">${this.isAiWindowMinimized ? '□' : '—'}</button>
        </div>
        <div id="ai-body">
          <textarea
            id="ai-prompt-input"
            placeholder="e.g., a dark, energetic techno track with a punchy kick and some acid bass"
            .value=${this.aiPromptText}
            @input=${(e: Event) => this.aiPromptText = (e.target as HTMLTextAreaElement).value}
            ?disabled=${this.isAiGenerating}
          ></textarea>
          <button
            id="ai-generate-btn"
            @click=${this.generateBeatFromPrompt}
            ?disabled=${this.isAiGenerating || !this.aiPromptText.trim()}
          >
            ${this.isAiGenerating ? 'Generating...' : 'Generate Beat'}
          </button>
        </div>
      </div>
    `;
  }
}