/**
 * @fileoverview Control real time music with a MIDI controller
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

import type { PlaybackState, Prompt } from './types';
import { GoogleGenAI, LiveMusicFilteredPrompt } from '@google/genai';
import { PromptDjMidi } from './components/PromptDjMidi';
import { ToastMessage } from './components/ToastMessage';
import { LiveMusicHelper } from './utils/LiveMusicHelper';
import { AudioAnalyser } from './utils/AudioAnalyser';

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });
const model = 'lyria-realtime-exp';

function main() {
  const initialPrompts = buildInitialPrompts();

  const liveMusicHelper = new LiveMusicHelper(ai, model);
  const pdjMidi = new PromptDjMidi(initialPrompts, ai, PROMPT_DEFINITIONS, liveMusicHelper);
  document.body.appendChild(pdjMidi);

  const toastMessage = new ToastMessage();
  document.body.appendChild(toastMessage);

  liveMusicHelper.setWeightedPrompts(new Map());

  const audioAnalyser = new AudioAnalyser(liveMusicHelper.audioContext);
  liveMusicHelper.extraDestination = audioAnalyser.node;

  pdjMidi.addEventListener('prompts-changed', ((e: Event) => {
    const customEvent = e as CustomEvent<Map<string, Prompt>>;
    const prompts = customEvent.detail;
    liveMusicHelper.setWeightedPrompts(prompts);
  }));

  // Recording listeners
  pdjMidi.addEventListener('toggle-recording', () => {
    if (liveMusicHelper.isRecording) {
      liveMusicHelper.stopRecording();
    } else {
      liveMusicHelper.startRecording();
    }
  });

  liveMusicHelper.addEventListener('recording-state-changed', (e) => {
    const { isRecording } = (e as CustomEvent<{isRecording: boolean}>).detail;
    pdjMidi.isRecording = isRecording;
  });

  liveMusicHelper.addEventListener('recording-finished', (e) => {
    const { blob, prompts } = (e as CustomEvent<{blob: Blob, prompts: string[]}>).detail;
    pdjMidi.setDownload(blob, prompts);
  });
  // End recording listeners

  liveMusicHelper.addEventListener('playback-state-changed', ((e: Event) => {
    const customEvent = e as CustomEvent<PlaybackState>;
    const playbackState = customEvent.detail;
    pdjMidi.playbackState = playbackState;
    playbackState === 'playing' ? audioAnalyser.start() : audioAnalyser.stop();
  }));

  liveMusicHelper.addEventListener('filtered-prompt', ((e: Event) => {
    const customEvent = e as CustomEvent<LiveMusicFilteredPrompt>;
    const filteredPrompt = customEvent.detail;
    toastMessage.show(filteredPrompt.filteredReason!)
    pdjMidi.addFilteredPrompt(filteredPrompt.text!);
  }));

  const errorToast = ((e: Event) => {
    const customEvent = e as CustomEvent<string>;
    const error = customEvent.detail;
    toastMessage.show(error);
  });

  liveMusicHelper.addEventListener('error', errorToast);
  pdjMidi.addEventListener('error', errorToast);

  audioAnalyser.addEventListener('audio-level-changed', ((e: Event) => {
    const customEvent = e as CustomEvent<number>;
    const level = customEvent.detail;
    pdjMidi.audioLevel = level;
  }));

}

const PALETTE = ['#3498db', '#2ecc71', '#9b59b6', '#f1c40f', '#e67e22', '#e74c3c', '#1abc9c', '#34495e', '#ecf0f1', '#7f8c8d', '#f39c12', '#d35400', '#c0392b', '#16a085', '#27ae60', '#2980b9', '#8e44ad', '#bdc3c7', '#5dade2', '#58d68d', '#af7ac5', '#f4d03f', '#eb984e', '#edbb99', '#a3e4d7', '#d2b4de', '#f5b7b1'];

const PROMPT_DEFINITIONS: { text: string, category: string }[] = [
  // --- HIP HOP ASSETS ---
  { text: 'Hip Hop', category: 'Hip Hop Genre' },
  { text: 'Trap Beat', category: 'Hip Hop Drums' },
  { text: 'Boom Bap Drums', category: 'Hip Hop Drums' },
  { text: 'Hard-hitting trap drums', category: 'Hip Hop Drums'},
  { text: 'Lo-fi Hip Hop', category: 'Hip Hop Genre' },
  { text: 'Cloud Rap', category: 'Hip Hop Genre' },
  { text: 'Rage Beat', category: 'Hip Hop Drums' },
  { text: 'Plugg Music', category: 'Hip Hop Genre' },
  { text: '808 Bass', category: 'Hip Hop Bass' },
  { text: 'Distorted 808', category: 'Hip Hop Bass' },
  { text: 'Clean 808', category: 'Hip Hop Bass' },
  { text: 'Spun 808', category: 'Hip Hop Bass' },
  { text: 'Zaytoven 808', category: 'Hip Hop Bass' },
  { text: 'Deep 808 with long decay', category: 'Hip Hop Bass' },
  { text: 'Jersey Club Kick Pattern', category: 'Hip Hop Drums' },
  { text: 'Phonk Cowbell Loop', category: 'Hip Hop Melodic' },
  { text: 'Phonk drift cowbell', category: 'Hip Hop Melodic' },
  { text: 'Memphis Rap Cowbell', category: 'Hip Hop Melodic' },
  { text: 'Vinyl Crackle', category: 'Hip Hop FX' },
  { text: 'Sampled Vocals', category: 'Hip Hop Vocals' },
  { text: 'Vocal Chop Melody', category: 'Hip Hop Vocals' },
  { text: 'Chopped Soul Sample', category: 'Hip Hop Melodic' },
  { text: 'Mellow Rhodes', category: 'Hip Hop Keys' },
  { text: 'Drill Beat', category: 'Hip Hop Drums' },
  { text: 'Drill 808 Glide', category: 'Hip Hop Bass' },
  { text: 'G-Funk Synth Lead', category: 'Hip Hop Melodic' },
  { text: 'Crate Digger Feel', category: 'Hip Hop Mood' },
  { text: 'Turntable Scratches', category: 'Hip Hop FX' },
  { text: 'Baby scratch', category: 'Hip Hop FX' },
  { text: 'Reversed Cymbal', category: 'Hip Hop FX' },
  { text: 'Old School Rap Vibe', category: 'Hip Hop Mood' },
  { text: 'Modern Trap Hi-Hats', category: 'Hip Hop Drums' },
  { text: 'Fast Hi-Hat Rolls', category: 'Hip Hop Drums' },
  { text: 'Triplet hi-hat pattern', category: 'Hip Hop Drums' },
  { text: 'Rap Ad-libs', category: 'Hip Hop Vocals' },
  { text: 'Air horn sound effect', category: 'Hip Hop FX' },
  { text: 'Gunshot sound effect', category: 'Hip Hop FX' },
  { text: 'Jersey club bed squeak fx', category: 'Hip Hop FX' },
  { text: 'Producer tag', category: 'Hip Hop Vocals' },
  { text: 'Dark piano melody for trap', category: 'Hip Hop Keys' },

  // --- JAZZ ASSETS ---
  { text: 'Jazz', category: 'Jazz Genre' },
  { text: 'Swing Rhythm', category: 'Jazz Drums' },
  { text: 'Walking Bassline', category: 'Jazz Bass' },
  { text: 'Jaco Pastorius style fretless bass', category: 'Jazz Bass' },
  { text: 'Saxophone Solo', category: 'Jazz Melodic' },
  { text: 'Mellow Saxophone', category: 'Jazz Melodic' },
  { text: 'Aggressive Tenor Saxophone', category: 'Jazz Melodic' },
  { text: 'Muted Trumpet Solo', category: 'Jazz Brass' },
  { text: 'Cool Jazz Piano', category: 'Jazz Keys' },
  { text: 'Bebop Energy', category: 'Jazz Mood' },
  { text: 'Jazz Fusion', category: 'Jazz Genre' },
  { text: 'Acid Jazz', category: 'Jazz Genre' },
  { text: 'Scat Vocals', category: 'Jazz Vocals' },
  { text: 'Brush Drums', category: 'Jazz Drums' },
  { text: 'Smooth Jazz Guitar', category: 'Jazz Melodic' },
  { text: 'Wes Montgomery style guitar', category: 'Jazz Melodic' },
  { text: 'Big Band Brass Section', category: 'Jazz Brass' },
  { text: 'Improvised Feel', category: 'Jazz Mood' },
  { text: 'Upright Bass', category: 'Jazz Bass' },
  { text: 'Smoky Jazz Club', category: 'Jazz Mood' },
  { text: 'Vibraphone solo', category: 'Jazz Melodic' },
  { text: 'Miles Davis style trumpet', category: 'Jazz Brass' },
  { text: 'John Coltrane style saxophone', category: 'Jazz Melodic' },

  // --- FUNK ASSETS ---
  { text: 'Funk', category: 'Funk Genre' },
  { text: 'Slap Bass', category: 'Funk Bass' },
  { text: 'Wah-wah Guitar', category: 'Funk Guitar' },
  { text: 'Funky Drummer Break', category: 'Funk Drums' },
  { text: 'Brass Section Stabs', category: 'Funk Brass' },
  { text: 'Clavinet', category: 'Funk Keys' },
  { text: 'Syncopated Horns', category: 'Funk Brass' },
  { text: 'Groovy Congas', category: 'Funk Percussion' },
  { text: 'P-Funk Synthesizer', category: 'Funk Synth' },
  { text: 'Talk Box Vocals', category: 'Funk Vocals' },
  { text: 'Tight Pocket Groove', category: 'Funk Mood' },
  { text: 'Funky guitar single-note riff', category: 'Funk Guitar' },
  { text: 'James Brown style vocal grunt', category: 'Funk Vocals' },

  // --- LO-FI ASSETS ---
  { text: 'Lo-fi', category: 'Lo-fi Genre' },
  { text: 'Rain Sounds', category: 'Lo-fi FX' },
  { text: 'Tape Hiss', category: 'Lo-fi FX' },
  { text: 'Wobbly Synth', category: 'Lo-fi Synth' },
  { text: 'Dusty Drum Loop', category: 'Lo-fi Drums' },
  { text: 'Relaxing Guitar Melody', category: 'Lo-fi Melodic' },
  { text: 'Upright Piano Melody', category: 'Lo-fi Keys' },
  { text: 'Muffled piano chords', category: 'Lo-fi Keys' },
  { text: 'Page Turn FX', category: 'Lo-fi FX' },
  { text: 'Chillhop Vibe', category: 'Lo-fi Mood' },
  { text: 'Warbling Electric Piano', category: 'Lo-fi Keys' },
  { text: 'Nostalgic Mood', category: 'Lo-fi Mood' },
  { text: 'Cozy Atmosphere', category: 'Lo-fi Mood' },
  { text: 'Cassette Tape Flutter', category: 'Lo-fi FX' },
  { text: 'Vinyl stop effect', category: 'Lo-fi FX' },
  { text: 'Reversed piano melody', category: 'Lo-fi Keys' },
  { text: 'Kalimba melody', category: 'Lo-fi Melodic' },
  { text: 'Melodica melody', category: 'Lo-fi Melodic'},

  // --- ELECTRONIC GENRES ---
  { text: 'Techno', category: 'Electronic Genre' },
  { text: 'Hard Techno', category: 'Electronic Genre' },
  { text: 'Acid Techno', category: 'Electronic Genre' },
  { text: 'Rumbling techno kick', category: 'Electronic Drums' },
  { text: 'Berlin School sequence', category: 'Electronic Synth' },
  { text: 'House', category: 'Electronic Genre' },
  { text: 'Deep House', category: 'Electronic Genre' },
  { text: 'Chicago House', category: 'Electronic Genre' },
  { text: 'Tech House', category: 'Electronic Genre' },
  { text: 'Slap House', category: 'Electronic Genre'},
  { text: 'Big Room House', category: 'Electronic Genre' },
  { text: 'Trance', category: 'Electronic Genre' },
  { text: 'Uplifting Trance', category: 'Electronic Genre' },
  { text: 'Goa Trance', category: 'Electronic Genre' },
  { text: 'Trance gate pad', category: 'Electronic Synth' },
  { text: 'Hoover Synth', category: 'Electronic Synth' },
  { text: 'Hardcore synth stabs', category: 'Electronic Synth' },
  { text: 'Drum and Bass', category: 'Electronic Genre' },
  { text: 'Amen Break', category: 'Electronic Drums' },
  { text: 'Neurofunk', category: 'Electronic Genre' },
  { text: 'Liquid D&B', category: 'Electronic Genre' },
  { text: 'Reese Bassline for D&B', category: 'Electronic Bass' },
  { text: 'Dubstep', category: 'Electronic Genre' },
  { text: 'Heavy Dubstep Growl Bass', category: 'Electronic Bass' },
  { text: 'Riddim', category: 'Electronic Genre' },
  { text: 'Future Bass', category: 'Electronic Genre' },
  { text: 'Future Bass Chords', category: 'Electronic Melodic' },
  { text: 'Synthwave', category: 'Electronic Genre' },
  { text: 'Darksynth', category: 'Electronic Genre' },
  { text: 'Hardwave', category: 'Electronic Genre' },
  { text: 'Ambient', category: 'Electronic Genre' },
  { text: 'Dark Ambient', category: 'Electronic Genre' },
  { text: 'Chillwave', category: 'Electronic Genre' },
  { text: 'Downtempo', category: 'Electronic Genre' },
  { text: 'Trip Hop', category: 'Electronic Genre' },
  { text: 'Glitch Hop', category: 'Electronic Genre' },
  { text: 'Chiptune', category: 'Electronic Genre' },
  { text: '8-bit melody', category: 'Electronic Melodic' },
  { text: 'Hardstyle', category: 'Electronic Genre' },
  { text: 'Hardstyle Kick', category: 'Electronic Drums' },
  { text: 'Gabber Kick', category: 'Electronic Drums' },
  { text: 'Donk Bass', category: 'Electronic Bass' },
  { text: 'Vaporwave', category: 'Electronic Genre' },
  { text: 'Eurodance', category: 'Electronic Genre' },
  { text: 'Future Garage', category: 'Electronic Genre' },
  { text: 'UK Garage', category: 'Electronic Genre' },
  { text: 'Jungle', category: 'Electronic Genre' },
  { text: 'Industrial', category: 'Electronic Genre' },
  { text: 'Psytrance', category: 'Electronic Genre' },
  { text: 'Rolling psytrance bassline', category: 'Electronic Bass' },
  { text: 'Minimal Techno', category: 'Electronic Genre' },
  { text: 'Progressive House', category: 'Electronic Genre' },
  { text: 'Dub Techno Chord Stabs', category: 'Electronic Melodic' },
  { text: 'IDM glitchy beat', category: 'Electronic Drums' },

  // --- ROCK & METAL GENRES ---
  { text: 'Rock and Roll', category: 'Rock Genre'},
  { text: 'Alternative Rock', category: 'Rock Genre' },
  { text: 'Indie Rock', category: 'Rock Genre' },
  { text: 'Post-Punk', category: 'Rock Genre' },
  { text: 'Shoegaze', category: 'Rock Genre' },
  { text: 'Grunge', category: 'Rock Genre'},
  { text: 'Nu Metal', category: 'Metal Genre'},
  { text: 'Math Rock', category: 'Rock Genre'},
  { text: 'Fuzzy Guitar', category: 'Rock Guitar' },
  { text: 'Wall of sound guitar', category: 'Rock Guitar' },
  { text: 'Jangle pop guitar', category: 'Rock Guitar'},
  { text: 'Chorus-laden clean guitar', category: 'Rock Guitar'},
  { text: 'Psychedelic Rock', category: 'Rock Genre' },
  { text: 'Goth Rock', category: 'Rock Genre' },
  { text: 'Thrash Metal', category: 'Metal Genre' },
  { text: 'Death Metal', category: 'Metal Genre' },
  { text: 'Blast beat drums', category: 'Metal Drums' },
  { text: 'Black Metal', category: 'Metal Genre' },
  { text: 'Progressive Metal', category: 'Metal Genre' },
  { text: 'Djent guitar riff', category: 'Metal Guitar' },
  { text: 'Sludge Metal', category: 'Metal Genre'},
  { text: 'Stoner Rock', category: 'Rock Genre' },
  { text: 'Distorted bass guitar', category: 'Rock Bass' },
  { text: 'Feedback swells', category: 'Rock Guitar' },

  // --- WORLD & TRADITIONAL GENRES ---
  { text: 'Reggae', category: 'World Genre' },
  { text: 'Dub', category: 'World Genre' },
  { text: 'Reggae organ skank', category: 'World Keys' },
  { text: 'Bossa Nova', category: 'World Genre' },
  { text: 'Salsa', category: 'World Genre' },
  { text: 'Afrobeat', category: 'World Genre' },
  { text: 'Amapiano', category: 'World Genre' },
  { text: 'Amapiano log drum', category: 'World Drums' },
  { text: 'Dembow rhythm', category: 'World Drums' },
  { text: 'Cumbia', category: 'World Genre'},
  { text: 'Kwaito', category: 'World Genre'},
  { text: 'Gamelan', category: 'World Percussion'},
  { text: 'Highlife Guitar', category: 'World Melodic' },
  { text: 'Folk', category: 'World Genre' },
  { text: 'Bollywood', category: 'World Genre' },
  { text: 'Sitar melody', category: 'World Melodic' },
  { text: 'Tabla rhythm', category: 'World Percussion' },
  { text: 'Dhol drum loop', category: 'World Percussion' },
  { text: 'Celtic', category: 'World Genre' },
  { text: 'Flamenco', category: 'World Genre' },
  { text: 'Samba', category: 'World Genre' },
  { text: 'Tango', category: 'World Genre' },
  { text: 'Mariachi Brass', category: 'World Brass' },
  { text: 'Kuduro Beat', category: 'World Drums' },
  { text: 'Steel pan melody', category: 'World Melodic' },
  { text: 'Baile Funk loop', category: 'World Drums' },
  { text: 'Afrobeats', category: 'World Genre' },

  // --- POP & OTHER GENRES ---
  { text: 'K-Pop', category: 'Pop Genre' },
  { text: 'J-Pop', category: 'Pop Genre' },
  { text: 'Hyperpop', category: 'Pop Genre' },
  { text: 'Highly processed vocals', category: 'Pop Vocals' },
  { text: 'Glitchy vocal chops', category: 'Pop Vocals' },
  { text: 'Autotuned vocals', category: 'Pop Vocals' },
  { text: 'Cinematic', category: 'Cinematic Genre' },
  { text: 'Disco', category: 'Pop Genre' },
  { text: 'Four-on-the-floor disco beat', category: 'Pop Drums' },
  { text: 'Orchestral', category: 'Cinematic Genre' },
  { text: 'Epic Orchestral', category: 'Cinematic Genre' },
  { text: 'Tension strings', category: 'Cinematic Strings' },
  { text: 'New Wave', category: 'Pop Genre' },
  { text: '80s Pop Synth', category: 'Pop Synth' },
  { text: 'Bubblegum bass', category: 'Pop Synth' },
  { text: 'Catchy pop piano chords', category: 'Pop Keys' },

  // --- DRUMS & PERCUSSION ---
  { text: '808 Drums', category: 'Drums' },
  { text: '909 Drums', category: 'Drums' },
  { text: '707 Drums', category: 'Drums' },
  { text: 'LinnDrum Machine', category: 'Drums' },
  { text: 'Acoustic Drums', category: 'Drums' },
  { text: 'Live Drums', category: 'Drums' },
  { text: 'Punchy Kick', category: 'Drums' },
  { text: 'Deep Kick Drum', category: 'Drums' },
  { text: 'Snappy Snare', category: 'Drums' },
  { text: 'Fat Snare', category: 'Drums' },
  { text: 'Trap snare', category: 'Drums' },
  { text: 'Electronic Percussion', category: 'Percussion' },
  { text: 'Acoustic Percussion', category: 'Percussion' },
  { text: 'Congas', category: 'Percussion' },
  { text: 'Bongos', category: 'Percussion' },
  { text: 'Timbales', category: 'Percussion' },
  { text: 'Shaker', category: 'Percussion' },
  { text: 'Tambourine', category: 'Percussion' },
  { text: 'Cowbell', category: 'Percussion' },
  { text: 'More Cowbell!', category: 'Percussion' },
  { text: 'Claps', category: 'Percussion' },
  { text: 'Finger Snaps', category: 'Percussion' },
  { text: 'Cymbals', category: 'Drums' },
  { text: 'Hi-Hats', category: 'Drums' },
  { text: 'Open Hi-Hat', category: 'Drums' },
  { text: 'Gated Reverb Snare', category: 'Drums' },
  { text: 'Tribal Drums', category: 'Percussion' },
  { text: 'Marching snare drum', category: 'Drums' },
  { text: 'Rimshot', category: 'Drums' },

  // --- BASS ---
  { text: 'Sub Bass', category: 'Bass' },
  { text: 'Funk Bassline', category: 'Bass' },
  { text: 'Acoustic Bass', category: 'Bass' },
  { text: 'Synth Bass', category: 'Bass' },
  { text: 'Wobble Bass', category: 'Bass' },
  { text: 'Reese Bass', category: 'Bass' },
  { text: '303 Bassline', category: 'Bass' },
  { text: 'Acid Bassline', category: 'Bass' },
  { text: 'TB-303 squelch', category: 'Bass' },
  { text: 'Moog Bass', category: 'Bass' },
  { text: 'Fretless Bass', category: 'Bass' },
  { text: 'Growl Bass', category: 'Bass' },
  { text: 'Pluck Bass', category: 'Bass' },

  // --- GUITARS ---
  { text: 'Acoustic Guitar', category: 'Guitar' },
  { text: 'Strummed acoustic guitar', category: 'Guitar' },
  { text: 'Fingerpicked acoustic guitar', category: 'Guitar' },
  { text: 'Electric Guitar', category: 'Guitar' },
  { text: 'Distorted Guitar', category: 'Guitar' },
  { text: 'Heavy Metal Guitar Riff', category: 'Guitar' },
  { text: 'Clean Electric Guitar', category: 'Guitar' },
  { text: 'Nylon String Guitar', category: 'Guitar' },
  { text: 'Jazzy Guitar Chords', category: 'Guitar' },
  { text: 'Palm-muted guitar', category: 'Guitar' },
  { text: 'Slide Guitar', category: 'Guitar' },
  { text: '12-string Guitar', category: 'Guitar' },
  
  // --- KEYBOARDS & SYNTHS ---
  { text: 'Piano', category: 'Keys' },
  { text: 'Grand Piano', category: 'Keys' },
  { text: 'Upright Piano', category: 'Keys' },
  { text: 'Electric Piano', category: 'Keys' },
  { text: 'Rhodes Piano', category: 'Keys' },
  { text: 'Wurlitzer', category: 'Keys' },
  { text: 'Organ', category: 'Keys' },
  { text: 'Hammond Organ', category: 'Keys' },
  { text: 'Church Organ', category: 'Keys' },
  { text: 'Synthesizer Lead', category: 'Synth' },
  { text: 'Sawtooth Synth Lead', category: 'Synth' },
  { text: 'Supersaw Chords', category: 'Synth' },
  { text: 'Square Wave Lead', category: 'Synth' },
  { text: 'Sine Wave Lead', category: 'Synth' },
  { text: 'Sparkling Arpeggios', category: 'Synth' },
  { text: 'Haunting synth arp', category: 'Synth' },
  { text: 'Synth Pad', category: 'Synth' },
  { text: 'Warm Analog Pad', category: 'Synth' },
  { text: 'Atmospheric Pad', category: 'Synth' },
  { text: 'Analog Synth', category: 'Synth' },
  { text: 'Digital Synth', category: 'Synth' },
  { text: 'FM Synthesis', category: 'Synth' },
  { text: 'Pluck Synth', category: 'Synth' },
  { text: 'Wavetable Synth', category: 'Synth' },
  { text: 'Modular Synth Textures', category: 'Synth' },
  { text: 'Harpsichord', category: 'Keys' },
  { text: 'Celeste', category: 'Keys' },
  { text: 'Mellotron strings', category: 'Keys' },

  // --- STRINGS & ORCHESTRAL ---
  { text: 'Lush Strings', category: 'Strings' },
  { text: 'Violin', category: 'Strings' },
  { text: 'Viola', category: 'Strings' },
  { text: 'Cello', category: 'Strings' },
  { text: 'Double Bass', category: 'Strings' },
  { text: 'Harp', category: 'Strings' },
  { text: 'Pizzicato Strings', category: 'Strings' },
  { text: 'String Quartet', category: 'Strings' },
  { text: 'Staccato Strings', category: 'Strings' },
  { text: 'Spiccato Strings', category: 'Strings' },
  { text: 'Orchestra Hit', category: 'Orchestral' },
  { text: 'Timpani roll', category: 'Orchestral' },
  
  // --- WIND & BRASS ---
  { text: 'Saxophone', category: 'Wind' },
  { text: 'Flute', category: 'Wind' },
  { text: 'Pan Flute', category: 'Wind' },
  { text: 'Shakuhachi Flute', category: 'Wind'},
  { text: 'Trumpet', category: 'Brass' },
  { text: 'Trombone', category: 'Brass' },
  { text: 'Clarinet', category: 'Wind' },
  { text: 'Oboe', category: 'Wind'},
  { text: 'Bassoon', category: 'Wind' },
  { text: 'Muted Trumpet', category: 'Brass' },
  { text: 'French Horn', category: 'Brass' },
  { text: 'Tuba', category: 'Brass' },

  // --- VOCALS & CHOIR ---
  { text: 'Female Vocals', category: 'Vocals' },
  { text: 'Male Vocals', category: 'Vocals' },
  { text: 'Child Vocals', category: 'Vocals' },
  { text: 'Choir', category: 'Choir' },
  { text: 'Ethereal Vocals', category: 'Vocal Style' },
  { text: 'Operatic Vocals', category: 'Vocal Style' },
  { text: 'Vocal Chops', category: 'Vocals' },
  { text: 'Breathy Female Ad-lib', category: 'Vocals' },
  { text: 'Pitched Down Vocal Hook', category: 'Vocals' },
  { text: 'Oohs and Aahs Choir', category: 'Choir' },
  { text: 'Spoken Word Phrase', category: 'Vocals' },
  { text: 'Whispering', category: 'Vocal Style' },
  { text: 'ASMR whispers', category: 'Vocal Style' },
  { text: 'Ad-libs', category: 'Vocals' },
  { text: 'Robot Voice', category: 'Vocal Style' },
  { text: 'Vocoder', category: 'Vocal Style' },
  { text: 'Gospel Choir', category: 'Choir' },
  { text: 'Gregorian Chant', category: 'Choir' },
  { text: 'Beatboxing', category: 'Vocal Style' },

  // --- MOODS & STYLES ---
  { text: 'Dark', category: 'Mood' },
  { text: 'Energetic', category: 'Mood' },
  { text: 'Ethereal', category: 'Mood' },
  { text: 'Funky', category: 'Mood' },
  { text: 'Melancholic', category: 'Mood' },
  { text: 'Introspective', category: 'Mood' },
  { text: 'Euphoric', category: 'Mood' },
  { text: 'Triumphant', category: 'Mood' },
  { text: 'Ominous', category: 'Mood' },
  { text: 'Minimalist', category: 'Style' },
  { text: 'Maximalist', category: 'Style' },
  { text: 'Uplifting', category: 'Mood' },
  { text: 'Vintage', category: 'Style' },
  { text: 'Aggressive', category: 'Mood' },
  { text: 'Dreamy', category: 'Mood' },
  { text: 'Mysterious', category: 'Mood' },
  { text: 'Nostalgic', category: 'Mood' },
  { text: 'Hypnotic', category: 'Mood' },
  { text: 'Robotic', category: 'Style' },
  { text: 'Peaceful', category: 'Mood' },
  { text: 'Epic', category: 'Mood' },
  { text: 'Romantic', category: 'Mood' },
  { text: 'Anxious', category: 'Mood' },
  { text: 'Laid-back', category: 'Mood' },
  { text: 'Gritty', category: 'Style' },
  { text: 'Futuristic', category: 'Style' },
  { text: 'Chaotic', category: 'Mood' },
  { text: '8-bit', category: 'Style' },
  { text: '16-bit', category: 'Style' },

  // --- RHYTHMS & TEMPO ---
  { text: 'Four on the floor', category: 'Rhythm' },
  { text: 'Breakbeat', category: 'Rhythm' },
  { text: 'Syncopated Rhythm', category: 'Rhythm' },
  { text: 'Polyrhythm', category: 'Rhythm' },
  { text: 'Staccato Rhythms', category: 'Rhythm' },
  { text: 'Legato Rhythms', category: 'Rhythm' },
  { text: 'Glitchy Beats', category: 'Rhythm' },
  { text: 'Latin Rhythms', category: 'Rhythm' },
  { text: 'Half-time drum beat', category: 'Rhythm' },
  { text: 'Double-time feel', category: 'Rhythm' },
  { text: 'Slow Tempo', category: 'Tempo' },
  { text: 'Medium Tempo', category: 'Tempo' },
  { text: 'Fast-paced', category: 'Tempo' },
  { text: 'Uptempo', category: 'Tempo' },
  { text: 'Driving Rhythm', category: 'Rhythm' },
  { text: 'Shuffle groove', category: 'Rhythm' },

  // --- MUSICAL ELEMENTS & FX ---
  { text: 'Melodic Motif', category: 'Melody' },
  { text: 'Counter-melody', category: 'Melody' },
  { text: 'Algorithmic melody', category: 'Melody' },
  { text: 'Sound Effects', category: 'FX' },
  { text: 'Riser FX', category: 'FX' },
  { text: 'Downlifter FX', category: 'FX' },
  { text: 'Impact FX', category: 'FX' },
  { text: 'White Noise Sweep', category: 'FX' },
  { text: 'Sub Drop', category: 'FX' },
  { text: 'Laser sounds', category: 'FX'},
  { text: 'Meme sound effect', category: 'FX' },
  { text: 'Field recording texture (city)', category: 'FX' },
  { text: 'Field recording texture (nature)', category: 'FX' },
  { text: 'Doppler effect', category: 'FX' },
  { text: 'Granular Textures', category: 'Sound Design' },
  { text: 'Granular synth cloud', category: 'Sound Design' },
  { text: 'Reverb Wash', category: 'FX' },
  { text: 'Long reverb tail', category: 'FX' },
  { text: 'Delayed Echoes', category: 'FX' },
  { text: 'Ping-pong delay', category: 'FX' },
  { text: 'Sidechain Compression', category: 'Technique' },
  { text: 'Filter Sweep', category: 'FX' },
  { text: 'Low-pass filter', category: 'FX' },
  { text: 'High-pass filter', category: 'FX' },
  { text: 'Bitcrusher', category: 'FX' },
  { text: 'Tape Rewind', category: 'FX' },
  { text: 'Time stretch effect', category: 'FX' },

  // --- ERA / DECADE ---
  { text: '50s Rock and Roll', category: 'Era/Decade'},
  { text: '60s Psychedelia', category: 'Era/Decade'},
  { text: '70s Disco Fever', category: 'Era/Decade'},
  { text: '80s Synth Pop', category: 'Era/Decade'},
  { text: '90s Grunge', category: 'Era/Decade'},
  { text: '2000s Pop Punk', category: 'Era/Decade'},
  { text: '2010s EDM', category: 'Era/Decade'},
];

function buildInitialPrompts() {
  const prompts = new Map<string, Prompt>();

  for (let i = 0; i < PROMPT_DEFINITIONS.length; i++) {
    const promptId = `prompt-${i}`;
    const prompt = PROMPT_DEFINITIONS[i];
    prompts.set(promptId, {
      promptId,
      text: prompt.text,
      weight: 0,
      cc: i,
      color: PALETTE[i % PALETTE.length],
      category: prompt.category,
    });
  }

  return prompts;
}

main();