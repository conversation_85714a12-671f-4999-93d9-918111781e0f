html {
  height: 100%;
  margin: 0;
  background-color: #0A0A0A;
}

body {
  height: 100%;
  margin: 0;
  background-color: transparent;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  overflow: hidden;
  color: #f0f0f0;
}

body.dragging {
  cursor: ns-resize;
}
body.dragging * {
  user-select: none;
  pointer-events: none;
}

/* Custom Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}
::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 10px;
}
::-webkit-scrollbar-thumb:hover {
  background-color: #666;
}

/* Neon Bloom Effect */
body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1; /* Place it behind all content */
  box-shadow: var(--bloom-shadow, inset 0 0 0vw 0vw #000);
  opacity: var(--bloom-opacity, 0);
  transition: opacity 0.4s ease-out, box-shadow 0.05s ease-out;
}