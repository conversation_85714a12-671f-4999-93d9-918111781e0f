/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import { css, html, LitElement } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { styleMap } from 'lit/directives/style-map.js';
import { classMap } from 'lit/directives/class-map.js';
import { GoogleGenAI, Type } from '@google/genai';

import { throttle } from '../utils/throttle';

import './PromptController';
import './PlayPauseButton';
import './FxPad';
import type { PlaybackState, Prompt, SongPlan, SongSection, LyricEvent } from '../types';
import { MidiDispatcher } from '../utils/MidiDispatcher';
import { LiveMusicHelper } from '../utils/LiveMusicHelper';

const DEEPGRAM_API_KEY = '****************************************';
const DEEPGRAM_VOICES = [
    // Female
    { id: 'aura-asteria-en', name: 'Narrative Female' },
    { id: 'aura-luna-en', name: 'Conversational Female' },
    { id: 'aura-stella-en', name: 'Energetic Female' },
    { id: 'aura-athena-en', name: 'Soothing Female' },
    { id: 'aura-hera-en', name: 'Playful Female' },
    // Male
    { id: 'aura-helios-en', name: 'Narrative Male' },
    { id: 'aura-hermes-en', name: 'Conversational Male' },
    { id: 'aura-orion-en', name: 'Energetic Male' },
    { id: 'aura-zeus-en', name: 'Authoritative Male' },
    { id: 'aura-ares-en', name: 'Announcer Male' },
    { id: 'aura-perseus-en', name: 'Casual Male' },
    { id: 'aura-apollo-en', name: 'Deep Male' },
];

/** The main UI component, inspired by DAW layouts. */
@customElement('prompt-dj-midi')
export class PromptDjMidi extends LitElement {
  static override styles = css`
    :host {
      height: 100%;
      width: 100%;
      display: block; /* Changed to block for the inner container to control flex */
      box-sizing: border-box;
      background: transparent; /* Changed for visualizer */
      color: #f0f0f0;
      font-family: 'Inter', sans-serif;
    }
    .responsive-container {
      display: flex;
      width: 100%;
      height: 100%;
      position: relative;
      overflow-x: hidden;
      background: transparent;
    }
    #sidebar {
      width: 320px;
      min-width: 320px;
      height: 100%;
      background: rgba(17, 17, 17, 0.9);
      backdrop-filter: blur(10px);
      display: flex;
      flex-direction: column;
      overflow-y: hidden;
      border-right: 1px solid rgba(255, 255, 255, 0.1);
      transition: transform 0.3s ease-in-out;
      z-index: 50;
    }
    #sidebar-header {
      padding: 20px 20px 15px 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      flex-shrink: 0;
    }
    #sidebar-header h2 {
      color: white;
      margin: 0 0 15px 0;
      font-size: 20px;
      font-weight: 500;
    }
    #search-input {
      width: 100%;
      padding: 8px 12px;
      border-radius: 6px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      background: #1C1C1C;
      color: white;
      font-family: inherit;
      font-size: 14px;
      box-sizing: border-box;
      transition: border-color 0.2s ease;
    }
    #search-input:focus {
      outline: none;
      border-color: #F9B200;
    }
    #asset-list {
      flex-grow: 1;
      padding: 10px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      overflow-y: auto;
    }
    .asset-item {
      display: flex;
      align-items: center;
      gap: 5px;
      background: #1C1C1C;
      border-radius: 6px;
      border: 1px solid transparent;
      padding: 0 8px 0 15px;
      transition: all 0.2s ease;
    }
    .asset-item:hover {
      border-color: rgba(255, 255, 255, 0.2);
      background: #282828;
    }
    .asset-info {
      flex-grow: 1;
      display: flex;
      align-items: center;
      gap: 12px;
      font-family: inherit;
      font-size: 14px;
      color: #f0f0f0;
      user-select: none;
      overflow: hidden;
      white-space: nowrap;
    }
    .asset-info span {
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .asset-actions {
        display: flex;
        flex-shrink: 0;
    }
    .preview-btn, .add-btn {
      width: 36px;
      height: 36px;
      flex-shrink: 0;
      background: transparent;
      border: none;
      color: #999;
      cursor: pointer;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }
    .preview-btn:hover, .add-btn:hover {
      background: #3c3c3c;
      color: white;
    }
    .preview-btn.previewing {
      color: #F9B200;
    }
    .preview-btn svg {
      width: 20px;
      height: 20px;
    }
    .add-btn {
        font-size: 24px;
        font-weight: 300;
    }
    .asset-color-swatch {
      width: 14px;
      height: 14px;
      border-radius: 4px;
      flex-shrink: 0;
    }
    #main-content {
      flex-grow: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
      overflow: hidden;
      background: transparent;
    }
    #toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 20px;
      background: rgba(17, 17, 17, 0.8);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      position: relative;
      z-index: 2;
      gap: 15px;
      flex-shrink: 0;
      height: 80px;
      box-sizing: border-box;
    }
    #transport-controls {
      display: flex;
      align-items: center;
      gap: 20px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
    #midi-controls, #record-controls {
      display: flex;
      gap: 10px;
      align-items: center;
    }
    #sidebar-toggle {
      display: none;
      background: transparent;
      border: none;
      color: #f0f0f0;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
    }
    #sidebar-toggle svg {
      width: 100%;
      height: 100%;
    }
    #main-actions {
      display: flex;
      gap: 10px;
      align-items: center;
    }
    #track-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;
      padding: 20px;
      overflow-y: auto;
      position: relative;
      z-index: 1;
    }
    .empty-state {
      color: #aaa;
      background: rgba(0,0,0,0.4);
      padding: 20px;
      border-radius: 8px;
      text-align: center;
      margin: auto;
      font-size: 1.1em;
      line-height: 1.6;
    }
    play-pause-button {
      width: 60px;
      height: 60px;
    }
    button {
      font: inherit;
      font-weight: 500;
      cursor: pointer;
      color: #f0f0f0;
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 6px;
      user-select: none;
      padding: 6px 12px;
      transition: all 0.2s ease;
    }
    button:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.5);
    }
    button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: transparent;
    }

    button.active {
      background-color: #F9B200;
      color: #0A0A0A;
      border-color: #F9B200;
    }
    button.active:hover {
      background-color: #fbc13a;
    }

    button.record-active {
      background-color: #e74c3c;
      color: white;
      border-color: #e74c3c;
      animation: pulse 1.5s infinite;
    }
    #download-btn {
      background: #F9B200;
      color: #0A0A0A;
      border-color: #F9B200;
      font-weight: bold;
      text-decoration: none;
      display: inline-block;
    }
    #download-btn:hover {
      background: #fbc13a;
    }
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
      100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
    }
    select {
      font: inherit;
      padding: 5px 8px;
      background: #1C1C1C;
      color: #f0f0f0;
      border-radius: 6px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      outline: none;
      cursor: pointer;
    }
    #complexity-control {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      color: #ccc;
    }
    #complexity-control label {
      font-size: 10px;
      font-weight: bold;
      letter-spacing: 1px;
      user-select: none;
    }
    #complexity-slider {
      -webkit-appearance: none;
      appearance: none;
      width: 150px;
      height: 5px;
      background: #0A0A0A;
      outline: none;
      border-radius: 3px;
      cursor: pointer;
    }
    #complexity-slider::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 15px;
      height: 15px;
      background: #f0f0f0;
      cursor: pointer;
      border-radius: 50%;
      border: none;
    }
    #complexity-slider::-moz-range-thumb {
      width: 15px;
      height: 15px;
      background: #f0f0f0;
      cursor: pointer;
      border-radius: 50%;
      border: none;
    }

    /* Floating Window Styles */
    .floating-window {
      position: absolute;
      bottom: 20px;
      background: rgba(28, 28, 28, 0.9);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.5);
      z-index: 10;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    }
    #ai-tool-window {
      right: 20px;
      width: 380px;
    }
    #audio-fx-window {
      left: 20px;
      width: 300px;
      transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1), width 0.3s ease, height 0.3s ease;
    }
    #audio-fx-window.pad-view-active {
      width: 280px;
      height: 360px;
    }
    .floating-window.minimized {
      height: 40px !important;
      width: 220px !important;
    }
    #audio-fx-window.minimized {
        width: 180px !important;
    }
    .window-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      height: 40px;
      background: #282828;
      cursor: pointer;
      flex-shrink: 0;
      user-select: none;
    }
    .window-header h3 {
      color: white;
      font-size: 14px;
      font-weight: 500;
      margin: 0;
    }
    .window-header button {
      background: none;
      border: none;
      color: #ccc;
      font-size: 20px;
      padding: 0;
      width: 24px;
      height: 24px;
      line-height: 24px;
    }
    .window-header button:hover {
      color: white;
    }
    .window-body {
      padding: 0;
      display: flex;
      flex-direction: column;
      gap: 0;
      flex-grow: 1;
      overflow: hidden;
    }
    .ai-tabs {
      display: flex;
      flex-shrink: 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    .ai-tab-btn {
      flex: 1;
      padding: 12px;
      background: transparent;
      border: none;
      color: #aaa;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      border-bottom: 2px solid transparent;
      margin-bottom: -1px;
    }
    .ai-tab-btn:hover {
      background: rgba(255,255,255,0.05);
      color: white;
    }
    .ai-tab-btn.active {
      color: white;
      border-bottom-color: #F9B200;
    }
    .ai-tab-content {
      padding: 15px;
      display: flex;
      flex-direction: column;
      gap: 15px;
      flex-grow: 1;
    }
    .ai-setting {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    .ai-setting label {
        font-size: 13px;
        color: #aaa;
        font-weight: 500;
    }
    .ai-textarea {
      width: 100%;
      box-sizing: border-box;
      background: #0A0A0A;
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      border-radius: 4px;
      padding: 10px;
      resize: none;
      font-family: inherit;
    }
    #songwriter-prompt-input {
      height: 80px;
    }
    #beat-maker-prompt-input {
      height: 120px;
    }
    .ai-button {
      border: none;
      color: white;
      padding: 12px;
      border-radius: 6px;
      font-size: 15px;
      font-weight: 600;
      margin-top: 5px;
    }
    .ai-button:hover:not(:disabled) {
      filter: brightness(1.1);
    }
    .ai-button:disabled {
      background: #444 !important;
      cursor: wait;
      color: #888;
    }
    #songwriter-generate-btn {
      background: #16a085;
    }
    #beat-maker-generate-btn {
      background: #8e44ad;
    }
    #vocalist-select {
        width: 100%;
    }
    .generation-view {
      margin: auto;
      text-align: center;
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
    }
    .generation-view h2 {
      font-size: 22px;
      color: #f0f0f0;
      margin: 0;
    }
    .generation-view h3 {
      font-size: 18px;
      color: #F9B200;
      margin: 0;
      font-style: italic;
      font-weight: 500;
    }
    .lyrics-display {
      background: rgba(0,0,0,0.2);
      border-radius: 8px;
      padding: 20px;
      white-space: pre-wrap;
      font-family: 'Georgia', serif;
      font-size: 16px;
      line-height: 1.7;
      color: #ddd;
      max-width: 80%;
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid rgba(255,255,255,0.1);
    }

    #sidebar-backdrop {
        display: none;
    }

    /* FX Window Styles */
    .fx-content {
        padding: 15px;
        display: flex;
        flex-direction: column;
        gap: 18px;
        flex-grow: 1;
    }
    .fx-setting {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .fx-setting label {
      display: flex;
      justify-content: space-between;
      font-size: 13px;
      color: #aaa;
      font-weight: 500;
    }
    .fx-setting label span:last-child {
      font-family: monospace;
      color: #f0f0f0;
      background: rgba(0,0,0,0.2);
      padding: 1px 4px;
      border-radius: 3px;
    }
    .fx-slider {
      -webkit-appearance: none;
      appearance: none;
      width: 100%;
      height: 6px;
      background: #0A0A0A;
      outline: none;
      border-radius: 3px;
      cursor: pointer;
      border: 1px solid rgba(255, 255, 255, 0.2);
      margin: 0;
    }
    .fx-slider::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 16px;
      height: 16px;
      background: #f0f0f0;
      cursor: pointer;
      border-radius: 50%;
      border: none;
      transition: transform 0.1s ease;
    }
    .fx-slider:active::-webkit-slider-thumb {
      transform: scale(1.2);
    }
    .fx-slider::-moz-range-thumb {
      width: 16px;
      height: 16px;
      background: #f0f0f0;
      cursor: pointer;
      border-radius: 50%;
      border: none;
    }
    .fx-buttons {
      display: flex;
      gap: 10px;
      margin-top: 5px;
    }
    .fx-buttons button {
      flex: 1;
    }

    .fx-pad-wrapper {
        flex-grow: 1;
        display: flex;
        padding: 15px;
    }

    .fx-pad-wrapper fx-pad {
        flex-grow: 1;
    }


    /* Responsive Design */
    @media (max-width: 768px) {
      #sidebar-toggle {
        display: block;
      }
      
      #sidebar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 280px;
        min-width: 280px;
        z-index: 100;
        transform: translateX(-100%);
        box-shadow: 4px 0px 15px rgba(0,0,0,0.4);
      }
      
      .sidebar-open #sidebar {
        transform: translateX(0);
      }
      
      .sidebar-open #sidebar-backdrop {
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 99;
      }
      
      #main-content {
        width: 100%;
      }
      
      #toolbar {
        flex-wrap: wrap;
        height: auto;
        justify-content: space-between;
        gap: 15px;
      }
      
      #transport-controls {
        position: static;
        transform: none;
        order: -1;
        width: 100%;
        justify-content: center;
        padding-bottom: 15px;
        margin-bottom: 15px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
      }

      #complexity-control {
          display: none;
      }

      play-pause-button {
          width: 50px;
          height: 50px;
      }
      
      #midi-controls { order: 0; }
      #main-actions { order: 1; }

      #track-area {
        padding: 15px;
        gap: 15px;
      }

      /* Floating Window Mobile Layout */
      .floating-window {
        /* Default state is maximized on mobile */
        width: calc(100% - 20px);
        left: 10px;
        right: 10px;
        bottom: 10px;
      }

      /* Both windows are visible now */
      #audio-fx-window {
          display: flex;
      }

      #ai-tool-window.minimized {
        width: 180px !important;
        left: auto;
        right: 10px;
      }
      #audio-fx-window.minimized {
        width: 160px !important;
        left: 10px;
        right: auto;
      }
    }
    @media (max-width: 480px) {
        #midi-controls {
            flex-grow: 1;
        }
        #midi-controls select {
            width: 100%;
            max-width: 150px;
        }
        #main-actions {
            flex-grow: 1;
            justify-content: flex-end;
        }
        .asset-item {
            padding: 0 4px 0 12px;
        }
        .preview-btn, .add-btn {
            width: 32px;
            height: 32px;
        }
    }
  `;

  private allPrompts: Map<string, Prompt>;
  private midiDispatcher: MidiDispatcher;
  private ai: GoogleGenAI;
  private availablePromptsForAI: { text: string, category: string }[];
  private liveMusicHelper: LiveMusicHelper;
  private textToIdMap = new Map<string, string>();
  private generatedVocalTracks = new Map<string, HTMLAudioElement>();


  @state() private activePromptIds = new Set<string>();
  @property({ type: Boolean }) private showMidi = false;
  @property({ type: String }) public playbackState: PlaybackState = 'stopped';
  @state() public audioLevel = 0;
  @state() private midiInputIds: string[] = [];
  @state() private activeMidiInputId: string | null = null;
  @state() private complexity = 0.5;
  @state() private searchQuery = '';
  @state() public isRecording = false;
  @state() private downloadUrl: string | null = null;
  @state() private downloadFilename = 'prompt-dj-beat.webm';
  
  @state() private previewingPromptId: string | null = null;
  @state() private wasPlayingBeforePreview = false;
  
  // AI Tools Window State
  @state() private isAiToolWindowMinimized = true;
  @state() private activeAiToolTab: 'songwriter' | 'beat-maker' = 'songwriter';

  // Audio Effects Window State
  @state() private audioFxState = { 
    pitch: 1.0, 
    pan: 0, 
    isMono: false, 
    is8D: false, 
    isWindowMinimized: true 
  };
  @state() private activeFxTab: 'controls' | 'pad' = 'controls';
  
  // AI Songwriter State
  @state() private songwriterPromptText = '';
  @state() private isGeneratingSong = false;
  @state() private songGenerationStatus = '';
  @state() private currentLyrics: LyricEvent[] = [];
  @state() private generatedSongTitle = '';
  @state() private selectedVocalist = DEEPGRAM_VOICES[0].id;

  // AI Beat Maker State
  @state() private beatMakerPromptText = '';
  @state() private isGeneratingBeat = false;
  
  @state() private isSidebarOpen = false;

  @property({ type: Object })
  private filteredPrompts = new Set<string>();

  constructor(
    initialPrompts: Map<string, Prompt>,
    ai: GoogleGenAI,
    availablePromptsForAI: { text: string, category: string }[],
    liveMusicHelper: LiveMusicHelper
  ) {
    super();
    this.allPrompts = initialPrompts;
    this.midiDispatcher = new MidiDispatcher();
    this.ai = ai;
    this.availablePromptsForAI = availablePromptsForAI;
    this.liveMusicHelper = liveMusicHelper;

    for (const prompt of this.allPrompts.values()) {
      this.textToIdMap.set(prompt.text, prompt.promptId);
    }
  }
  
  public getActivePromptColors(): string[] {
    const activePrompts = [...this.allPrompts.values()].filter(p => this.activePromptIds.has(p.promptId) && p.weight > 0);
    return activePrompts.map(p => p.color);
  }
  
  protected updated(changedProperties: Map<string, unknown>) {
    // On small screens, auto-close the sidebar when user starts interaction
    if (window.innerWidth <= 768 && this.isSidebarOpen) {
      if ((changedProperties.has('playbackState') && this.playbackState === 'playing') ||
          (changedProperties.has('isGeneratingSong') && this.isGeneratingSong) ||
          (changedProperties.has('isGeneratingBeat') && this.isGeneratingBeat)) {
        this.isSidebarOpen = false;
      }
    }
  }

  private dispatchPromptsChanged() {
    const promptsToSend = new Map<string, Prompt>();
    
    // If in preview mode, send only the preview prompt
    if (this.previewingPromptId) {
      const prompt = this.allPrompts.get(this.previewingPromptId);
      if (prompt) {
        const previewPromptClone = { ...prompt, weight: 1.0 };
        promptsToSend.set(this.previewingPromptId, previewPromptClone);
      }
    } else {
       // Add only active prompts
      for (const promptId of this.activePromptIds) {
        const prompt = this.allPrompts.get(promptId);
        if (prompt && prompt.weight > 0) {
          promptsToSend.set(promptId, prompt);
        }
      }
      // Add a special prompt for complexity, with its weight based on the slider.
      if (this.complexity > 0.05) { // Using a small deadzone
        const complexityPrompt: Prompt = {
          promptId: 'internal-complexity-prompt',
          text: 'intricate and layered instrumentation, complex harmonies, detailed patterns',
          weight: this.complexity * 0.75, // Scale it a bit, max weight will be 0.75
          cc: -1, // Not associated with a MIDI CC
          color: 'transparent', // Not visible in the UI
          category: 'Complexity',
        };
        promptsToSend.set(complexityPrompt.promptId, complexityPrompt);
      }
    }
    
    this.dispatchEvent(
      new CustomEvent('prompts-changed', { detail: promptsToSend }),
    );
  }

  private handleComplexityChange(e: Event) {
    this.complexity = (e.target as HTMLInputElement).valueAsNumber;
    this.dispatchPromptsChanged();
  }

  private handlePromptChanged(e: CustomEvent<Prompt>) {
    const { promptId, text, weight, cc, color, category } = e.detail;
    const prompt = this.allPrompts.get(promptId);
    if (!prompt) return;

    prompt.text = text;
    prompt.weight = weight;
    prompt.cc = cc;
    prompt.color = color;
    prompt.category = category;
    
    this.allPrompts.set(promptId, prompt);
    this.dispatchPromptsChanged();
    this.requestUpdate('allPrompts');
  }

  private addPrompt(promptId: string) {
    // Cannot add prompts while one is being previewed.
    if (this.previewingPromptId) return;
    const prompt = this.allPrompts.get(promptId);
    if(prompt) {
      prompt.weight = 0.5; // Set a default weight
      this.allPrompts.set(promptId, prompt);
    }
    this.activePromptIds.add(promptId);
    this.dispatchPromptsChanged();
    this.requestUpdate('activePromptIds');

    if (window.innerWidth <= 768) {
      this.isSidebarOpen = false;
    }
  }

  private handlePromptRemoved(e: CustomEvent<{promptId: string}>) {
    const {promptId} = e.detail;
    const prompt = this.allPrompts.get(promptId);
    if (prompt) {
      prompt.weight = 0; // Reset weight
      this.allPrompts.set(promptId, prompt);
    }
    this.activePromptIds.delete(promptId);
    this.dispatchPromptsChanged();
    this.requestUpdate('activePromptIds');
  }

  private toggleShowMidi() {
    return this.setShowMidi(!this.showMidi);
  }

  public async setShowMidi(show: boolean) {
    this.showMidi = show;
    if (!this.showMidi) return;
    try {
      const inputIds = await this.midiDispatcher.getMidiAccess();
      this.midiInputIds = inputIds;
      this.activeMidiInputId = this.midiDispatcher.activeMidiInputId;
    } catch (e: any) {
      this.dispatchEvent(new CustomEvent('error', {detail: e.message}));
    }
  }

  private handleMidiInputChange(event: Event) {
    const selectElement = event.target as HTMLSelectElement;
    const newMidiId = selectElement.value;
    this.activeMidiInputId = newMidiId;
    this.midiDispatcher.activeMidiInputId = newMidiId;
  }
  
  private handleSearchInput(e: Event) {
    this.searchQuery = (e.target as HTMLInputElement).value;
  }

  private toggleRecording() {
    this.dispatchEvent(new CustomEvent('toggle-recording'));
    // If a download is available, this new action clears it
    if (this.downloadUrl) {
      this.clearDownload();
    }
  }

  public setDownload(blob: Blob, prompts: string[]) {
    if (this.downloadUrl) {
      URL.revokeObjectURL(this.downloadUrl);
    }
    this.downloadUrl = URL.createObjectURL(blob);
    const usedPrompts = prompts.join('-').replace(/[^a-z0-9-_]/gi, '_');
    this.downloadFilename = `${usedPrompts || 'prompt-dj-beat'}.webm`;
  }
  
  public clearDownload() {
    if (this.downloadUrl) {
      URL.revokeObjectURL(this.downloadUrl);
      this.downloadUrl = null;
    }
  }

  private async playPause() {
    if (this.isGeneratingSong) return;

    if (this.previewingPromptId) {
      this.previewingPromptId = null;
      this.wasPlayingBeforePreview = false;
      await this.liveMusicHelper.stop();
    } else {
      await this.liveMusicHelper.playPause();
    }
    this.clearDownload();
  }

  public addFilteredPrompt(prompt: string) {
    this.filteredPrompts = new Set([...this.filteredPrompts, prompt]);
  }

  private async handlePreviewToggle(promptIdToPreview: string) {
    if (this.isGeneratingSong) return;
    const isCurrentlyPreviewingThis = this.previewingPromptId === promptIdToPreview;
    
    // If stopping the current preview
    if (isCurrentlyPreviewingThis) {
      this.previewingPromptId = null;
      await this.liveMusicHelper.pause();
      if (this.wasPlayingBeforePreview) {
        this.dispatchPromptsChanged(); // Restore original prompts
        await this.liveMusicHelper.play();
      }
      this.wasPlayingBeforePreview = false;
      return;
    }
    
    // If starting a new preview
    if (this.previewingPromptId === null) {
      this.wasPlayingBeforePreview = this.playbackState === 'playing' || this.playbackState === 'loading';
      if (this.wasPlayingBeforePreview) {
        await this.liveMusicHelper.pause();
      }
    }
    
    this.previewingPromptId = promptIdToPreview;
    this.dispatchPromptsChanged();
    await this.liveMusicHelper.play();
  }

  private toggleAiWindow() {
    this.isAiToolWindowMinimized = !this.isAiToolWindowMinimized;
    if (window.innerWidth <= 768 && !this.isAiToolWindowMinimized) {
        // If we are maximizing the AI window on mobile, minimize the FX window
        this.audioFxState = { ...this.audioFxState, isWindowMinimized: true };
    }
  }

  private toggleFxWindow() {
    const currentMinimizedState = this.audioFxState.isWindowMinimized;
    this.audioFxState = { ...this.audioFxState, isWindowMinimized: !currentMinimizedState };
    if (window.innerWidth <= 768 && !this.audioFxState.isWindowMinimized) {
        // If we are maximizing the FX window on mobile, minimize the AI window
        this.isAiToolWindowMinimized = true;
    }
  }

  private handleFxChange(property: 'pitch' | 'pan', e: Event) {
    const value = (e.target as HTMLInputElement).valueAsNumber;
    this.audioFxState = { ...this.audioFxState, [property]: value };
    if (property === 'pitch') {
        this.liveMusicHelper.setPlaybackRate(value);
    } else if (property === 'pan') {
        this.liveMusicHelper.setPan(value);
    }
  }

  private handleFxToggle(property: 'isMono' | 'is8D') {
      const newValue = !this.audioFxState[property];
      
      const pan = property === 'is8D' && newValue ? 0 : this.audioFxState.pan;
      this.audioFxState = { ...this.audioFxState, [property]: newValue, pan };

      if (property === 'isMono') {
          this.liveMusicHelper.setMono(newValue);
      } else if (property === 'is8D') {
          this.liveMusicHelper.toggle8D(newValue);
          // When 8D is turned on, pan is controlled automatically.
          // When turned off, it returns to the slider's value.
          this.liveMusicHelper.setPan(pan);
      }
  }
  
  private handlePerfFxChange(e: CustomEvent<{x: number, y: number}>) {
    const { x, y } = e.detail;
    this.liveMusicHelper.setFilter(x);
    this.liveMusicHelper.setDelay(y);
  }

  private buildCategorizedPromptList(): string {
    const categories = new Map<string, string[]>();
    for (const prompt of this.availablePromptsForAI) {
      if (!categories.has(prompt.category)) {
        categories.set(prompt.category, []);
      }
      categories.get(prompt.category)!.push(prompt.text);
    }

    let formattedList = "Here are the available instrumental prompts, grouped by category:\n\n";
    for (const [category, prompts] of categories.entries()) {
      formattedList += `**${category}**\n`;
      formattedList += prompts.map(p => `- ${p}`).join('\n');
      formattedList += '\n\n';
    }
    return formattedList;
  }
  
  private assembleSsmlForSection(lyrics: LyricEvent[], bpm: number): string {
    if (!lyrics || lyrics.length === 0) {
        return '';
    }

    const secondsPerBeat = 60.0 / bpm;
    let ssmlContent = '';

    for (let i = 0; i < lyrics.length; i++) {
        const currentWord = lyrics[i];
        
        let wordContent = currentWord.word;
        if (currentWord.annotation) {
            let prosodyAttrs = '';
            switch (currentWord.annotation) {
                case 'stressed':
                    prosodyAttrs = `volume="loud"`;
                    break;
                case 'whispered':
                    prosodyAttrs = `volume="x-soft"`;
                    break;
                case 'elongated':
                    prosodyAttrs = `rate="slow"`;
                    break;
                case 'punchy':
                    prosodyAttrs = `rate="fast"`;
                    break;
                case 'rising':
                    prosodyAttrs = `pitch="high"`;
                    break;
                case 'falling':
                    prosodyAttrs = `pitch="low"`;
                    break;
            }
            if (prosodyAttrs) {
                wordContent = `<prosody ${prosodyAttrs}>${currentWord.word}</prosody>`;
            }
        }
        
        ssmlContent += `${wordContent} `;

        if (i < lyrics.length - 1) {
            const nextWord = lyrics[i + 1];
            const beatDifference = nextWord.beat - currentWord.beat;
            if (beatDifference > 0) {
                const breakTimeMs = Math.round(beatDifference * secondsPerBeat * 1000);
                if (breakTimeMs > 10) {
                    ssmlContent += `<break time="${breakTimeMs}ms"/>`;
                }
            }
        }
    }

    return `<speak>${ssmlContent.trim()}</speak>`;
  }

  private async generateVocalTracks(songPlan: SongPlan, voice: string): Promise<Map<string, HTMLAudioElement>> {
    const vocalTracks = new Map<string, HTMLAudioElement>();
    
    for (const [index, section] of songPlan.sections.entries()) {
        if (!section.lyrics || section.lyrics.length === 0) {
            continue;
        }

        this.songGenerationStatus = `Generating vocals for ${section.name}...`;

        try {
            const ssmlText = this.assembleSsmlForSection(section.lyrics, songPlan.bpm);
            if (!ssmlText) continue;
            
            const response = await fetch(`https://api.deepgram.com/v1/speak?model=${voice}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Token ${DEEPGRAM_API_KEY}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ text: ssmlText })
            });

            if (!response.ok) {
                const errorBody = await response.json();
                console.error(`Deepgram API error for section "${section.name}": `, errorBody);
                this.dispatchEvent(new CustomEvent('error', {detail: `Deepgram API error for section "${section.name}": \n${errorBody.err_msg}`}));
                continue;
            }

            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const audio = new Audio(url);
            vocalTracks.set(`${section.name}-${index}`, audio);
        } catch (error) {
            console.error(`Failed to fetch vocal track for section "${section.name}":`, error);
            this.dispatchEvent(new CustomEvent('error', {detail: `Failed to generate vocal for section "${section.name}".`}));
        }
    }
    return vocalTracks;
  }
  
  private async generateBeat() {
    if (!this.beatMakerPromptText.trim() || this.isGeneratingBeat) return;

    this.isGeneratingBeat = true;
    
    const schema = {
      type: Type.OBJECT,
      properties: {
        prompts: {
          type: Type.ARRAY,
          description: 'A list of musical prompts and their weights to create the beat.',
          items: {
            type: Type.OBJECT,
            properties: {
              promptName: { type: Type.STRING, description: 'The exact name of a prompt from the provided list.' },
              weight: { type: Type.NUMBER, description: 'The weight for this prompt (0.1 to 2.0).' }
            },
            required: ['promptName', 'weight']
          }
        }
      },
      required: ['prompts']
    };

    const systemInstruction = `You are an expert AI Beat Maker. Your task is to interpret a user's description of a beat and translate it into a thoughtful combination of musical prompts from a provided list.

1.  **Analyze the User's Request:** Deconstruct the user's description to understand the desired genre, mood, instruments, and rhythm.
2.  **Select Prompts:** Choose a set of 3 to 8 prompts from the provided list that best combine to create the requested beat. Pay close attention to the prompt categories provided as they are a guide to building a good foundation.
3.  **Build a Balanced Beat:** Your primary goal is to create a musically coherent and balanced beat. Unless the user's request is very specific (e.g., "just a drum loop"), you MUST select a combination of prompts that includes:
    *   **A core rhythmic element** (e.g., from a 'Drums' or 'Hip Hop Drums' category).
    *   **A bass element** (e.g., from a 'Bass' or 'Hip Hop Bass' category).
    *   **At least one melodic or harmonic element** (e.g., from 'Keys', 'Synth', 'Melodic', 'Guitar', etc.).
4.  **Assign Weights:** For each selected prompt, assign a 'weight' between 0.1 and 2.0. The weight determines the prominence of that element in the mix. Use a range of weights to create a dynamic mix.
5.  **Final JSON Output:** Provide your final output as a single JSON object matching the required schema: \`{ "prompts": [{ "promptName": "...", "weight": ... }] }\`. Ensure every \`promptName\` exists exactly as it appears in the provided list. Do not invent new prompts.`;
    
    const categorizedList = this.buildCategorizedPromptList();

    try {
      const contents = [
        {text: systemInstruction},
        {text: `User's beat idea: "${this.beatMakerPromptText}"`},
        {text: categorizedList},
      ];

      const response = await this.ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: contents,
        config: {
          responseMimeType: 'application/json',
          responseSchema: schema,
        }
      });
      
      const beatPlan = JSON.parse(response.text.trim());
      
      // Clear existing tracks
      this.activePromptIds.forEach(id => {
        const prompt = this.allPrompts.get(id);
        if (prompt) prompt.weight = 0;
      });
      this.activePromptIds.clear();
      
      const newActiveIds = new Set<string>();
      if (beatPlan.prompts && Array.isArray(beatPlan.prompts)) {
        for (const p of beatPlan.prompts) {
            const promptId = this.textToIdMap.get(p.promptName);
            if (promptId) {
                newActiveIds.add(promptId);
                const prompt = this.allPrompts.get(promptId);
                if (prompt) {
                  prompt.weight = Math.max(0, Math.min(2, p.weight || 0.5));
                }
            }
        }
      }
      this.activePromptIds = newActiveIds;
      this.dispatchPromptsChanged();
      this.requestUpdate();

    } catch(err) {
      console.error(err);
      this.dispatchEvent(new CustomEvent('error', {detail: 'Failed to generate beat. The AI might be busy, or the request was too complex. Please try again.'}));
    } finally {
      this.isGeneratingBeat = false;
    }
  }

  private async generateFullSong() {
    if (!this.songwriterPromptText.trim() || this.isGeneratingSong) return;
    
    this.isGeneratingSong = true;
    this.songGenerationStatus = 'Writing your song...';
    this.currentLyrics = [];
    this.generatedSongTitle = '';
    this.downloadUrl = null;
    this.activePromptIds.clear();
    this.dispatchPromptsChanged();
    this.generatedVocalTracks.clear();

    const schema = {
      type: Type.OBJECT,
      properties: {
        songTitle: { type: Type.STRING, description: 'A creative title for the song inspired by the user\'s prompt.' },
        bpm: { type: Type.NUMBER, description: 'The beats per minute for the song, e.g., 120.' },
        sections: {
          type: Type.ARRAY,
          description: 'The different sections of the song.',
          items: {
            type: Type.OBJECT,
            properties: {
              name: { type: Type.STRING, description: 'The name of the section (e.g., "Intro", "Verse 1", "Chorus", "Bridge", "Outro").' },
              durationInBars: { type: Type.NUMBER, description: 'The length of this section in bars (e.g., 8, 16).' },
              lyrics: {
                type: Type.ARRAY,
                description: 'A beat map of the lyrics. For each word, specify the beat it should start on and annotations for vocal performance.',
                items: {
                  type: Type.OBJECT,
                  properties: {
                    word: { type: Type.STRING, description: 'A single word of the lyric.' },
                    beat: { type: Type.NUMBER, description: 'The beat this word should start on (e.g., 1, 1.5, 2, 2.75). Beat 1 is the downbeat.' },
                    annotation: { type: Type.STRING, description: 'Optional. Vocal performance cue (e.g., "stressed", "whispered", "elongated", "punchy", "rising", "falling").' }
                  },
                  required: ['word', 'beat']
                }
              },
              prompts: {
                type: Type.ARRAY,
                description: 'The musical prompts for this section.',
                items: {
                  type: Type.OBJECT,
                  properties: {
                    promptName: { type: Type.STRING, description: 'The exact name of a prompt from the provided list.' },
                    weight: { type: Type.NUMBER, description: 'The weight for this prompt (0.1 to 2.0).' }
                  },
                  required: ['promptName', 'weight']
                }
              }
            },
            required: ['name', 'durationInBars', 'lyrics', 'prompts']
          }
        }
      },
      required: ['songTitle', 'bpm', 'sections']
    };

    const categorizedList = this.buildCategorizedPromptList();
    const systemInstruction = `You are a world-class AI Music Producer and Rhythmic Poet. Your goal is to write a full song based on a user's idea, complete with lyrics, instrumental arrangement, and a highly detailed vocal performance map.

1.  **Analyze User's Request:** Deconstruct the user's prompt for genre, lyrical themes, and mood.

2.  **Compose Music & Write Lyrics:**
    *   Create a standard song structure (e.g., Intro, Verse, Chorus, Bridge, Outro) to create a song roughly 2-3 minutes long.
    *   Write original, creative lyrics that fit the theme and genre.
    *   Design a backing track for each section using the provided list of instrumental prompts.
    *   **CRITICAL RULE: The instrumental backing track MUST NOT contain any human voice-like sounds.** This includes prompts like "Sampled Vocals", "Scat Vocals", "Rap Ad-libs", "Producer tag", etc. The user's voice will be generated separately, so the backing track must be purely instrumental to avoid clashes.

3.  **Create a Rhythmic & Expressive Vocal "Beat Map":**
    *   This is the most important part. To make the vocal sound human and musical (not robotic), you must create a "beat map" for every section with lyrics.
    *   For **every single word**, define:
        a. **The precise beat** it lands on within a 4-beat measure. **Heavily use syncopation** (off-beats like 1.5, 2.75, 3.25) to create a natural, groovy rhythm. Avoid placing every word on a whole or half beat.
        b. **A vocal performance annotation** (optional, but highly encouraged) to add emotion and dynamics.
    *   **Available Annotations:**
        *   \`"stressed"\`: The word is delivered with more force.
        *   \`"whispered"\`: The word is delivered softly and breathily.
        *   \`"elongated"\`: The word is stretched out, delivered more slowly.
        *   \`"punchy"\`: The word is short, sharp, and delivered quickly.
        *   \`"rising"\`: The word has a rising pitch (like at the end of a question).
        *   \`"falling"\`: The word has a falling pitch (like at the end of a statement).
    *   **Example of an excellent, syncopated, and expressive beat map:**
        \`\`\`json
        "lyrics": [
          { "word": "In", "beat": 1.75 },
          { "word": "the", "beat": 2 },
          { "word": "still", "beat": 2.5, "annotation": "elongated" },
          { "word": "of", "beat": 3.25 },
          { "word": "the", "beat": 3.5 },
          { "word": "night", "beat": 4, "annotation": "falling" },
          { "word": "I", "beat": 4.75, "annotation": "whispered" },
          { "word": "can", "beat": 5.25 },
          { "word": "hear", "beat": 5.5, "annotation": "stressed" },
          { "word": "the", "beat": 6 },
          { "word": "sound", "beat": 6.5, "annotation": "punchy" }
        ]
        \`\`\`
        (Note: Beats can go above 4 to represent subsequent measures in the same lyrical phrase.)

4.  **Final JSON Output:**
    *   Provide your final output as a single JSON object matching the required schema. Ensure every \`promptName\` exists in the provided list. The 'lyrics' field for each section must be a beat map array.`;

    try {
      this.songGenerationStatus = 'Sending to the AI producer...';
      
      const contents = [
        {text: systemInstruction},
        {text: `User's song idea: "${this.songwriterPromptText}"`},
        {text: categorizedList},
      ];

      const response = await this.ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: contents,
        config: {
          responseMimeType: 'application/json',
          responseSchema: schema,
        }
      });
      
      const songPlan: SongPlan = JSON.parse(response.text.trim());
      this.generatedSongTitle = songPlan.songTitle;
      
      this.generatedVocalTracks = await this.generateVocalTracks(songPlan, this.selectedVocalist);

      if (this.playbackState === 'stopped' || this.playbackState === 'paused') {
        await this.liveMusicHelper.play();
      }
      // Brief pause to ensure the music stream is stable before recording
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      this.toggleRecording(); // Start recording the instrumental output
      
      const bpm = songPlan.bpm || 120;
      
      for (let i = 0; i < songPlan.sections.length; i++) {
        const section = songPlan.sections[i];
        this.songGenerationStatus = `Rendering: ${section.name}`;
        this.currentLyrics = section.lyrics;
        
        const vocalTrack = this.generatedVocalTracks.get(`${section.name}-${i}`);
        if (vocalTrack) {
          vocalTrack.currentTime = 0;
          vocalTrack.play();
        }

        const sectionPromptIds = new Set<string>();
        for (const p of section.prompts) {
          const promptId = this.textToIdMap.get(p.promptName);
          if (promptId) {
            sectionPromptIds.add(promptId);
            const prompt = this.allPrompts.get(promptId);
            if (prompt) {
              prompt.weight = Math.max(0, Math.min(2, p.weight || 0.5));
            }
          }
        }
        
        for (const oldId of this.activePromptIds) {
          if (!sectionPromptIds.has(oldId)) {
            const prompt = this.allPrompts.get(oldId);
            if (prompt) prompt.weight = 0;
          }
        }

        this.activePromptIds = sectionPromptIds;
        this.dispatchPromptsChanged();
        
        const durationInSeconds = (section.durationInBars * 4 * 60) / bpm;
        await new Promise(resolve => setTimeout(resolve, durationInSeconds * 1000));
      }
      this.songGenerationStatus = 'Finalizing your song...';
      this.currentLyrics = [];

    } catch (err) {
      console.error(err);
      this.dispatchEvent(new CustomEvent('error', {detail: 'Failed to generate song. The AI might be busy, or the request was too complex. Please try again.'}));
      this.songGenerationStatus = '';
    } finally {
      if (this.isRecording) {
        this.toggleRecording(); // Stop recording
      }
      
      this.isGeneratingSong = false;
      this.currentLyrics = [];
      
      this.activePromptIds.forEach(id => {
          const prompt = this.allPrompts.get(id);
          if (prompt) prompt.weight = 0;
      });
      this.activePromptIds.clear();
      this.dispatchPromptsChanged();
      await this.liveMusicHelper.pause();
    }
  }

  override render() {
    const containerClasses = classMap({ 'sidebar-open': this.isSidebarOpen });
    const hamburgerIcon = html`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2z"></path></svg>`;

    return html`
      <div class="responsive-container ${containerClasses}">
        <div id="sidebar">
          <div id="sidebar-header">
            <h2>Prompts</h2>
            <input 
              type="search" 
              id="search-input" 
              placeholder="Search assets..."
              .value=${this.searchQuery}
              @input=${this.handleSearchInput}
              ?disabled=${this.isGeneratingSong || this.isGeneratingBeat}
            />
          </div>
          <div id="asset-list">
            ${this.renderSidebar()}
          </div>
        </div>
        <div id="main-content">
          ${this.isSidebarOpen ? html`<div id="sidebar-backdrop" @click=${() => this.isSidebarOpen = false}></div>` : ''}
          <div id="toolbar">
            <div id="midi-controls">
              <button id="sidebar-toggle" @click=${() => this.isSidebarOpen = !this.isSidebarOpen} title="Toggle Prompts">${hamburgerIcon}</button>
              <button
                @click=${this.toggleShowMidi}
                class=${this.showMidi ? 'active' : ''}
                >MIDI</button
              >
              <select
                @change=${this.handleMidiInputChange}
                .value=${this.activeMidiInputId || ''}
                style=${this.showMidi ? '' : 'visibility: hidden'}>
                ${this.midiInputIds.length > 0
              ? this.midiInputIds.map(
                (id) =>
                  html`<option value=${id}>
                          ${this.midiDispatcher.getDeviceName(id)}
                        </option>`,
              )
              : html`<option value="">No devices found</option>`}
              </select>
            </div>
            <div id="transport-controls">
              <play-pause-button .playbackState=${this.isGeneratingSong ? 'loading' : this.playbackState} @click=${this.playPause}></play-pause-button>
              <div id="complexity-control">
                <label for="complexity-slider">COMPLEXITY</label>
                <input
                  id="complexity-slider"
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  .value=${this.complexity}
                  @input=${this.handleComplexityChange}
                />
              </div>
            </div>
            <div id="main-actions">
              <div id="record-controls">
                  <button 
                    @click=${this.toggleRecording}
                    class=${this.isRecording ? 'record-active' : ''}
                    title=${this.isRecording ? 'Stop Recording' : 'Record Session'}
                    ?disabled=${this.isGeneratingSong || this.isGeneratingBeat}
                    >${this.isRecording ? 'STOP' : 'REC'}
                  </button>
                  ${this.downloadUrl ? html`
                    <a id="download-btn" class="button" .href=${this.downloadUrl} .download=${this.downloadFilename}>DOWNLOAD</a>
                  ` : ''}
              </div>
            </div>
          </div>
          <div id="track-area">
            ${this.renderTrackArea()}
          </div>
          ${this.renderAudioFxWindow()}
          ${this.renderAiToolsWindow()}
        </div>
      </div>
    `;
  }
  
  private renderSidebar() {
    const lowerCaseQuery = this.searchQuery.toLowerCase();
    const availablePrompts = [...this.allPrompts.values()]
      .filter(p => !this.activePromptIds.has(p.promptId) && (p.text.toLowerCase().includes(lowerCaseQuery) || p.category.toLowerCase().includes(lowerCaseQuery)))
      .sort((a, b) => a.text.localeCompare(b.text));
      
    if (availablePrompts.length === 0 && this.searchQuery) {
      return html`<div class="empty-state">No prompts found.</div>`;
    }

    const playIcon = html`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"></path></svg>`;
    const stopIcon = html`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6 6h12v12H6z"></path></svg>`;

    return availablePrompts.map(prompt => html`
      <div class="asset-item">
        <div class="asset-info" title=${prompt.text}>
            <div class="asset-color-swatch" style="background-color: ${prompt.color}"></div>
            <span>${prompt.text}</span>
        </div>
        <div class="asset-actions">
            <button 
                class="preview-btn ${this.previewingPromptId === prompt.promptId ? 'previewing' : ''}" 
                @click=${() => this.handlePreviewToggle(prompt.promptId)} 
                title=${this.previewingPromptId === prompt.promptId ? 'Stop preview' : `Preview ${prompt.text}`}
                ?disabled=${this.isGeneratingSong || this.isGeneratingBeat}>
                ${this.previewingPromptId === prompt.promptId ? stopIcon : playIcon}
            </button>
            <button class="add-btn" @click=${() => this.addPrompt(prompt.promptId)} title="Add ${prompt.text} to tracks" ?disabled=${this.isGeneratingSong || this.isGeneratingBeat}>+</button>
        </div>
      </div>
    `);
  }
  
  private renderTrackArea() {
    if (this.isGeneratingSong) {
      return html`
        <div class="generation-view">
            <h2>${this.songGenerationStatus}</h2>
            ${this.generatedSongTitle ? html`<h3>"${this.generatedSongTitle}"</h3>` : ''}
            ${this.currentLyrics.length > 0 ? html`<pre class="lyrics-display">${this.currentLyrics.map(e => e.word).join(' ')}</pre>` : ''}
        </div>`;
    }
    const activePrompts = [...this.allPrompts.values()].filter(p => this.activePromptIds.has(p.promptId));
    
    if (activePrompts.length === 0) {
      return html`<div class="empty-state">Add prompts from the sidebar or use the AI tools<br>to create a beat or a full song.</div>`;
    }

    return activePrompts.map((prompt) => {
      return html`<prompt-controller
        .promptId=${prompt.promptId}
        ?filtered=${this.filteredPrompts.has(prompt.text)}
        .cc=${prompt.cc}
        .text=${prompt.text}
        .weight=${prompt.weight}
        .color=${prompt.color}
        .category=${prompt.category}
        .midiDispatcher=${this.midiDispatcher}
        ?showCC=${this.showMidi}
        .audioLevel=${this.audioLevel}
        @prompt-changed=${this.handlePromptChanged}
        @prompt-removed=${this.handlePromptRemoved}>
      </prompt-controller>`;
    });
  }

  private renderSongwriterContent() {
    return html`
        <div class="ai-setting">
          <label for="songwriter-prompt-input">Song Description</label>
          <textarea
              id="songwriter-prompt-input"
              class="ai-textarea"
              placeholder="e.g., 'A sad, lo-fi ballad about a robot who lost its dog, with a soft piano and rain sounds'"
              .value=${this.songwriterPromptText}
              @input=${(e: Event) => this.songwriterPromptText = (e.target as HTMLTextAreaElement).value}
              ?disabled=${this.isGeneratingSong || this.isGeneratingBeat}
          ></textarea>
        </div>

        <div class="ai-setting">
          <label for="vocalist-select">Vocalist</label>
          <select
              id="vocalist-select"
              .value=${this.selectedVocalist}
              @change=${(e: Event) => this.selectedVocalist = (e.target as HTMLSelectElement).value}
              ?disabled=${this.isGeneratingSong || this.isGeneratingBeat}>
              ${DEEPGRAM_VOICES.map(voice => html`<option value=${voice.id}>${voice.name}</option>`)}
          </select>
        </div>
        
        <button
          id="songwriter-generate-btn"
          class="ai-button"
          @click=${this.generateFullSong}
          ?disabled=${this.isGeneratingSong || this.isGeneratingBeat || !this.songwriterPromptText.trim()}
        >
          ${this.isGeneratingSong ? 'Generating...' : 'Generate Full Song'}
        </button>
    `;
  }

  private renderBeatMakerContent() {
    return html`
      <div class="ai-setting">
        <label for="beat-maker-prompt-input">Beat Description</label>
        <textarea
            id="beat-maker-prompt-input"
            class="ai-textarea"
            placeholder="e.g., 'An energetic 90s boom bap beat for studying, with a jazzy saxophone sample and a dusty drum loop.'"
            .value=${this.beatMakerPromptText}
            @input=${(e: Event) => this.beatMakerPromptText = (e.target as HTMLTextAreaElement).value}
            ?disabled=${this.isGeneratingBeat || this.isGeneratingSong}
        ></textarea>
      </div>
      
      <button
        id="beat-maker-generate-btn"
        class="ai-button"
        @click=${this.generateBeat}
        ?disabled=${this.isGeneratingBeat || this.isGeneratingSong || !this.beatMakerPromptText.trim()}
      >
        ${this.isGeneratingBeat ? 'Generating...' : 'Generate Beat'}
      </button>
    `;
  }

  private renderAiToolsWindow() {
    const windowClasses = classMap({ 
        'floating-window': true, 
        minimized: this.isAiToolWindowMinimized 
    });

    return html`
      <div id="ai-tool-window" class=${windowClasses}>
        <div class="window-header" @click=${this.toggleAiWindow}>
          <h3>AI Tools</h3>
          <button title="Toggle Window">${this.isAiToolWindowMinimized ? '□' : '—'}</button>
        </div>
        <div class="window-body">
            <div class="ai-tabs">
                <button 
                    class=${classMap({ 'ai-tab-btn': true, active: this.activeAiToolTab === 'songwriter' })}
                    @click=${() => this.activeAiToolTab = 'songwriter'}
                    ?disabled=${this.isGeneratingSong || this.isGeneratingBeat}
                >AI Songwriter</button>
                <button 
                    class=${classMap({ 'ai-tab-btn': true, active: this.activeAiToolTab === 'beat-maker' })}
                    @click=${() => this.activeAiToolTab = 'beat-maker'}
                    ?disabled=${this.isGeneratingSong || this.isGeneratingBeat}
                >AI Beat Maker</button>
            </div>
            <div class="ai-tab-content">
                ${this.activeAiToolTab === 'songwriter' ? this.renderSongwriterContent() : this.renderBeatMakerContent()}
            </div>
        </div>
      </div>
    `;
  }

  private renderAudioFxWindow() {
    const { pitch, pan, isMono, is8D, isWindowMinimized } = this.audioFxState;
    const windowClasses = classMap({ 
        'floating-window': true,
        minimized: isWindowMinimized,
        'pad-view-active': !isWindowMinimized && this.activeFxTab === 'pad'
    });
    
    return html`
        <div id="audio-fx-window" class=${windowClasses}>
            <div class="window-header" @click=${this.toggleFxWindow}>
                <h3>Audio Effects</h3>
                <button title="Toggle Window">${isWindowMinimized ? '□' : '—'}</button>
            </div>
            <div class="window-body">
                <div class="ai-tabs">
                     <button 
                        class=${classMap({ 'ai-tab-btn': true, active: this.activeFxTab === 'controls' })}
                        @click=${() => this.activeFxTab = 'controls'}
                    >Controls</button>
                    <button 
                        class=${classMap({ 'ai-tab-btn': true, active: this.activeFxTab === 'pad' })}
                        @click=${() => this.activeFxTab = 'pad'}
                    >Performance Pad</button>
                </div>

                ${this.activeFxTab === 'controls' ? html`
                    <div class="fx-content">
                        <div class="fx-setting">
                            <label for="pitch-slider">Pitch <span>${pitch.toFixed(2)}x</span></label>
                            <input
                                id="pitch-slider"
                                class="fx-slider"
                                type="range"
                                min="0.5"
                                max="2"
                                step="0.01"
                                .value=${pitch}
                                @input=${(e:Event) => this.handleFxChange('pitch', e)}
                            />
                        </div>

                        <div class="fx-setting">
                            <label for="pan-slider">Stereo Pan <span>${pan === 0 ? 'C' : (pan > 0 ? `R ${Math.round(pan*100)}` : `L ${Math.round(Math.abs(pan)*100)}`)}</span></label>
                            <input
                                id="pan-slider"
                                class="fx-slider"
                                type="range"
                                min="-1"
                                max="1"
                                step="0.01"
                                .value=${pan}
                                ?disabled=${is8D}
                                @input=${(e:Event) => this.handleFxChange('pan', e)}
                            />
                        </div>

                        <div class="fx-buttons">
                            <button 
                                @click=${() => this.handleFxToggle('isMono')}
                                class=${isMono ? 'active' : ''}
                            >Mono</button>
                            <button 
                                @click=${() => this.handleFxToggle('is8D')}
                                class=${is8D ? 'active' : ''}
                            >8D Audio</button>
                        </div>
                    </div>
                ` : html`
                    <div class="fx-pad-wrapper">
                        <fx-pad @fx-changed=${this.handlePerfFxChange}></fx-pad>
                    </div>
                `}
            </div>
        </div>
    `;
  }
}