/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import type { PlaybackState, Prompt } from '../types';
import type { AudioChunk, GoogleGenAI, LiveMusicFilteredPrompt, LiveMusicServerMessage, LiveMusicSession } from '@google/genai';
import { decode, decodeAudioData } from './audio';
import { throttle } from './throttle';
import { AudioEffects } from './AudioEffects';

export class LiveMusicHelper extends EventTarget {

  private ai: GoogleGenAI;
  private model: string;

  private session: LiveMusicSession | null = null;
  private sessionPromise: Promise<LiveMusicSession> | null = null;

  private filteredPrompts = new Set<string>();
  private nextStartTime = 0;
  private bufferTime = 2;

  public readonly audioContext: AudioContext;
  public extraDestination: AudioNode | null = null;

  private outputNode: GainNode;
  private playbackState: PlaybackState = 'stopped';

  private prompts: Map<string, Prompt>;

  // Recording properties
  public isRecording = false;
  private mediaRecorder: MediaRecorder | null = null;
  private streamDestination: MediaStreamAudioDestinationNode | null = null;
  private recordedChunks: Blob[] = [];

  // Effects properties
  private audioEffects: AudioEffects;
  public playbackRate = 1.0;

  // Reconnection properties
  private isTryingToConnectOrPlay = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(ai: GoogleGenAI, model: string) {
    super();
    this.ai = ai;
    this.model = model;
    this.prompts = new Map();
    this.audioContext = new AudioContext({ sampleRate: 48000 });
    this.outputNode = this.audioContext.createGain();

    // Create and connect effects chain
    this.audioEffects = new AudioEffects(this.audioContext);
    this.outputNode.connect(this.audioEffects.input);
  }

  private getSession(): Promise<LiveMusicSession> {
    if (!this.sessionPromise) this.sessionPromise = this.connect();
    return this.sessionPromise;
  }

  private handleDisconnection() {
    this.session = null;
    this.sessionPromise = null;
    
    if (this.isTryingToConnectOrPlay && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.setPlaybackState('loading');
      // Exponential backoff with jitter
      const delay = Math.pow(2, this.reconnectAttempts) * 1000 + (Math.random() * 1000);
      this.dispatchEvent(new CustomEvent('error', { detail: `Connection lost. Reconnecting in ${Math.round(delay/1000)}s... (Attempt ${this.reconnectAttempts + 1})` }));
      
      this.reconnectAttempts++;

      setTimeout(() => {
        // Only try to play if we are still supposed to be connected
        if (this.isTryingToConnectOrPlay) {
          this.play();
        }
      }, delay);

    } else if (this.isTryingToConnectOrPlay) {
      this.dispatchEvent(new CustomEvent('error', { detail: 'Could not reconnect to the server. Please restart playback.' }));
      this.stop();
    }
  }

  private async connect(): Promise<LiveMusicSession> {
    this.sessionPromise = this.ai.live.music.connect({
      model: this.model,
      callbacks: {
        onmessage: async (e: LiveMusicServerMessage) => {
          if (e.setupComplete) {
            // Successful connection, reset reconnect attempts
            this.reconnectAttempts = 0;
          }
          if (e.filteredPrompt) {
            this.filteredPrompts = new Set([...this.filteredPrompts, e.filteredPrompt.text!])
            this.dispatchEvent(new CustomEvent<LiveMusicFilteredPrompt>('filtered-prompt', { detail: e.filteredPrompt }));
          }
          if (e.serverContent?.audioChunks) {
            await this.processAudioChunks(e.serverContent.audioChunks);
          }
        },
        onerror: () => {
          this.handleDisconnection();
        },
        onclose: () => {
          this.handleDisconnection();
        },
      },
    });
    
    // Catch potential initial connection errors
    this.sessionPromise.catch(() => {
        if (!this.session) {
            this.handleDisconnection();
        }
    });

    return this.sessionPromise;
  }

  private setPlaybackState(state: PlaybackState) {
    this.playbackState = state;
    this.dispatchEvent(new CustomEvent('playback-state-changed', { detail: state }));
  }

  private async processAudioChunks(audioChunks: AudioChunk[]) {
    if (this.playbackState === 'paused' || this.playbackState === 'stopped') return;
    const audioBuffer = await decodeAudioData(
      decode(audioChunks[0].data!),
      this.audioContext,
      48000,
      2,
    );
    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.playbackRate.value = this.playbackRate;
    source.connect(this.outputNode);
    if (this.nextStartTime === 0) {
      this.nextStartTime = this.audioContext.currentTime + this.bufferTime;
      setTimeout(() => {
        // Only set to playing if we are still in a loading state.
        if (this.playbackState === 'loading') {
          this.setPlaybackState('playing');
        }
      }, this.bufferTime * 1000);
    }
    if (this.nextStartTime < this.audioContext.currentTime) {
      this.setPlaybackState('loading');
      this.nextStartTime = 0;
      return;
    }
    source.start(this.nextStartTime);
    this.nextStartTime += audioBuffer.duration;
  }

  public get activePrompts() {
    return Array.from(this.prompts.values())
      .filter((p) => {
        return !this.filteredPrompts.has(p.text) && p.weight > 0;
      })
  }

  public readonly setWeightedPrompts = throttle(async (prompts: Map<string, Prompt>) => {
    this.prompts = prompts;

    if (this.activePrompts.length === 0 && (this.playbackState === 'playing' || this.playbackState === 'loading')) {
      this.dispatchEvent(new CustomEvent('error', { detail: 'There needs to be one active prompt to play.' }));
      this.pause();
      return;
    }

    // store the prompts to set later if we haven't connected yet
    // there should be a user interaction before calling setWeightedPrompts
    if (!this.session) return;

    try {
      await this.session.setWeightedPrompts({
        weightedPrompts: this.activePrompts,
      });
    } catch (e: any) {
      this.dispatchEvent(new CustomEvent('error', { detail: e.message }));
      this.pause();
    }
  }, 200);

  public async play() {
    this.isTryingToConnectOrPlay = true;
    if (this.activePrompts.length === 0) {
      this.dispatchEvent(new CustomEvent('error', { detail: 'Add and enable a prompt to start playing.' }));
      this.isTryingToConnectOrPlay = false;
      return;
    }
    this.setPlaybackState('loading');
    this.session = await this.getSession();
    
    // If the play call was cancelled while we were connecting, stop here.
    if (!this.isTryingToConnectOrPlay) {
      this.stop();
      return;
    }
    
    await this.setWeightedPrompts(this.prompts);
    this.audioContext.resume();
    this.session.play();
    this.audioEffects.output.connect(this.audioContext.destination);
    if (this.extraDestination) this.audioEffects.output.connect(this.extraDestination);
    this.outputNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    this.outputNode.gain.linearRampToValueAtTime(1, this.audioContext.currentTime + 0.1);
  }

  public pause() {
    this.isTryingToConnectOrPlay = false;
    if (this.isRecording) {
      this.stopRecording();
    }
    if (this.session) this.session.pause();
    this.setPlaybackState('paused');
    this.outputNode.gain.setValueAtTime(this.outputNode.gain.value, this.audioContext.currentTime);
    this.outputNode.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + 0.1);
    this.nextStartTime = 0;
  }

  public stop() {
    this.isTryingToConnectOrPlay = false;
    if (this.isRecording) {
      this.stopRecording();
    }
    if (this.session) this.session.stop();
    this.setPlaybackState('stopped');
    this.outputNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    this.nextStartTime = 0;
    this.session = null;
    this.sessionPromise = null;
    this.reconnectAttempts = 0;
  }

  public async playPause() {
    switch (this.playbackState) {
      case 'playing':
        return this.pause();
      case 'paused':
      case 'stopped':
        this.reconnectAttempts = 0; // Reset attempts on fresh user play action
        return this.play();
      case 'loading':
        return this.stop();
    }
  }

  // --- Recording Methods ---

  public startRecording() {
    if (this.isRecording || this.playbackState !== 'playing') {
      if (this.playbackState !== 'playing') {
        this.dispatchEvent(new CustomEvent('error', { detail: 'Must be playing to record.' }));
      }
      return;
    }
    
    this.streamDestination = this.audioContext.createMediaStreamDestination();
    this.audioEffects.output.connect(this.streamDestination);
    
    const mimeType = 'audio/webm';
    this.mediaRecorder = new MediaRecorder(this.streamDestination.stream, { mimeType });

    this.recordedChunks = [];
    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.recordedChunks.push(event.data);
      }
    };
    
    const fxOutputToDisconnect = this.audioEffects.output;
    const destinationToDisconnectFrom = this.streamDestination;

    this.mediaRecorder.onstop = () => {
      const blob = new Blob(this.recordedChunks, { type: mimeType });
      const activePromptTexts = this.activePrompts.map(p => p.text);
      this.dispatchEvent(new CustomEvent('recording-finished', { detail: { blob, prompts: activePromptTexts } }));
      
      this.isRecording = false;
      this.dispatchEvent(new CustomEvent('recording-state-changed', { detail: { isRecording: this.isRecording } }));
      
      // cleanup
      fxOutputToDisconnect.disconnect(destinationToDisconnectFrom);
      this.streamDestination = null;
      this.mediaRecorder = null;
    };

    this.mediaRecorder.start();
    this.isRecording = true;
    this.dispatchEvent(new CustomEvent('recording-state-changed', { detail: { isRecording: this.isRecording } }));
  }
  
  public stopRecording() {
    if (!this.isRecording || !this.mediaRecorder) {
      return;
    }
    this.mediaRecorder.stop();
  }

  // --- Effects Methods ---
  public setPlaybackRate(rate: number) {
    this.playbackRate = rate;
  }

  public setPan(pan: number) {
    this.audioEffects.setPan(pan);
  }

  public toggle8D(enabled: boolean) {
    this.audioEffects.toggle8D(enabled);
  }

  public setMono(isMono: boolean) {
    this.audioEffects.setMono(isMono);
  }
  
  public setFilter(value: number) {
    this.audioEffects.setFilter(value);
  }

  public setDelay(value: number) {
    this.audioEffects.setDelay(value);
  }
}