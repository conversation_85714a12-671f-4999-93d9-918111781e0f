/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
export interface Prompt {
  readonly promptId: string;
  text: string;
  weight: number;
  cc: number;
  color: string;
  category: string;
}

export interface ControlChange {
  channel: number;
  cc: number;
  value: number;
}

export type PlaybackState = 'stopped' | 'playing' | 'loading' | 'paused';

export interface LyricEvent {
  word: string;
  beat: number;
  annotation?: string;
}

export interface SongSection {
  name: string;
  durationInBars: number;
  lyrics: LyricEvent[];
  prompts: {
    promptName: string;
    weight: number;
  }[];
}

export interface SongPlan {
  songTitle: string;
  bpm: number;
  sections: SongSection[];
}